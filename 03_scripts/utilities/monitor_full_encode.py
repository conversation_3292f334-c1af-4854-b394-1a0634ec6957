#!/usr/bin/env python3
"""
Monitor the full ENCODE pipeline progress.
"""

import os
import time
import pandas as pd
import subprocess


def get_process_status():
    """
    Check if the ENCODE pipeline is still running.
    """
    try:
        result = subprocess.run(['pgrep', '-f', 'encode_complete_pipeline.py'], 
                              capture_output=True, text=True)
        return len(result.stdout.strip().split('\n')) > 0 if result.stdout.strip() else False
    except:
        return False


def monitor_full_pipeline():
    """
    Monitor the full ENCODE pipeline.
    """
    print("🚀 Monitoring FULL ENCODE Pipeline (823 samples)")
    print("=" * 60)
    print("This will download and process ALL available ENCODE ATAC-seq samples")
    print("Estimated time: 6-12 hours depending on network and processing speed")
    print("Press Ctrl+C to stop monitoring (pipeline will continue running)")
    print("=" * 60)
    
    start_time = time.time()
    last_file_count = 0
    
    try:
        while True:
            current_time = time.time()
            elapsed_hours = (current_time - start_time) / 3600
            
            print(f"\n{'='*60}")
            print(f"📊 Progress Check - {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️  Elapsed time: {elapsed_hours:.1f} hours")
            print(f"{'='*60}")
            
            # Check if process is still running
            is_running = get_process_status()
            print(f"🔄 Pipeline status: {'RUNNING' if is_running else 'STOPPED/COMPLETED'}")
            
            # Check log file
            if os.path.exists("encode_full_pipeline.log"):
                with open("encode_full_pipeline.log", "r") as f:
                    lines = f.readlines()
                
                print(f"📝 Log file: {len(lines)} lines")
                
                # Show recent progress
                if lines:
                    print("\n📋 Recent log entries:")
                    for line in lines[-5:]:
                        if line.strip():
                            print(f"   {line.strip()}")
            else:
                print("📝 Log file not found yet...")
            
            # Check downloads
            download_count = 0
            if os.path.exists("encode_downloads"):
                downloaded_files = [f for f in os.listdir("encode_downloads") if f.endswith(".bed")]
                download_count = len(downloaded_files)
                print(f"⬇️  Downloaded files: {download_count}")
                
                # Calculate download rate
                if download_count > last_file_count:
                    rate = (download_count - last_file_count) / 5  # files per minute (5 min intervals)
                    eta_minutes = (823 - download_count) / rate if rate > 0 else 0
                    print(f"📈 Download rate: {rate:.1f} files/min")
                    if eta_minutes > 0:
                        print(f"⏰ ETA for downloads: {eta_minutes:.0f} minutes")
                
                last_file_count = download_count
            else:
                print("⬇️  Downloads directory not created yet...")
            
            # Check mapping file
            if os.path.exists("encode_file_mapping.csv"):
                mapping_df = pd.read_csv("encode_file_mapping.csv")
                total_files = len(mapping_df)
                print(f"📋 Total files to process: {total_files}")
                
                if download_count > 0:
                    progress_pct = (download_count / total_files) * 100
                    print(f"📊 Download progress: {progress_pct:.1f}%")
                
                # Show biosample distribution
                if 'biosample_term_name' in mapping_df.columns and len(mapping_df) > 50:
                    biosample_counts = mapping_df['biosample_term_name'].value_counts()
                    print(f"\n🧬 Top cell types (of {total_files} total):")
                    for i, (biosample, count) in enumerate(biosample_counts.head(8).items()):
                        print(f"   {i+1}. {biosample}: {count} samples")
            
            # Check output files
            output_files = [f for f in os.listdir(".") if f.startswith("ENCODE_all_samples_combined")]
            if output_files:
                print(f"\n📁 Output files:")
                for output_file in output_files:
                    size_mb = os.path.getsize(output_file) / (1024**2)
                    print(f"   {output_file}: {size_mb:.1f} MB")
            
            # Check for completion
            if not is_running and os.path.exists("ENCODE_all_samples_combined_10kb.tsv"):
                print(f"\n🎉 PIPELINE COMPLETED!")
                
                # Final statistics
                final_df = pd.read_csv("ENCODE_all_samples_combined_10kb.tsv", sep='\t', nrows=1)
                sample_count = len(final_df.columns) - 3  # exclude chr, start, end
                
                print(f"✅ Final matrix: {sample_count} samples")
                print(f"⏱️  Total time: {elapsed_hours:.1f} hours")
                
                # Suggest next steps
                print(f"\n🚀 Next steps:")
                print(f"   1. Combine with TCGA: python combine_tcga_encode_matrices.py --create-annotations")
                print(f"   2. Final matrix will have: 404 TCGA + {sample_count} ENCODE = {404 + sample_count} total samples")
                
                break
            
            if not is_running:
                print(f"\n⚠️  Pipeline stopped but no output file found. Check logs for errors.")
                break
            
            print(f"\n⏳ Next check in 5 minutes...")
            time.sleep(300)  # Check every 5 minutes
            
    except KeyboardInterrupt:
        print(f"\n\n👋 Monitoring stopped. Pipeline continues running in background.")
        print(f"📝 Check logs: tail -f encode_full_pipeline.log")
        print(f"🔍 Check progress: python monitor_full_encode.py")


def show_quick_status():
    """
    Show a quick status without continuous monitoring.
    """
    print("📊 Quick Status Check")
    print("=" * 40)
    
    # Process status
    is_running = get_process_status()
    print(f"Pipeline: {'RUNNING' if is_running else 'STOPPED'}")
    
    # Download count
    if os.path.exists("encode_downloads"):
        download_count = len([f for f in os.listdir("encode_downloads") if f.endswith(".bed")])
        print(f"Downloads: {download_count} files")
    
    # Mapping info
    if os.path.exists("encode_file_mapping.csv"):
        mapping_df = pd.read_csv("encode_file_mapping.csv")
        total = len(mapping_df)
        print(f"Total files: {total}")
        if download_count > 0:
            print(f"Progress: {(download_count/total)*100:.1f}%")
    
    # Output files
    output_files = [f for f in os.listdir(".") if f.startswith("ENCODE_all_samples_combined")]
    if output_files:
        print(f"Output: {output_files}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        show_quick_status()
    else:
        monitor_full_pipeline()
