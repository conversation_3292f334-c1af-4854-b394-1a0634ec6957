#!/bin/bash
# ENCODE Human IDR narrowPeak download script
# Generated for 50 files
# Usage: bash encode_human_idr_download.sh

mkdir -p encode_downloads
cd encode_downloads

echo 'Downloading ENCFF926KTI (K562)...'
wget -O ENCFF926KTI.bed.gz 'https://www.encodeproject.org/files/ENCFF926KTI/@@download/ENCFF926KTI.bed.gz'

echo 'Downloading ENCFF046GBZ (K562)...'
wget -O ENCFF046GBZ.bed.gz 'https://www.encodeproject.org/files/ENCFF046GBZ/@@download/ENCFF046GBZ.bed.gz'

echo 'Downloading ENCFF202HWV (K562)...'
wget -O ENCFF202HWV.bed.gz 'https://www.encodeproject.org/files/ENCFF202HWV/@@download/ENCFF202HWV.bed.gz'

echo 'Downloading ENCFF489GQF (K562)...'
wget -O ENCFF489GQF.bed.gz 'https://www.encodeproject.org/files/ENCFF489GQF/@@download/ENCFF489GQF.bed.gz'

echo 'Downloading ENCFF540FNK (K562)...'
wget -O ENCFF540FNK.bed.gz 'https://www.encodeproject.org/files/ENCFF540FNK/@@download/ENCFF540FNK.bed.gz'

echo 'Downloading ENCFF738NOA (K562)...'
wget -O ENCFF738NOA.bed.gz 'https://www.encodeproject.org/files/ENCFF738NOA/@@download/ENCFF738NOA.bed.gz'

echo 'Downloading ENCFF855OHJ (stimulated activated naive CD8-positive, alpha-beta T cell)...'
wget -O ENCFF855OHJ.bed.gz 'https://www.encodeproject.org/files/ENCFF855OHJ/@@download/ENCFF855OHJ.bed.gz'

echo 'Downloading ENCFF753NRZ (stimulated activated naive CD8-positive, alpha-beta T cell)...'
wget -O ENCFF753NRZ.bed.gz 'https://www.encodeproject.org/files/ENCFF753NRZ/@@download/ENCFF753NRZ.bed.gz'

echo 'Downloading ENCFF767EUF (stimulated activated naive CD8-positive, alpha-beta T cell)...'
wget -O ENCFF767EUF.bed.gz 'https://www.encodeproject.org/files/ENCFF767EUF/@@download/ENCFF767EUF.bed.gz'

echo 'Downloading ENCFF767YBU (activated T-cell)...'
wget -O ENCFF767YBU.bed.gz 'https://www.encodeproject.org/files/ENCFF767YBU/@@download/ENCFF767YBU.bed.gz'

echo 'Downloading ENCFF847WTI (activated T-cell)...'
wget -O ENCFF847WTI.bed.gz 'https://www.encodeproject.org/files/ENCFF847WTI/@@download/ENCFF847WTI.bed.gz'

echo 'Downloading ENCFF564OCG (activated T-cell)...'
wget -O ENCFF564OCG.bed.gz 'https://www.encodeproject.org/files/ENCFF564OCG/@@download/ENCFF564OCG.bed.gz'

echo 'Downloading ENCFF999QSA (T-helper 17 cell)...'
wget -O ENCFF999QSA.bed.gz 'https://www.encodeproject.org/files/ENCFF999QSA/@@download/ENCFF999QSA.bed.gz'

echo 'Downloading ENCFF870WGS (stimulated activated naive CD8-positive, alpha-beta T cell)...'
wget -O ENCFF870WGS.bed.gz 'https://www.encodeproject.org/files/ENCFF870WGS/@@download/ENCFF870WGS.bed.gz'

echo 'Downloading ENCFF865ZYY (stimulated activated naive CD8-positive, alpha-beta T cell)...'
wget -O ENCFF865ZYY.bed.gz 'https://www.encodeproject.org/files/ENCFF865ZYY/@@download/ENCFF865ZYY.bed.gz'

echo 'Downloading ENCFF701COP (stimulated activated naive CD8-positive, alpha-beta T cell)...'
wget -O ENCFF701COP.bed.gz 'https://www.encodeproject.org/files/ENCFF701COP/@@download/ENCFF701COP.bed.gz'

echo 'Downloading ENCFF273AWR (naive thymus-derived CD8-positive, alpha-beta T cell)...'
wget -O ENCFF273AWR.bed.gz 'https://www.encodeproject.org/files/ENCFF273AWR/@@download/ENCFF273AWR.bed.gz'

echo 'Downloading ENCFF038ONH (naive thymus-derived CD8-positive, alpha-beta T cell)...'
wget -O ENCFF038ONH.bed.gz 'https://www.encodeproject.org/files/ENCFF038ONH/@@download/ENCFF038ONH.bed.gz'

echo 'Downloading ENCFF246EKP (naive thymus-derived CD8-positive, alpha-beta T cell)...'
wget -O ENCFF246EKP.bed.gz 'https://www.encodeproject.org/files/ENCFF246EKP/@@download/ENCFF246EKP.bed.gz'

echo 'Downloading ENCFF514SFC (T-helper 17 cell)...'
wget -O ENCFF514SFC.bed.gz 'https://www.encodeproject.org/files/ENCFF514SFC/@@download/ENCFF514SFC.bed.gz'

echo 'Downloading ENCFF679WGA (T-helper 17 cell)...'
wget -O ENCFF679WGA.bed.gz 'https://www.encodeproject.org/files/ENCFF679WGA/@@download/ENCFF679WGA.bed.gz'

echo 'Downloading ENCFF980FDH (T-helper 17 cell)...'
wget -O ENCFF980FDH.bed.gz 'https://www.encodeproject.org/files/ENCFF980FDH/@@download/ENCFF980FDH.bed.gz'

echo 'Downloading ENCFF242BPO (naive thymus-derived CD8-positive, alpha-beta T cell)...'
wget -O ENCFF242BPO.bed.gz 'https://www.encodeproject.org/files/ENCFF242BPO/@@download/ENCFF242BPO.bed.gz'

echo 'Downloading ENCFF284FAV (CD4-positive, CD25-positive, alpha-beta regulatory T cell)...'
wget -O ENCFF284FAV.bed.gz 'https://www.encodeproject.org/files/ENCFF284FAV/@@download/ENCFF284FAV.bed.gz'

echo 'Downloading ENCFF852LGZ (activated naive CD8-positive, alpha-beta T cell)...'
wget -O ENCFF852LGZ.bed.gz 'https://www.encodeproject.org/files/ENCFF852LGZ/@@download/ENCFF852LGZ.bed.gz'

echo 'Downloading ENCFF788CFY (naive thymus-derived CD4-positive, alpha-beta T cell)...'
wget -O ENCFF788CFY.bed.gz 'https://www.encodeproject.org/files/ENCFF788CFY/@@download/ENCFF788CFY.bed.gz'

echo 'Downloading ENCFF400WXD (naive thymus-derived CD4-positive, alpha-beta T cell)...'
wget -O ENCFF400WXD.bed.gz 'https://www.encodeproject.org/files/ENCFF400WXD/@@download/ENCFF400WXD.bed.gz'

echo 'Downloading ENCFF759ZEZ (naive thymus-derived CD4-positive, alpha-beta T cell)...'
wget -O ENCFF759ZEZ.bed.gz 'https://www.encodeproject.org/files/ENCFF759ZEZ/@@download/ENCFF759ZEZ.bed.gz'

echo 'Downloading ENCFF717CDB (activated naive CD4-positive, alpha-beta T cell)...'
wget -O ENCFF717CDB.bed.gz 'https://www.encodeproject.org/files/ENCFF717CDB/@@download/ENCFF717CDB.bed.gz'

echo 'Downloading ENCFF961DWF (activated naive CD4-positive, alpha-beta T cell)...'
wget -O ENCFF961DWF.bed.gz 'https://www.encodeproject.org/files/ENCFF961DWF/@@download/ENCFF961DWF.bed.gz'

echo 'Downloading ENCFF272RDL (activated naive CD4-positive, alpha-beta T cell)...'
wget -O ENCFF272RDL.bed.gz 'https://www.encodeproject.org/files/ENCFF272RDL/@@download/ENCFF272RDL.bed.gz'

echo 'Downloading ENCFF331XQA (stimulated activated effector memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF331XQA.bed.gz 'https://www.encodeproject.org/files/ENCFF331XQA/@@download/ENCFF331XQA.bed.gz'

echo 'Downloading ENCFF816USW (stimulated activated effector memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF816USW.bed.gz 'https://www.encodeproject.org/files/ENCFF816USW/@@download/ENCFF816USW.bed.gz'

echo 'Downloading ENCFF276FVU (stimulated activated effector memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF276FVU.bed.gz 'https://www.encodeproject.org/files/ENCFF276FVU/@@download/ENCFF276FVU.bed.gz'

echo 'Downloading ENCFF081RHG (stimulated activated naive CD8-positive, alpha-beta T cell)...'
wget -O ENCFF081RHG.bed.gz 'https://www.encodeproject.org/files/ENCFF081RHG/@@download/ENCFF081RHG.bed.gz'

echo 'Downloading ENCFF939PQC (activated CD4-positive, CD25-positive, alpha-beta regulatory T cell)...'
wget -O ENCFF939PQC.bed.gz 'https://www.encodeproject.org/files/ENCFF939PQC/@@download/ENCFF939PQC.bed.gz'

echo 'Downloading ENCFF518VXK (T-helper 17 cell)...'
wget -O ENCFF518VXK.bed.gz 'https://www.encodeproject.org/files/ENCFF518VXK/@@download/ENCFF518VXK.bed.gz'

echo 'Downloading ENCFF907HEI (activated naive CD4-positive, alpha-beta T cell)...'
wget -O ENCFF907HEI.bed.gz 'https://www.encodeproject.org/files/ENCFF907HEI/@@download/ENCFF907HEI.bed.gz'

echo 'Downloading ENCFF338BCO (activated naive CD4-positive, alpha-beta T cell)...'
wget -O ENCFF338BCO.bed.gz 'https://www.encodeproject.org/files/ENCFF338BCO/@@download/ENCFF338BCO.bed.gz'

echo 'Downloading ENCFF029EIQ (activated naive CD4-positive, alpha-beta T cell)...'
wget -O ENCFF029EIQ.bed.gz 'https://www.encodeproject.org/files/ENCFF029EIQ/@@download/ENCFF029EIQ.bed.gz'

echo 'Downloading ENCFF270DPY (central memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF270DPY.bed.gz 'https://www.encodeproject.org/files/ENCFF270DPY/@@download/ENCFF270DPY.bed.gz'

echo 'Downloading ENCFF016CIJ (central memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF016CIJ.bed.gz 'https://www.encodeproject.org/files/ENCFF016CIJ/@@download/ENCFF016CIJ.bed.gz'

echo 'Downloading ENCFF441BUL (central memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF441BUL.bed.gz 'https://www.encodeproject.org/files/ENCFF441BUL/@@download/ENCFF441BUL.bed.gz'

echo 'Downloading ENCFF519QMA (CD4-positive, CD25-positive, alpha-beta regulatory T cell)...'
wget -O ENCFF519QMA.bed.gz 'https://www.encodeproject.org/files/ENCFF519QMA/@@download/ENCFF519QMA.bed.gz'

echo 'Downloading ENCFF846LCD (activated T-helper 17 cell)...'
wget -O ENCFF846LCD.bed.gz 'https://www.encodeproject.org/files/ENCFF846LCD/@@download/ENCFF846LCD.bed.gz'

echo 'Downloading ENCFF168ZRB (activated T-helper 17 cell)...'
wget -O ENCFF168ZRB.bed.gz 'https://www.encodeproject.org/files/ENCFF168ZRB/@@download/ENCFF168ZRB.bed.gz'

echo 'Downloading ENCFF217QDP (activated T-helper 17 cell)...'
wget -O ENCFF217QDP.bed.gz 'https://www.encodeproject.org/files/ENCFF217QDP/@@download/ENCFF217QDP.bed.gz'

echo 'Downloading ENCFF603GBI (effector memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF603GBI.bed.gz 'https://www.encodeproject.org/files/ENCFF603GBI/@@download/ENCFF603GBI.bed.gz'

echo 'Downloading ENCFF205RXD (effector memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF205RXD.bed.gz 'https://www.encodeproject.org/files/ENCFF205RXD/@@download/ENCFF205RXD.bed.gz'

echo 'Downloading ENCFF589DMP (effector memory CD8-positive, alpha-beta T cell)...'
wget -O ENCFF589DMP.bed.gz 'https://www.encodeproject.org/files/ENCFF589DMP/@@download/ENCFF589DMP.bed.gz'

