#!/usr/bin/env python3
"""
Simple ENCODE query for narrowPeak IDR files.
"""

import requests
import json
import pandas as pd


def simple_encode_query():
    """
    Simple query for ENCODE narrowPeak IDR files.
    """
    print("Querying ENCODE for narrowPeak IDR files...")
    
    # Simplified API query
    url = "https://www.encodeproject.org/search/"
    params = {
        "type": "File",
        "file_format": "bed",
        "output_type": "IDR thresholded peaks",
        "status": "released",
        "limit": 100,
        "format": "json"
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"Request URL: {response.url}")
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            files = data.get("@graph", [])
            print(f"Found {len(files)} files")
            
            # Extract basic info
            file_list = []
            for file_info in files:
                file_list.append({
                    "accession": file_info.get("accession", ""),
                    "href": file_info.get("href", ""),
                    "file_size": file_info.get("file_size", 0),
                    "assembly": file_info.get("assembly", ""),
                    "download_url": f"https://www.encodeproject.org{file_info.get('href', '')}"
                })
            
            # Save to CSV
            df = pd.DataFrame(file_list)
            df.to_csv("encode_narrowpeak_idr_files.csv", index=False)
            
            # Create download script
            with open("download_encode_files.sh", "w") as f:
                f.write("#!/bin/bash\n")
                f.write("# Download ENCODE narrowPeak IDR files\n\n")
                for _, row in df.iterrows():
                    download_url = f"{row['download_url']}@@download/{row['accession']}.bed.gz"
                    f.write(f"wget -O {row['accession']}.bed.gz '{download_url}'\n")
            
            print(f"Saved file list to: encode_narrowpeak_idr_files.csv")
            print(f"Saved download script to: download_encode_files.sh")
            print(f"Make executable with: chmod +x download_encode_files.sh")
            
            return df
            
        else:
            print(f"Error: HTTP {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        return None


if __name__ == "__main__":
    simple_encode_query()
