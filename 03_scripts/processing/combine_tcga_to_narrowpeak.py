#!/usr/bin/env python3
"""
Combine TCGA ATAC-seq peak coordinates and signal data into narrowPeak format.
"""

import pandas as pd
import numpy as np
import argparse
import os


def combine_tcga_files(coords_file, signals_file, output_prefix="TCGA_ATAC",
                      signal_threshold=None, sample_subset=None, max_samples=None):
    """
    Combine TCGA peak coordinates with signal data to create narrowPeak files.
    
    Args:
        coords_file: Path to TCGA_ATAC_peak.all.txt
        signals_file: Path to TCGA_ATAC_peak_Log2Counts_dedup_sample.tsv
        output_prefix: Prefix for output files
        signal_threshold: Minimum signal threshold (auto if None)
        sample_subset: List of specific samples to process (all if None)
    """
    print("Loading peak coordinates...")
    coords_df = pd.read_csv(coords_file, sep='\t')
    print(f"Loaded {len(coords_df)} peak regions")
    
    print("Loading signal data...")
    signals_df = pd.read_csv(signals_file, sep='\t', index_col=0)
    print(f"Loaded signal data for {len(signals_df)} peaks and {len(signals_df.columns)} samples")
    
    # Verify that the files match
    if len(coords_df) != len(signals_df):
        raise ValueError(f"Mismatch: {len(coords_df)} coordinates vs {len(signals_df)} signal rows")
    
    # Get sample names
    sample_names = signals_df.columns.tolist()
    
    if sample_subset:
        # Filter to specific samples
        available_samples = [s for s in sample_subset if s in sample_names]
        if not available_samples:
            raise ValueError(f"None of the requested samples found in data")
        sample_names = available_samples
        signals_df = signals_df[sample_names]
        print(f"Filtered to {len(sample_names)} requested samples")
    elif max_samples and len(sample_names) > max_samples:
        # Limit to max_samples if specified
        sample_names = sample_names[:max_samples]
        signals_df = signals_df[sample_names]
        print(f"Limited to first {len(sample_names)} samples")
    
    print(f"Processing {len(sample_names)} samples...")
    
    # Auto-determine threshold if not provided
    if signal_threshold is None:
        # Use median + 1*MAD as threshold
        all_signals = signals_df.values.flatten()
        median_signal = np.median(all_signals)
        mad = np.median(np.abs(all_signals - median_signal))
        signal_threshold = median_signal + 1 * mad
        print(f"Auto-determined signal threshold: {signal_threshold:.3f}")
    
    # Create base narrowPeak data from coordinates
    base_narrowpeak = pd.DataFrame({
        'chr': coords_df['chrom'],
        'start': coords_df['chromStart'],
        'end': coords_df['chromEnd'],
        'name': coords_df['id'],
        'score': 0,  # Will be updated based on signal
        'strand': coords_df['strand'],
        'signalValue': 0.0,  # Will be updated
        'pValue': -1,  # Not available
        'qValue': -1,  # Not available
        'peak': -1   # Will be calculated as center
    })
    
    # Calculate peak center (summit)
    base_narrowpeak['peak'] = (base_narrowpeak['end'] - base_narrowpeak['start']) // 2
    
    # Process each sample
    output_files = []
    
    for i, sample in enumerate(sample_names):
        print(f"Processing sample {i+1}/{len(sample_names)}: {sample}")
        
        # Get signal values for this sample
        sample_signals = signals_df[sample].values
        
        # Filter peaks above threshold
        above_threshold = sample_signals >= signal_threshold
        
        if not np.any(above_threshold):
            print(f"  No peaks above threshold for {sample}")
            continue
        
        # Create narrowPeak for this sample
        sample_narrowpeak = base_narrowpeak[above_threshold].copy()
        sample_narrowpeak['signalValue'] = sample_signals[above_threshold]
        
        # Update score (scale signal to 0-1000 range)
        max_signal = sample_narrowpeak['signalValue'].max()
        min_signal = sample_narrowpeak['signalValue'].min()
        if max_signal > min_signal:
            normalized_scores = (sample_narrowpeak['signalValue'] - min_signal) / (max_signal - min_signal)
            sample_narrowpeak['score'] = (normalized_scores * 1000).astype(int)
        else:
            sample_narrowpeak['score'] = 500  # Default middle value
        
        # Generate output filename
        output_file = f"{output_prefix}_{sample}.narrowPeak"
        
        # Save narrowPeak file (tab-separated, no header)
        sample_narrowpeak.to_csv(output_file, sep='\t', header=False, index=False,
                                columns=['chr', 'start', 'end', 'name', 'score', 'strand', 
                                        'signalValue', 'pValue', 'qValue', 'peak'])
        
        output_files.append(output_file)
        print(f"  Saved {len(sample_narrowpeak)} peaks to {output_file}")
    
    print(f"\nGenerated {len(output_files)} narrowPeak files")
    return output_files


def create_consensus_narrowpeak(coords_file, signals_file, output_file="TCGA_ATAC_consensus.narrowPeak",
                               min_samples=5, signal_threshold=None):
    """
    Create a consensus narrowPeak file with peaks present in multiple samples.
    
    Args:
        coords_file: Path to TCGA_ATAC_peak.all.txt
        signals_file: Path to TCGA_ATAC_peak_Log2Counts_dedup_sample.tsv
        output_file: Output consensus narrowPeak file
        min_samples: Minimum number of samples a peak must be present in
        signal_threshold: Minimum signal threshold (auto if None)
    """
    print("Creating consensus narrowPeak file...")
    
    print("Loading peak coordinates...")
    coords_df = pd.read_csv(coords_file, sep='\t')
    
    print("Loading signal data...")
    signals_df = pd.read_csv(signals_file, sep='\t', index_col=0)
    
    # Auto-determine threshold if not provided
    if signal_threshold is None:
        all_signals = signals_df.values.flatten()
        median_signal = np.median(all_signals)
        mad = np.median(np.abs(all_signals - median_signal))
        signal_threshold = median_signal + 1 * mad
        print(f"Auto-determined signal threshold: {signal_threshold:.3f}")
    
    # Count how many samples each peak is active in
    above_threshold = signals_df >= signal_threshold
    peak_counts = above_threshold.sum(axis=1)
    
    # Filter peaks present in at least min_samples
    consensus_peaks = peak_counts >= min_samples
    
    print(f"Found {consensus_peaks.sum()} peaks present in >= {min_samples} samples")
    
    if not consensus_peaks.any():
        print("No consensus peaks found!")
        return None
    
    # Create consensus narrowPeak
    consensus_coords = coords_df[consensus_peaks].copy()
    consensus_signals = signals_df[consensus_peaks]
    
    # Calculate mean signal across samples for each peak
    mean_signals = consensus_signals.mean(axis=1)
    
    consensus_narrowpeak = pd.DataFrame({
        'chr': consensus_coords['chrom'],
        'start': consensus_coords['chromStart'],
        'end': consensus_coords['chromEnd'],
        'name': consensus_coords['id'],
        'score': 0,  # Will be updated
        'strand': consensus_coords['strand'],
        'signalValue': mean_signals.values,
        'pValue': -1,
        'qValue': -1,
        'peak': (consensus_coords['chromEnd'] - consensus_coords['chromStart']) // 2
    })
    
    # Update score based on mean signal
    max_signal = consensus_narrowpeak['signalValue'].max()
    min_signal = consensus_narrowpeak['signalValue'].min()
    if max_signal > min_signal:
        normalized_scores = (consensus_narrowpeak['signalValue'] - min_signal) / (max_signal - min_signal)
        consensus_narrowpeak['score'] = (normalized_scores * 1000).astype(int)
    else:
        consensus_narrowpeak['score'] = 500
    
    # Save consensus narrowPeak
    consensus_narrowpeak.to_csv(output_file, sep='\t', header=False, index=False,
                               columns=['chr', 'start', 'end', 'name', 'score', 'strand', 
                                       'signalValue', 'pValue', 'qValue', 'peak'])
    
    print(f"Saved consensus narrowPeak with {len(consensus_narrowpeak)} peaks to {output_file}")
    
    # Print summary statistics
    print(f"\nConsensus narrowPeak statistics:")
    print(f"  Mean signal: {consensus_narrowpeak['signalValue'].mean():.3f}")
    print(f"  Max signal: {consensus_narrowpeak['signalValue'].max():.3f}")
    print(f"  Mean peak length: {(consensus_narrowpeak['end'] - consensus_narrowpeak['start']).mean():.1f} bp")
    
    return output_file


def main():
    parser = argparse.ArgumentParser(description='Combine TCGA ATAC-seq files into narrowPeak format')
    parser.add_argument('coords_file', help='TCGA_ATAC_peak.all.txt file')
    parser.add_argument('signals_file', help='TCGA_ATAC_peak_Log2Counts_dedup_sample.tsv file')
    parser.add_argument('--output-prefix', default='TCGA_ATAC', help='Output file prefix')
    parser.add_argument('--threshold', type=float, help='Signal threshold (auto if not specified)')
    parser.add_argument('--samples', nargs='+', help='Specific samples to process (all if not specified)')
    parser.add_argument('--consensus-only', action='store_true', help='Only create consensus narrowPeak')
    parser.add_argument('--min-samples', type=int, default=5, help='Min samples for consensus peaks')
    parser.add_argument('--max-samples', type=int, default=None, help='Max individual samples to process (all if not specified)')
    parser.add_argument('--all-samples', action='store_true', help='Process all 404 samples')
    
    args = parser.parse_args()
    
    # Check if input files exist
    if not os.path.exists(args.coords_file):
        print(f"Error: {args.coords_file} not found")
        return
    
    if not os.path.exists(args.signals_file):
        print(f"Error: {args.signals_file} not found")
        return
    
    try:
        if args.consensus_only:
            # Only create consensus file
            create_consensus_narrowpeak(args.coords_file, args.signals_file, 
                                       f"{args.output_prefix}_consensus.narrowPeak",
                                       args.min_samples, args.threshold)
        else:
            # Determine which samples to process
            samples_to_process = args.samples

            if args.all_samples:
                print("Processing ALL 404 samples...")
                samples_to_process = None  # Process all samples
            elif not args.samples and args.max_samples:
                print(f"Processing first {args.max_samples} samples...")
                # Will be limited in combine_tcga_files function

            # Create individual sample files
            output_files = combine_tcga_files(args.coords_file, args.signals_file,
                                            args.output_prefix, args.threshold,
                                            samples_to_process, args.max_samples if not args.all_samples else None)

            print(f"Generated {len(output_files)} narrowPeak files")

            # Also create consensus file
            create_consensus_narrowpeak(args.coords_file, args.signals_file,
                                       f"{args.output_prefix}_consensus.narrowPeak",
                                       args.min_samples, args.threshold)
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
