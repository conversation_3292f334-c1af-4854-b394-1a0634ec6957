#!/usr/bin/env python3
"""
Convert BigWig files to narrowPeak format by calling peaks from signal data.
Requires pyBigWig and scipy for signal processing.
"""

import pyBigWig
import pandas as pd
import numpy as np
import argparse
import os


def find_peaks_from_bigwig(bw_file, output_file, threshold=None, min_length=50, max_gap=100):
    """
    Convert BigWig to narrowPeak by finding peaks in the signal.
    
    Args:
        bw_file: Path to BigWig file
        output_file: Output narrowPeak file path
        threshold: Signal threshold for peak calling (auto if None)
        min_length: Minimum peak length in bp
        max_gap: Maximum gap to merge nearby peaks
    """
    print(f"Opening BigWig file: {bw_file}")
    
    # Open BigWig file
    bw = pyBigWig.open(bw_file)
    
    # Get chromosome information
    chroms = bw.chroms()
    print(f"Found {len(chroms)} chromosomes")
    
    all_peaks = []
    peak_id = 1
    
    for chrom, length in chroms.items():
        print(f"Processing {chrom} (length: {length:,} bp)...")
        
        # Skip very small chromosomes or random/unknown sequences
        if length < 1000000 or 'random' in chrom.lower() or 'un_' in chrom.lower():
            print(f"  Skipping {chrom} (too small or random sequence)")
            continue
        
        try:
            # Get signal values for entire chromosome
            # Process in chunks to avoid memory issues
            chunk_size = 1000000  # 1MB chunks
            
            for start in range(0, length, chunk_size):
                end = min(start + chunk_size, length)
                
                # Get signal values
                values = bw.values(chrom, start, end)
                
                # Skip if no data
                if values is None or len(values) == 0:
                    continue
                
                # Convert to numpy array and handle NaN values
                values = np.array(values, dtype=float)
                values = np.nan_to_num(values, nan=0.0)
                
                # Auto-determine threshold if not provided
                if threshold is None:
                    # Use mean + 2*std as threshold
                    chunk_threshold = np.mean(values) + 2 * np.std(values)
                    if chunk_threshold <= 0:
                        chunk_threshold = np.percentile(values[values > 0], 95) if np.any(values > 0) else 1.0
                else:
                    chunk_threshold = threshold
                
                # Find regions above threshold
                above_threshold = values >= chunk_threshold
                
                if not np.any(above_threshold):
                    continue
                
                # Find continuous regions
                diff = np.diff(np.concatenate(([False], above_threshold, [False])).astype(int))
                starts = np.where(diff == 1)[0]
                ends = np.where(diff == -1)[0]
                
                # Process each peak region
                for peak_start, peak_end in zip(starts, ends):
                    peak_length = peak_end - peak_start
                    
                    # Filter by minimum length
                    if peak_length < min_length:
                        continue
                    
                    # Calculate peak statistics
                    peak_values = values[peak_start:peak_end]
                    peak_max = np.max(peak_values)
                    peak_mean = np.mean(peak_values)
                    
                    # Find summit (position of maximum signal)
                    summit_rel = np.argmax(peak_values)
                    
                    # Convert to genomic coordinates
                    genomic_start = start + peak_start
                    genomic_end = start + peak_end
                    summit_pos = summit_rel
                    
                    # Create narrowPeak entry
                    # Format: chr, start, end, name, score, strand, signalValue, pValue, qValue, peak
                    peak_entry = {
                        'chr': chrom,
                        'start': genomic_start,
                        'end': genomic_end,
                        'name': f"peak_{peak_id}",
                        'score': min(int(peak_max * 10), 1000),  # Scale and cap at 1000
                        'strand': '.',
                        'signalValue': peak_max,
                        'pValue': -1,  # Not calculated
                        'qValue': -1,  # Not calculated
                        'peak': summit_pos
                    }
                    
                    all_peaks.append(peak_entry)
                    peak_id += 1
                
        except Exception as e:
            print(f"  Error processing {chrom}: {e}")
            continue
    
    bw.close()
    
    # Convert to DataFrame and save
    if all_peaks:
        peaks_df = pd.DataFrame(all_peaks)
        
        # Sort by chromosome and position
        peaks_df = peaks_df.sort_values(['chr', 'start'])
        
        # Save as narrowPeak format (tab-separated, no header)
        peaks_df.to_csv(output_file, sep='\t', header=False, index=False,
                        columns=['chr', 'start', 'end', 'name', 'score', 'strand', 
                                'signalValue', 'pValue', 'qValue', 'peak'])
        
        print(f"\nSaved {len(peaks_df)} peaks to {output_file}")
        
        # Print summary statistics
        print(f"Peak statistics:")
        print(f"  Mean signal: {peaks_df['signalValue'].mean():.3f}")
        print(f"  Max signal: {peaks_df['signalValue'].max():.3f}")
        print(f"  Mean length: {(peaks_df['end'] - peaks_df['start']).mean():.1f} bp")
        
    else:
        print("No peaks found!")


def main():
    parser = argparse.ArgumentParser(description='Convert BigWig to narrowPeak format')
    parser.add_argument('input', help='Input BigWig file')
    parser.add_argument('-o', '--output', help='Output narrowPeak file (default: input.narrowPeak)')
    parser.add_argument('-t', '--threshold', type=float, help='Signal threshold for peak calling (auto if not specified)')
    parser.add_argument('--min-length', type=int, default=50, help='Minimum peak length in bp (default: 50)')
    parser.add_argument('--max-gap', type=int, default=100, help='Maximum gap to merge nearby peaks (default: 100)')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file {args.input} not found")
        return
    
    # Generate output filename if not provided
    if args.output is None:
        base_name = os.path.splitext(args.input)[0]
        args.output = f"{base_name}.narrowPeak"
    
    print(f"Converting {args.input} to {args.output}")
    print(f"Parameters:")
    print(f"  Threshold: {'auto' if args.threshold is None else args.threshold}")
    print(f"  Min length: {args.min_length} bp")
    print(f"  Max gap: {args.max_gap} bp")
    
    try:
        find_peaks_from_bigwig(args.input, args.output, args.threshold, args.min_length, args.max_gap)
    except ImportError as e:
        print(f"Error: Missing required package. Please install with:")
        print(f"  pip install pyBigWig scipy")
        print(f"Original error: {e}")
    except Exception as e:
        print(f"Error during conversion: {e}")


if __name__ == "__main__":
    main()
