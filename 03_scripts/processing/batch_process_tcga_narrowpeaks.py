#!/usr/bin/env python3
"""
Batch process multiple TCGA narrowPeak files to generate genomic windows with CA signal.
"""

import glob
import os
import subprocess
import argparse
from concurrent.futures import ProcessPoolExecutor, as_completed
import time


def process_single_file(narrowpeak_file, window_size, output_dir):
    """
    Process a single narrowPeak file.
    
    Args:
        narrowpeak_file: Path to narrowPeak file
        window_size: Window size in bp
        output_dir: Output directory
    
    Returns:
        Tuple of (success, filename, message)
    """
    try:
        # Extract sample name from filename
        basename = os.path.basename(narrowpeak_file)
        if basename.startswith('TCGA_ATAC_'):
            sample_name = basename.replace('TCGA_ATAC_', '').replace('.narrowPeak', '')
        else:
            sample_name = basename.replace('.narrowPeak', '')
        
        # Generate output filename
        if window_size >= 1000:
            size_label = f"{window_size // 1000}kb"
        else:
            size_label = f"{window_size}bp"
        
        output_file = os.path.join(output_dir, f"{sample_name}_windows_{size_label}.tsv")
        
        # Build command
        cmd = [
            'python', 'process_narrowpeak_memory_efficient.py',
            '--main-chr-only',
            '--window-size', str(window_size),
            '--pattern', narrowpeak_file,
            '--output', output_file
        ]
        
        # Run processing
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            return True, sample_name, f"Successfully processed {len(open(output_file).readlines())} windows"
        else:
            return False, sample_name, f"Error: {result.stderr}"
            
    except Exception as e:
        return False, narrowpeak_file, f"Exception: {str(e)}"


def main():
    parser = argparse.ArgumentParser(description='Batch process TCGA narrowPeak files')
    parser.add_argument('--pattern', default='TCGA_ATAC_*.narrowPeak', 
                       help='Pattern to match narrowPeak files (default: TCGA_ATAC_*.narrowPeak)')
    parser.add_argument('--window-size', type=int, default=10000,
                       help='Window size in base pairs (default: 10000)')
    parser.add_argument('--output-dir', default='tcga_windows_output',
                       help='Output directory (default: tcga_windows_output)')
    parser.add_argument('--max-workers', type=int, default=4,
                       help='Maximum number of parallel processes (default: 4)')
    parser.add_argument('--max-files', type=int, default=None,
                       help='Maximum number of files to process (default: all)')
    parser.add_argument('--sample-subset', nargs='+',
                       help='Process only specific samples (e.g., TCGA-DJ-A13X-01A)')
    
    args = parser.parse_args()
    
    # Find narrowPeak files
    narrowpeak_files = glob.glob(args.pattern)
    
    if not narrowpeak_files:
        print(f"No files found matching pattern: {args.pattern}")
        return
    
    # Filter to specific samples if requested
    if args.sample_subset:
        filtered_files = []
        for sample in args.sample_subset:
            matching_files = [f for f in narrowpeak_files if sample in f]
            filtered_files.extend(matching_files)
        narrowpeak_files = filtered_files
        
        if not narrowpeak_files:
            print(f"No files found for requested samples: {args.sample_subset}")
            return
    
    # Limit number of files if requested
    if args.max_files:
        narrowpeak_files = narrowpeak_files[:args.max_files]
    
    print(f"Found {len(narrowpeak_files)} narrowPeak files to process")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Process files in parallel
    start_time = time.time()
    successful = 0
    failed = 0
    
    print(f"Processing with {args.max_workers} parallel workers...")
    
    with ProcessPoolExecutor(max_workers=args.max_workers) as executor:
        # Submit all jobs
        future_to_file = {
            executor.submit(process_single_file, f, args.window_size, args.output_dir): f 
            for f in narrowpeak_files
        }
        
        # Process completed jobs
        for i, future in enumerate(as_completed(future_to_file), 1):
            file_path = future_to_file[future]
            
            try:
                success, sample_name, message = future.result()
                
                if success:
                    successful += 1
                    print(f"✓ [{i}/{len(narrowpeak_files)}] {sample_name}: {message}")
                else:
                    failed += 1
                    print(f"✗ [{i}/{len(narrowpeak_files)}] {sample_name}: {message}")
                    
            except Exception as e:
                failed += 1
                print(f"✗ [{i}/{len(narrowpeak_files)}] {os.path.basename(file_path)}: Exception: {e}")
    
    elapsed_time = time.time() - start_time
    
    print(f"\n{'='*60}")
    print(f"Batch processing completed in {elapsed_time:.1f} seconds")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Total: {len(narrowpeak_files)}")
    
    # List output files
    output_files = glob.glob(os.path.join(args.output_dir, "*.tsv"))
    print(f"\nGenerated {len(output_files)} output files in {args.output_dir}/")
    
    if output_files:
        print("Sample output files:")
        for f in sorted(output_files)[:5]:
            size = os.path.getsize(f)
            print(f"  {os.path.basename(f)} ({size:,} bytes)")
        
        if len(output_files) > 5:
            print(f"  ... and {len(output_files) - 5} more files")


if __name__ == "__main__":
    main()
