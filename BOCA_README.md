# BOCA Brain Open Chromatin Atlas Integration

This directory contains scripts and tools to download, process, and integrate the BOCA (Brain Open Chromatin Atlas) dataset with your existing ENCODE and TCGA chromatin accessibility analysis pipeline.

## 📊 Dataset Overview

**BOCA Dataset**: 115 human brain ATAC-seq samples from <PERSON><PERSON> et al. (2018)
- **Paper**: "An atlas of chromatin accessibility in the adult human brain"
- **Journal**: Genome Research, 28(8):1243–52
- **PMID**: 29945882
- **GEO**: GSE96949
- **DOI**: https://doi.org/10.1101/gr.232488.117

### Sample Details
- **115 samples** from **5 control subjects**
- **2 cell types**: Neuronal (NeuN+) and Non-neuronal (NeuN-)
- **14 brain regions**: ACC, AMY, DLPFC, HIPP, INS, ITC, MDT, NAC, OFC, PMC, PUT, PVC, STC, VLPFC
- **Genome build**: GRCh37/hg19 (with hg38 liftover available)

## 🚀 Quick Start

### Option 1: Quick Download (Recommended)
```bash
# Download pre-processed peak files and metadata
python3 download_boca_quick_start.py
```

### Option 2: Full BigWig Processing
```bash
# Download BigWig files and convert to narrowPeak (slower, ~65GB)
python3 03_scripts/processing/process_boca_brain_data.py --download --convert --max-files 5
```

### Option 3: Pre-processed Peaks Only
```bash
# Download only the pre-processed peak files
python3 03_scripts/processing/download_boca_peaks.py --genome hg19
```

## 📁 File Structure

After running the scripts, your directory structure will be:

```
01_raw_data/boca_brain/
├── peaks/                          # Extracted BED files
├── narrowpeaks/                    # Converted narrowPeak files
├── boca_peaks_hg19.zip            # Region-specific peaks
├── boca_consensus_peaks_hg19.bed  # Consensus peaks
├── boca_raw_count_matrix.tsv.gz   # Raw count matrix
├── boca_norm_count_matrix.tsv.gz  # Normalized count matrix
└── dataset_info.txt               # Dataset information

02_processed_data/boca_brain/
├── boca_sample_metadata.csv       # Sample metadata
├── boca_summary.txt               # Dataset summary
└── boca_standardized_metadata.csv # Standardized metadata

01_raw_data/other_narrowpeaks/
└── BOCA_*.narrowPeak              # BOCA files in standard location
```

## 🔄 Integration with ENCODE/TCGA Pipeline

### Step 1: Download BOCA Data
```bash
# Quick start download
python3 download_boca_quick_start.py
```

### Step 2: Process to narrowPeak Format
```bash
# If using BigWig files
python3 03_scripts/processing/process_boca_brain_data.py --download --convert

# If using pre-processed peaks
python3 03_scripts/processing/download_boca_peaks.py
```

### Step 3: Integrate with Existing Pipeline
```bash
# Full integration
python3 03_scripts/processing/integrate_boca_with_pipeline.py --all

# Or step by step:
python3 03_scripts/processing/integrate_boca_with_pipeline.py --create-metadata
python3 03_scripts/processing/integrate_boca_with_pipeline.py --copy-files
python3 03_scripts/processing/integrate_boca_with_pipeline.py --unified-metadata
python3 03_scripts/processing/integrate_boca_with_pipeline.py --summary
```

### Step 4: Run Analysis Pipeline
```bash
# Process with existing scripts
python3 03_scripts/processing/process_narrowpeak_files.py --input-dir 01_raw_data/other_narrowpeaks
```

## 📋 Available Data Formats

### 1. BigWig Files (from GEO)
- **Size**: 65.3 GB total
- **Format**: Normalized signal tracks
- **Use**: For custom peak calling and signal analysis

### 2. Pre-processed Peaks (from BOCA)
- **Region-specific peaks**: Cell type and brain region specific
- **Consensus peaks**: Merged across all samples
- **Format**: BED files (convertible to narrowPeak)

### 3. Count Matrices
- **Raw counts**: Read counts per peak per sample
- **Normalized counts**: Processed count matrix
- **Format**: TSV (tab-separated values)

## 🧠 Sample Naming Convention

BOCA samples follow the pattern: `{Subject}_{CellType}_{Region}`

- **Subject**: 351, 372, 437, 440, 446
- **CellType**: G (Non-neuronal), N (Neuronal)  
- **Region**: ACC, AMY, DLPFC, HIPP, INS, ITC, MDT, NAC, OFC, PMC, PUT, PVC, STC, VLPFC

Example: `351_N_DLPFC` = Subject 351, Neuronal cells, Dorsolateral Prefrontal Cortex

## 🔧 Script Descriptions

### `download_boca_quick_start.py`
- Quick download of essential BOCA files
- Creates basic directory structure
- Downloads pre-processed peaks and count matrices

### `process_boca_brain_data.py`
- Downloads BigWig files from GEO
- Converts BigWig to narrowPeak format
- Creates comprehensive sample metadata

### `download_boca_peaks.py`
- Downloads pre-processed peak files
- Converts BED to narrowPeak format
- Faster alternative to BigWig processing

### `integrate_boca_with_pipeline.py`
- Integrates BOCA with existing ENCODE/TCGA pipeline
- Creates unified metadata across all datasets
- Copies files to standard locations

## 📊 Data Access Options

### Direct Downloads
- **BOCA Website**: http://icahn.mssm.edu/boca
- **GEO Repository**: https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE96949
- **UCSC Browser**: [hg19](http://genome.ucsc.edu/cgi-bin/hgTracks?db=hg19&hubUrl=https://multireg.s3.amazonaws.com/ucsc/hub.txt) | [hg38](http://genome.ucsc.edu/cgi-bin/hgTracks?db=hg38&hubUrl=https://multireg.s3.amazonaws.com/ucsc/hub.txt)

### Programmatic Access
```python
# Using the BOCAProcessor class
from process_boca_brain_data import BOCAProcessor

processor = BOCAProcessor()
processor.download_bigwig_files(max_files=5)  # Test with 5 files
processor.convert_to_narrowpeak()
```

## 🔍 Quality Control

### File Validation
```bash
# Check downloaded files
ls -lh 01_raw_data/boca_brain/
wc -l 01_raw_data/boca_brain/peaks/*.bed

# Validate narrowPeak format
head -5 01_raw_data/boca_brain/narrowpeaks/*.narrowPeak
```

### Sample Counts
- **Expected**: 115 total samples
- **Neuronal**: ~57 samples
- **Non-neuronal**: ~58 samples
- **Per region**: Variable (5-10 samples depending on region)

## 🚨 Important Notes

1. **File Sizes**: BigWig files are large (~500MB-1GB each). Consider using pre-processed peaks for faster setup.

2. **Genome Build**: BOCA data is in GRCh37/hg19. Liftover to hg38 is available.

3. **Memory Requirements**: BigWig to narrowPeak conversion requires significant memory for large chromosomes.

4. **Network**: Downloads may take time depending on connection speed.

## 🆘 Troubleshooting

### Common Issues

1. **Download Failures**
   ```bash
   # Resume interrupted downloads
   python3 03_scripts/processing/process_boca_brain_data.py --download --resume
   ```

2. **Memory Issues**
   ```bash
   # Process fewer files at once
   python3 03_scripts/processing/process_boca_brain_data.py --convert --max-files 10
   ```

3. **Missing Dependencies**
   ```bash
   pip install pyBigWig pandas numpy requests
   ```

## 📚 References

1. Fullard JF, Hauberg ME, Bendl J, et al. An atlas of chromatin accessibility in the adult human brain. Genome Research. 2018;28(8):1243-1252.

2. BOCA Database: http://icahn.mssm.edu/boca

3. GEO Dataset: https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE96949

## 📞 Support

For issues with the integration scripts, check the logs in `04_logs/` directory or review the error messages for specific troubleshooting steps.
