import pandas as pd

# 1. Load your intersection file (no header, so assign columns)
cols_intersect = [
    "chr_gene", "start_gene", "end_gene", "gene_name", "source", "attributes",
    "chr_region", "start_region", "end_region", "col7", "col8", "col9", "col10"
]
df_intersect = pd.read_csv("intersected.bed", sep="\t", names=cols_intersect)

# 2. Load the OncoKB file
# df_oncokb = pd.read_csv("20250116_oncokb_cancerGeneList.tsv", sep="\t")
df_oncokb = pd.read_csv("cgc_v100_17102024.tsv", sep="\t")


# 3. Merge (join) on gene_name vs. “Hugo Symbol”
#    The 'how="left"' means keep all genes from intersect, and match if found in OncoKB
df_merged = pd.merge(df_intersect, df_oncokb, left_on="gene_name", right_on="GENE_SYMBOL", how="left")

# 4. Check if a gene is OncoKB-annotated
df_merged["Is_in_CGC"] = ~df_merged["GENE_SYMBOL"].isnull()

# 5. Write out the merged results
df_merged.to_csv("intersected_annotated_CGC.csv", index=False)
