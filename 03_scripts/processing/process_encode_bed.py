#!/usr/bin/env python3
"""
Process ENCODE BED file (ENCFF135EYC.bed) to create 10kb genomic windows with CA signal.
"""

import pandas as pd
import numpy as np
import argparse
import os


def create_genomic_windows_template(window_size=10000):
    """
    Create a template of genomic windows for main chromosomes.
    """
    print("Creating genomic windows template...")
    
    # Define main chromosomes and their approximate lengths
    chr_lengths = {
        'chr1': 248956422, 'chr2': 242193529, 'chr3': 198295559, 'chr4': 190214555,
        'chr5': 181538259, 'chr6': 170805979, 'chr7': 159345973, 'chr8': 145138636,
        'chr9': 138394717, 'chr10': 133797422, 'chr11': 135086622, 'chr12': 133275309,
        'chr13': 114364328, 'chr14': 107043718, 'chr15': 101991189, 'chr16': 90338345,
        'chr17': 83257441, 'chr18': 80373285, 'chr19': 58617616, 'chr20': 64444167,
        'chr21': 46709983, 'chr22': 50818468, 'chrX': 156040895, 'chrY': 57227415
    }
    
    windows_list = []
    
    for chr_name, max_pos in chr_lengths.items():
        # Create windows from 1 to max_pos with step = window_size
        starts = list(range(1, max_pos + 1, window_size))
        
        for start in starts:
            end = min(start + window_size - 1, max_pos)
            windows_list.append({
                'chr': chr_name,
                'start': start,
                'end': end
            })
    
    template_df = pd.DataFrame(windows_list)
    print(f"Created template with {len(template_df)} windows")
    
    return template_df


def process_encode_bed(bed_file, window_size=10000, output_file=None):
    """
    Process ENCODE BED file to create genomic windows with CA signal.
    
    Args:
        bed_file: Path to ENCODE BED file
        window_size: Window size in bp
        output_file: Output file name
    """
    print(f"Processing ENCODE BED file: {bed_file}")
    
    # Read the BED file
    print("Loading peaks...")
    peaks_df = pd.read_csv(bed_file, sep='\t')
    
    # Check if it has a header
    if peaks_df.iloc[0, 0] == 'chrom':
        # Skip header row
        peaks_df = peaks_df.iloc[1:].reset_index(drop=True)
    
    # Rename columns to standard format
    if len(peaks_df.columns) >= 9:
        # This appears to be narrowPeak format with header
        peaks_df.columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 'signalValue', 'pValue', 'qValue', 'peak'][:len(peaks_df.columns)]
        # Handle the '.' values in signalValue column
        peaks_df['signalValue'] = pd.to_numeric(peaks_df['signalValue'], errors='coerce')
        # Use pValue as signal if signalValue is missing
        if peaks_df['signalValue'].isna().all():
            peaks_df['signalValue'] = pd.to_numeric(peaks_df['pValue'], errors='coerce')
    else:
        # Basic BED format
        peaks_df.columns = ['chr', 'start', 'end', 'name', 'score', 'strand'][:len(peaks_df.columns)]
        peaks_df['signalValue'] = pd.to_numeric(peaks_df['score'], errors='coerce') / 100.0  # Convert score to signal
    
    # Convert coordinates to numeric
    peaks_df['start'] = pd.to_numeric(peaks_df['start'], errors='coerce')
    peaks_df['end'] = pd.to_numeric(peaks_df['end'], errors='coerce')

    # Ensure signalValue is numeric and handle missing values
    if 'signalValue' not in peaks_df.columns:
        peaks_df['signalValue'] = pd.to_numeric(peaks_df['score'], errors='coerce') / 100.0
    else:
        peaks_df['signalValue'] = pd.to_numeric(peaks_df['signalValue'], errors='coerce')

    # Drop rows with missing essential data
    peaks_df = peaks_df.dropna(subset=['start', 'end', 'signalValue'])
    
    print(f"Loaded {len(peaks_df)} peaks")
    print(f"Signal value range: {peaks_df['signalValue'].min():.3f} - {peaks_df['signalValue'].max():.3f}")
    
    # Filter to main chromosomes
    main_chrs = [f'chr{i}' for i in range(1, 23)] + ['chrX', 'chrY']
    peaks_df = peaks_df[peaks_df['chr'].isin(main_chrs)]
    print(f"Filtered to main chromosomes: {len(peaks_df)} peaks")
    
    # Create genomic windows template
    template_df = create_genomic_windows_template(window_size)
    
    # Initialize CA_signal column
    template_df['CA_signal'] = 0.0
    
    print("Computing overlaps and mean signals...")
    
    # Process each chromosome separately for efficiency
    for chr_name in template_df['chr'].unique():
        print(f"  Processing {chr_name}...")
        
        chr_peaks = peaks_df[peaks_df['chr'] == chr_name].copy()
        chr_windows = template_df[template_df['chr'] == chr_name].copy()
        
        if len(chr_peaks) == 0:
            continue
        
        # For each window, find overlapping peaks and compute mean signal
        for idx in chr_windows.index:
            window = template_df.loc[idx]
            
            # Find peaks that overlap with this window
            overlapping_peaks = chr_peaks[
                (chr_peaks['end'] >= window['start']) & 
                (chr_peaks['start'] <= window['end'])
            ]
            
            if len(overlapping_peaks) > 0:
                mean_signal = overlapping_peaks['signalValue'].mean()
                template_df.loc[idx, 'CA_signal'] = mean_signal
    
    # Generate output filename if not provided
    if output_file is None:
        base_name = os.path.splitext(bed_file)[0]
        if window_size >= 1000:
            size_label = f"{window_size // 1000}kb"
        else:
            size_label = f"{window_size}bp"
        output_file = f"{base_name}_windows_{size_label}.tsv"
    
    # Save results
    print(f"Saving results to {output_file}...")
    template_df.to_csv(output_file, sep='\t', index=False)
    
    # Display summary statistics
    print(f"\nSummary statistics:")
    print(f"Total windows: {len(template_df)}")
    print(f"Windows with signal > 0: {(template_df['CA_signal'] > 0).sum()}")
    print(f"Mean CA signal: {template_df['CA_signal'].mean():.4f}")
    print(f"Max CA signal: {template_df['CA_signal'].max():.4f}")
    
    # Display first few rows
    print(f"\nFirst 10 rows of results:")
    print(template_df.head(10))
    
    return output_file


def add_to_tcga_matrix(encode_windows_file, tcga_matrix_file, sample_name="ENCODE_ENCFF135EYC", output_file=None):
    """
    Add ENCODE sample to existing TCGA matrix.
    
    Args:
        encode_windows_file: ENCODE windows file
        tcga_matrix_file: TCGA combined matrix file
        sample_name: Name for the ENCODE sample column
        output_file: Output file name
    """
    print(f"Adding ENCODE sample to TCGA matrix...")
    
    # Load ENCODE windows
    encode_df = pd.read_csv(encode_windows_file, sep='\t')
    
    # Load TCGA matrix
    print("Loading TCGA matrix (this may take a moment)...")
    tcga_df = pd.read_csv(tcga_matrix_file, sep='\t')
    
    print(f"TCGA matrix: {len(tcga_df)} windows × {len(tcga_df.columns)} columns")
    print(f"ENCODE data: {len(encode_df)} windows")
    
    # Create merge key
    encode_df['merge_key'] = encode_df['chr'] + ':' + encode_df['start'].astype(str) + '-' + encode_df['end'].astype(str)
    tcga_df['merge_key'] = tcga_df['chr'] + ':' + tcga_df['start'].astype(str) + '-' + tcga_df['end'].astype(str)
    
    # Merge the data
    print("Merging datasets...")
    combined_df = pd.merge(
        tcga_df, 
        encode_df[['merge_key', 'CA_signal']].rename(columns={'CA_signal': sample_name}),
        on='merge_key',
        how='left'
    )
    
    # Fill missing values with 0
    combined_df[sample_name] = combined_df[sample_name].fillna(0)
    
    # Remove merge key
    combined_df = combined_df.drop('merge_key', axis=1)
    
    # Generate output filename if not provided
    if output_file is None:
        base_name = os.path.splitext(tcga_matrix_file)[0]
        output_file = f"{base_name}_with_ENCODE.tsv"
    
    # Save combined matrix
    print(f"Saving combined matrix to {output_file}...")
    combined_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"\nCombined matrix:")
    print(f"Dimensions: {len(combined_df)} windows × {len(combined_df.columns)} columns")
    print(f"New column '{sample_name}' added")
    print(f"ENCODE signal range: {combined_df[sample_name].min():.4f} - {combined_df[sample_name].max():.4f}")
    
    return output_file


def main():
    parser = argparse.ArgumentParser(description='Process ENCODE BED file to create genomic windows')
    parser.add_argument('bed_file', help='ENCODE BED file to process')
    parser.add_argument('--window-size', type=int, default=10000,
                       help='Window size in base pairs (default: 10000)')
    parser.add_argument('--output', help='Output file name (auto-generated if not specified)')
    parser.add_argument('--add-to-tcga', help='Add to existing TCGA matrix file')
    parser.add_argument('--sample-name', default='ENCODE_ENCFF135EYC',
                       help='Sample name for ENCODE data (default: ENCODE_ENCFF135EYC)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.bed_file):
        print(f"Error: BED file {args.bed_file} not found")
        return
    
    # Process ENCODE BED file
    encode_windows_file = process_encode_bed(args.bed_file, args.window_size, args.output)
    
    # Add to TCGA matrix if requested
    if args.add_to_tcga:
        if not os.path.exists(args.add_to_tcga):
            print(f"Error: TCGA matrix file {args.add_to_tcga} not found")
            return
        
        combined_file = add_to_tcga_matrix(encode_windows_file, args.add_to_tcga, args.sample_name)
        print(f"\nFinal combined matrix saved to: {combined_file}")


if __name__ == "__main__":
    main()
