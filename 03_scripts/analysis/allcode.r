breast_patient_ids <- readLine("HMF_sample_lists/Breast.txt")
breast_patient_ids <- readLines("HMF_sample_lists/Breast.txt")
filtered_maf_HMF_breast <- maf_HMF[maf_HMF$patient_id %in% breast_patient_ids, ]
View(filtered_maf_HMF_breast)
all_breast <- all(filtered_maf_HMF$cancer_type == "breast")
all_breast <- all(filtered_maf_HMF_breast$cancer_type == "breast")
rm(all_breast)
rm(all_same)
rm(nique_cancer_types2)
View(filtered_maf_HMF_breast)
library(dplyr)
window_size <- 10000  # Set the size of each genomic window

window_size <- 1000  # Set the size of each genomic window
unique_chromosomes <- unique(maf_HMF$chr)

windows_df <- do.call(rbind, lapply(unique_chromosomes, function(chr) {
  # Get the maximum start position for this chromosome
  max_start_chr <- max(maf_HMF$start[maf_HMF$chr == chr], na.rm = TRUE)
  
  # Generate windows for this chromosome
  data.frame(
    chr = chr,
    start = seq(1, max_start_chr, by = window_size),
    end = pmin(seq(1, max_start_chr, by = window_size) + window_size - 1, max_start_chr)
  )
}))
windows_df$mutation_count <- 0



breast_patient_ids <- readLines("HMF_sample_lists/Breast.txt")
filtered_maf_HMF_breast <- maf_HMF[maf_HMF$patient_id %in% breast_patient_ids, ]
filtered_maf_HMF_breast <- filtered_maf_HMF_breast %>%
mutate(window_start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_breast %>%
group_by(chr, window_start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# View the mutation counts per window
print(mutation_counts)
View(mutation_counts)
mutation_counts <- mutation_counts %>% rename(start = window_start)
windows_df <- windows_df %>% select(-mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
total_mutation_count <- sum(windows_df$mutation_count)
# Print the result
print(total_mutation_count)
windows_df <- windows_df %>% rename(breast = mutation_count)

prost_patient_ids <- readLines("HMF_sample_lists/Prostate.txt")
filtered_maf_HMF_prostate <- maf_HMF[maf_HMF$patient_id %in% prost_patient_ids,]
filtered_maf_HMF_prostate <- filtered_maf_HMF_prostate %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
mutation_counts <- filtered_maf_HMF_prostate %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
total_mutation_count <- sum(mutation_counts$mutation_count)
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(prostate = mutation_count)

kidney_patient_ids <- readLines("HMF_sample_lists/Kidney.txt")
filtered_maf_HMF_kidney <- maf_HMF[maf_HMF$patient_id %in% kidney_patient_ids, ]
filtered_maf_HMF_kidney <- filtered_maf_HMF_kidney %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
mutation_counts <- filtered_maf_HMF_kidney %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
windows_df <- windows_df %>% rename(kidney = mutation_count)

skin_patient_ids <- readLines("HMF_sample_lists/Skin.txt")
filtered_maf_HMF_skin <- maf_HMF[maf_HMF$patient_id %in% skin_patient_ids, ]
filtered_maf_HMF_skin <- filtered_maf_HMF_skin %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
mutation_counts <- filtered_maf_HMF_skin %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
total_mutation_count <- sum(mutation_counts$mutation_count)
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
windows_df <- windows_df %>% rename(skin = mutation_count)

uterus_patient_ids <- readLines("HMF_sample_lists/Uterus.txt")
filtered_maf_HMF_uterus <- maf_HMF[maf_HMF$patient_id %in% uterus_patient_ids, ]
filtered_maf_HMF_uterus <- filtered_maf_HMF_uterus %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_uterus %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(uterus = mutation_count)

eso_patient_ids <- readLines("HMF_sample_lists/Esophagus.txt")
filtered_maf_HMF_eso <- maf_HMF[maf_HMF$patient_id %in% eso_patient_ids, ]
filtered_maf_HMF_eso <- filtered_maf_HMF_eso %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_eso %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(esophagus = mutation_count)
# Load the patient IDs from the stomach.txt file

stomach_patient_ids <- readLines("HMF_sample_lists/Stomach.txt")
# Filter maf_HMF to include only rows with patient_id in stomach_patient_ids
filtered_maf_HMF_stomach <- maf_HMF[maf_HMF$patient_id %in% stomach_patient_ids, ]
filtered_maf_HMF_stomach <- filtered_maf_HMF_stomach %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_stomach %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(stomach = mutation_count)

CNS_patient_ids <- readLines("HMF_sample_lists/Nervous_system.txt")
# Filter maf_HMF to include only rows with patient_id in CNS_patient_ids
filtered_maf_HMF_CNS <- maf_HMF[maf_HMF$patient_id %in% CNS_patient_ids, ]
filtered_maf_HMF_CNS <- filtered_maf_HMF_CNS %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_CNS %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(nervous_system = mutation_count)

print(total_mutation_count)
# Load the patient IDs from the lung.txt file
lung_patient_ids <- readLines("HMF_sample_lists/Lung.txt")
# Filter maf_HMF to include only rows with patient_id in lung_patient_ids
filtered_maf_HMF_lung <- maf_HMF[maf_HMF$patient_id %in% lung_patient_ids, ]
filtered_maf_HMF_lung <- filtered_maf_HMF_lung %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_lung %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(lung = mutation_count)

ColoRect_patient_ids <- readLines("HMF_sample_lists/colorectal.txt")
# Filter maf_HMF to include only rows with patient_id in ColoRect_patient_ids
filtered_maf_HMF_ColoRect <- maf_HMF[maf_HMF$patient_id %in% ColoRect_patient_ids, ]
filtered_maf_HMF_ColoRect <- filtered_maf_HMF_ColoRect %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_ColoRect %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(colorectal = mutation_count)

print(total_mutation_count)
biliary_patient_ids <- readLines("HMF_sample_lists/Biliary.txt")
# Filter maf_HMF to include only rows with patient_id in biliary_patient_ids
filtered_maf_HMF_biliary <- maf_HMF[maf_HMF$patient_id %in% biliary_patient_ids, ]
filtered_maf_HMF_biliary <- filtered_maf_HMF_biliary %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_biliary %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(biliary = mutation_count)

print(total_mutation_count)
head_patient_ids <- readLines("HMF_sample_lists/head_neck.txt")
# Filter maf_HMF to include only rows with patient_id in head_patient_ids
filtered_maf_HMF_head <- maf_HMF[maf_HMF$patient_id %in% head_patient_ids, ]

filtered_maf_HMF_head <- filtered_maf_HMF_head %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_head %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(head_neck = mutation_count)

lymphoid_patient_ids <- readLines("HMF_sample_lists/lymphoid.txt")
# Filter maf_HMF to include only rows with patient_id in lymphoid_patient_ids
filtered_maf_HMF_lymphoid <- maf_HMF[maf_HMF$patient_id %in% lymphoid_patient_ids, ]
filtered_maf_HMF_lymphoid <- filtered_maf_HMF_lymphoid %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_lymphoid %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(lymphoid = mutation_count)


liver_patient_ids <- readLines("HMF_sample_lists/liver.txt")
# Filter maf_HMF to include only rows with patient_id in liver_patient_ids
filtered_maf_HMF_liver <- maf_HMF[maf_HMF$patient_id %in% liver_patient_ids, ]

filtered_maf_HMF_liver <- filtered_maf_HMF_liver %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_liver %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(liver = mutation_count)


pancancer_patient_ids <- readLines("HMF_sample_lists/all_no_dup.txt")
# Filter maf_HMF to include only rows with patient_id in pancancer_patient_ids
filtered_maf_HMF_pancancer <- maf_HMF[maf_HMF$patient_id %in% pancancer_patient_ids, ]

filtered_maf_HMF_pancancer <- filtered_maf_HMF_pancancer %>%
mutate(start = ((start - 1) %/% window_size) * window_size + 1)
# Count mutations by `chr` and `window_start`, ensuring each mutation is counted only once
mutation_counts <- filtered_maf_HMF_pancancer %>%
group_by(chr, start) %>%
summarise(mutation_count = n(), .groups = 'drop')
# Sum up the total mutation count from this grouped data
total_mutation_count <- sum(mutation_counts$mutation_count)
# Print the total mutation count
print(total_mutation_count)
# Perform a left join to merge mutation_counts into windows_df
windows_df <- windows_df %>%
left_join(mutation_counts, by = c("chr", "start")) %>%
# Replace NA values in mutation_count with 0
mutate(mutation_count = ifelse(is.na(mutation_count), 0, mutation_count))
# View the updated windows_df
print(windows_df)
windows_df <- windows_df %>% rename(pancancer = mutation_count)