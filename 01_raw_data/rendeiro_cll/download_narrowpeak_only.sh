#!/bin/bash

# Download script for <PERSON><PERSON><PERSON> et al. CLL narrowPeak files only
# Paper: Chromatin mapping and single-cell immune profiling define the temporal dynamics of ibrutinib response in CLL
# Nature Communications (2020), PMID: 31996669
# GEO Accession: GSE111013 (ATAC-seq)

set -e  # Exit on any error

# Create directory structure
echo "Creating directory structure..."
mkdir -p 01_raw_data/rendeiro_cll/{narrowpeak,metadata}

# Change to the data directory
cd 01_raw_data/rendeiro_cll

echo "=== Downloading Rendeiro et al. CLL narrowPeak Files Only ==="
echo "Study: Chromatin mapping and single-cell immune profiling define the temporal dynamics of ibrutinib response in CLL"
echo "Authors: <AUTHORS>
echo "Journal: Nature Communications (2020)"
echo "PMID: 31996669"
echo ""

# Download metadata files first
echo "Downloading metadata files..."
cd metadata

echo "Downloading ATAC-seq metadata (GSE111013)..."
wget -O GSE111013_series_matrix.txt.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111013/matrix/GSE111013_series_matrix.txt.gz"

echo "Downloading detailed sample information..."
wget -O GSE111013_family.soft.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111013/soft/GSE111013_family.soft.gz"

cd ..

# Download and extract only narrowPeak files
echo ""
echo "Downloading ATAC-seq data (19.6 GB) - will extract only narrowPeak files..."
echo "This includes narrowPeak files for 176 samples"

# Create temporary directory for extraction
mkdir -p temp_extract
cd temp_extract

# Download the large TAR file containing all processed ATAC-seq data
echo "Downloading TAR file..."
wget -O GSE111013_RAW.tar "https://www.ncbi.nlm.nih.gov/geo/download/?acc=GSE111013&format=file"

echo "Extracting only narrowPeak files from TAR archive..."
# Extract only narrowPeak files using tar with pattern matching
tar -xf GSE111013_RAW.tar --wildcards "*.narrowPeak*"

# Move narrowPeak files to the narrowpeak directory
echo "Moving narrowPeak files to organized directory..."
find . -name "*.narrowPeak*" -exec mv {} ../narrowpeak/ \;

# Clean up
cd ..
rm -rf temp_extract

echo ""
echo "=== Download Summary ==="
echo "Downloaded: narrowPeak files only (176 samples)"
echo "Skipped: BigWig files (signal tracks)"
echo "Metadata: Series matrix and SOFT files included"
echo ""
echo "Cell types included:"
echo "- Normal donors: B-cells, CD4+ T-cells, CD8+ T-cells, T-cells, Naive B-cells, Switched B-cells, Unswitched B-cells"
echo "- CLL patients: CLL cells, B-cells, CD4+ T-cells, CD8+ T-cells, NK cells, Monocytes"
echo "- Time points: 0, 1, 2, 3, 8, 30, 120, 150, 240, 280 days of ibrutinib treatment"
echo ""
echo "Genome build: GRCh37/hg19"
echo ""

# Count the downloaded narrowPeak files
narrowpeak_count=$(find narrowpeak -name "*.narrowPeak*" | wc -l)
echo "Total narrowPeak files downloaded: $narrowpeak_count"
echo ""
echo "Download completed successfully!"
echo "narrowPeak files location: $(pwd)/narrowpeak/"

# Create a summary file
echo "Creating data summary file..."
cat > narrowpeak_summary.txt << EOF
Rendeiro et al. CLL Chromatin Accessibility Dataset - narrowPeak Files Only
=========================================================================

Study Information:
- Title: Chromatin mapping and single-cell immune profiling define the temporal dynamics of ibrutinib response in CLL
- Authors: <AUTHORS>
- Journal: Nature Communications (2020)
- PMID: 31996669
- DOI: https://doi.org/10.1038/s41467-019-14081-6

GEO Accession: GSE111013 (ATAC-seq)

Dataset Description:
- 176 ATAC-seq samples of primary immune cell types
- Normal donors: 3 individuals
- CLL patients: 8 individuals
- Genome build: GRCh37/hg19

Downloaded Files:
- narrowPeak files only (peak calls)
- Metadata files (sample information)

Cell Types:
Normal Donors:
- B-cells
- CD4+ T-cells  
- CD8+ T-cells
- T-cells
- Naive B-cells
- Switched B-cells
- Unswitched B-cells

CLL Patients:
- CLL cells
- B-cells
- CD4+ T-cells
- CD8+ T-cells
- NK cells
- Monocytes

Treatment Time Points:
- Day 0 (baseline)
- Day 1, 2, 3 (early response)
- Day 8, 30 (intermediate response)  
- Day 120, 150, 240, 280 (late response)

File Format:
- narrowPeak: BED6+4 format with peak calls and significance scores
- Columns: chr, start, end, name, score, strand, signalValue, pValue, qValue, peak

Download Date: $(date)
Total narrowPeak files: $narrowpeak_count
EOF

echo ""
echo "narrowPeak summary saved to: narrowpeak_summary.txt"

# List some example files
echo ""
echo "Example narrowPeak files downloaded:"
find narrowpeak -name "*.narrowPeak*" | head -10
if [ $(find narrowpeak -name "*.narrowPeak*" | wc -l) -gt 10 ]; then
    echo "... and $(( $(find narrowpeak -name "*.narrowPeak*" | wc -l) - 10 )) more files"
fi
