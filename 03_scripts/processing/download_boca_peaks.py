#!/usr/bin/env python3
"""
Download pre-processed BOCA peak files and convert to narrowPeak format.
This is faster than downloading BigWig files and calling peaks.

BOCA provides:
1. Region & cell-type specific peaks (BED format)
2. Consensus peaks (BED format)
3. Count matrices
"""

import os
import sys
import requests
import pandas as pd
import zipfile
import argparse
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BOCAPeakDownloader:
    """Download and process BOCA pre-computed peak files."""
    
    def __init__(self, base_dir=".", download_dir="01_raw_data/boca_brain"):
        self.base_dir = Path(base_dir)
        self.download_dir = self.base_dir / download_dir
        self.peaks_dir = self.download_dir / "peaks"
        self.narrowpeak_dir = self.download_dir / "narrowpeaks"
        
        # Create directories
        self.download_dir.mkdir(parents=True, exist_ok=True)
        self.peaks_dir.mkdir(parents=True, exist_ok=True)
        self.narrowpeak_dir.mkdir(parents=True, exist_ok=True)
        
        # BOCA download URLs
        self.urls = {
            'peaks_hg19': 'https://bendlj01.dmz.hpc.mssm.edu/multireg/resources/boca_peaks.zip',
            'peaks_hg38': 'https://bendlj01.dmz.hpc.mssm.edu/multireg/resources/boca_peaks_hg38_lifted.zip',
            'consensus_hg19': 'https://multireg.s3.amazonaws.com/resources/boca_peaks_consensus_no_blacklisted_regions.bed',
            'consensus_hg38': 'https://multireg.s3.amazonaws.com/resources/boca_peaks_consensus_no_blacklisted_regions_hg38_lifted.bed',
            'raw_counts': 'https://multireg.s3.amazonaws.com/resources/boca_raw_count_matrix.tsv.gz',
            'norm_counts': 'https://multireg.s3.amazonaws.com/resources/boca_norm_count_matrix.tsv.gz'
        }
    
    def download_file(self, url, output_path, resume=True):
        """Download a single file with resume capability."""
        if resume and output_path.exists():
            logger.info(f"File already exists: {output_path.name}")
            return True
        
        logger.info(f"Downloading {output_path.name}...")
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"Successfully downloaded {output_path.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to download {output_path.name}: {e}")
            return False
    
    def download_peak_files(self, genome='hg19', include_consensus=True, include_counts=True):
        """Download BOCA peak files."""
        logger.info(f"Downloading BOCA peak files for {genome}...")
        
        downloads = []
        
        # Download region-specific peaks
        peaks_url = self.urls[f'peaks_{genome}']
        peaks_file = self.download_dir / f"boca_peaks_{genome}.zip"
        downloads.append((peaks_url, peaks_file))
        
        # Download consensus peaks
        if include_consensus:
            consensus_url = self.urls[f'consensus_{genome}']
            consensus_file = self.download_dir / f"boca_consensus_peaks_{genome}.bed"
            downloads.append((consensus_url, consensus_file))
        
        # Download count matrices
        if include_counts:
            for count_type in ['raw_counts', 'norm_counts']:
                count_url = self.urls[count_type]
                count_file = self.download_dir / f"boca_{count_type.replace('_', '_count_')}.tsv.gz"
                downloads.append((count_url, count_file))
        
        # Perform downloads
        success_count = 0
        for url, output_path in downloads:
            if self.download_file(url, output_path):
                success_count += 1
        
        logger.info(f"Downloaded {success_count}/{len(downloads)} files successfully")
        
        # Extract zip files
        for url, output_path in downloads:
            if output_path.suffix == '.zip' and output_path.exists():
                self.extract_zip(output_path)
        
        return success_count == len(downloads)
    
    def extract_zip(self, zip_path):
        """Extract zip file to peaks directory."""
        logger.info(f"Extracting {zip_path.name}...")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.peaks_dir)
            
            logger.info(f"Successfully extracted {zip_path.name}")
            
            # List extracted files
            extracted_files = list(self.peaks_dir.glob("*"))
            logger.info(f"Extracted {len(extracted_files)} files")
            
        except Exception as e:
            logger.error(f"Failed to extract {zip_path.name}: {e}")
    
    def convert_bed_to_narrowpeak(self, bed_file, output_file):
        """Convert BED file to narrowPeak format."""
        logger.info(f"Converting {bed_file.name} to narrowPeak format...")

        try:
            # Read BED file - BOCA files have many columns, we only need first few
            df = pd.read_csv(bed_file, sep='\t', header=None, usecols=[0, 1, 2, 3])
            df.columns = ['chrom', 'start', 'end', 'length']

            # Ensure numeric columns are properly typed
            df['start'] = pd.to_numeric(df['start'], errors='coerce')
            df['end'] = pd.to_numeric(df['end'], errors='coerce')
            df['length'] = pd.to_numeric(df['length'], errors='coerce')

            # Remove rows with invalid coordinates
            df = df.dropna()
            df = df[df['start'] < df['end']]

            # Create peak names
            peak_names = [f"peak_{i+1}" for i in range(len(df))]

            # Create narrowPeak format
            # narrowPeak: chrom, start, end, name, score, strand, signalValue, pValue, qValue, peak
            narrowpeak_df = pd.DataFrame({
                'chrom': df['chrom'],
                'start': df['start'].astype(int),
                'end': df['end'].astype(int),
                'name': peak_names,
                'score': 1000,  # Default score
                'strand': '.',
                'signalValue': 100,  # Default signal value
                'pValue': -1,  # Not available in BED
                'qValue': -1,  # Not available in BED
                'peak': ((df['end'] - df['start']) // 2).astype(int)  # Peak summit at center
            })

            # Save as narrowPeak
            narrowpeak_df.to_csv(output_file, sep='\t', header=False, index=False)
            logger.info(f"Saved narrowPeak file: {output_file.name} with {len(narrowpeak_df)} peaks")

            return True

        except Exception as e:
            logger.error(f"Failed to convert {bed_file.name}: {e}")
            return False

    def convert_bed_to_narrowpeak_with_signals(self, bed_file, output_file, count_matrix=None):
        """Convert BED file to narrowPeak format with real signal values from count matrix."""
        logger.info(f"Converting {bed_file.name} to narrowPeak format with signal values...")

        try:
            # Read BED file - BOCA files have many columns, we only need first few
            df = pd.read_csv(bed_file, sep='\t', header=None, usecols=[0, 1, 2, 3])
            df.columns = ['chrom', 'start', 'end', 'length']

            # Ensure numeric columns are properly typed
            df['start'] = pd.to_numeric(df['start'], errors='coerce')
            df['end'] = pd.to_numeric(df['end'], errors='coerce')
            df['length'] = pd.to_numeric(df['length'], errors='coerce')

            # Remove rows with invalid coordinates
            df = df.dropna()
            df = df[df['start'] < df['end']]

            # Create peak names that match count matrix
            peak_names = [f"Peak_{i+1}" for i in range(len(df))]

            # Get signal values from count matrix if available
            signal_values = []
            if count_matrix is not None:
                # Parse filename to extract region and cell type
                filename_parts = bed_file.stem.split('_')
                if len(filename_parts) >= 2:
                    region = filename_parts[0].upper()  # e.g., DLPFC
                    cell_type = filename_parts[1].lower()  # e.g., neuron or glia

                    # Map cell type to BOCA naming convention
                    cell_code = 'N' if cell_type == 'neuron' else 'G'

                    # Find all matching columns for this region and cell type
                    matching_columns = []
                    for col in count_matrix.columns:
                        # Format: {subject}_{cell_code}_{region} (e.g., 351_N_DLPFC)
                        if col.endswith(f"_{cell_code}_{region}"):
                            matching_columns.append(col)

                    if matching_columns:
                        logger.info(f"Found {len(matching_columns)} matching samples: {matching_columns}")

                        # Average across all matching samples for this region/cell type
                        sample_signals = count_matrix[matching_columns].mean(axis=1)

                        # Get signal values for this sample
                        if len(sample_signals) >= len(df):
                            signal_values = sample_signals.iloc[:len(df)].tolist()
                        else:
                            # Pad with default values if count matrix is shorter
                            signal_values = sample_signals.tolist()
                            signal_values.extend([1] * (len(df) - len(sample_signals)))

                        logger.info(f"Using averaged signals from {len(matching_columns)} samples")
                    else:
                        logger.warning(f"No matching columns found for {region} {cell_type}")
                        signal_values = [1] * len(df)
                else:
                    logger.warning(f"Could not parse filename: {bed_file.name}")
                    signal_values = [1] * len(df)
            else:
                signal_values = [1] * len(df)

            # Ensure we have the right number of signal values
            if len(signal_values) != len(df):
                logger.warning(f"Signal value count mismatch. Using default values.")
                signal_values = [10] * len(df)

            # Apply minimum signal threshold (peaks with 0 signal are suspicious)
            # Set minimum signal to 1 for called peaks
            signal_values = [max(1, val) for val in signal_values]

            # Create narrowPeak format
            # narrowPeak: chrom, start, end, name, score, strand, signalValue, pValue, qValue, peak
            narrowpeak_df = pd.DataFrame({
                'chrom': df['chrom'],
                'start': df['start'].astype(int),
                'end': df['end'].astype(int),
                'name': peak_names,
                'score': [min(1000, max(1, int(val))) for val in signal_values],  # Score based on signal, min 1
                'strand': '.',
                'signalValue': signal_values,  # Real signal values with minimum threshold
                'pValue': -1,  # Not available in BED
                'qValue': -1,  # Not available in BED
                'peak': ((df['end'] - df['start']) // 2).astype(int)  # Peak summit at center
            })

            # Save as narrowPeak
            narrowpeak_df.to_csv(output_file, sep='\t', header=False, index=False)

            # Log statistics
            signal_stats = pd.Series(signal_values).describe()
            logger.info(f"Saved narrowPeak file: {output_file.name}")
            logger.info(f"  - {len(narrowpeak_df)} peaks")
            logger.info(f"  - Signal range: {signal_stats['min']:.1f} - {signal_stats['max']:.1f}")
            logger.info(f"  - Mean signal: {signal_stats['mean']:.1f}")

            return True

        except Exception as e:
            logger.error(f"Failed to convert {bed_file.name}: {e}")
            return False

    def load_count_matrix(self):
        """Load the BOCA count matrix with signal values."""
        logger.info("Loading BOCA count matrix...")

        count_file = self.download_dir / "boca_raw_count_matrix.tsv.gz"

        if not count_file.exists():
            logger.warning("Count matrix not found. Signal values will be set to defaults.")
            return None

        try:
            # Load count matrix
            import gzip
            with gzip.open(count_file, 'rt') as f:
                count_df = pd.read_csv(f, sep='\t', index_col=0)

            logger.info(f"Loaded count matrix: {count_df.shape[0]} peaks × {count_df.shape[1]} samples")
            return count_df

        except Exception as e:
            logger.error(f"Failed to load count matrix: {e}")
            return None

    def process_all_peaks(self):
        """Convert all BED files to narrowPeak format with real signal values."""
        logger.info("Converting all BED files to narrowPeak format with signal values...")

        # Load count matrix for signal values
        count_matrix = self.load_count_matrix()

        # Find all BED files in peaks directory
        bed_files = list(self.peaks_dir.glob("*.bed"))

        if not bed_files:
            logger.warning("No BED files found in peaks directory")
            return 0

        logger.info(f"Found {len(bed_files)} BED files to convert")

        converted = 0
        for bed_file in bed_files:
            # Create output filename
            narrowpeak_file = self.narrowpeak_dir / f"{bed_file.stem}.narrowPeak"

            if self.convert_bed_to_narrowpeak_with_signals(bed_file, narrowpeak_file, count_matrix):
                converted += 1

        logger.info(f"Converted {converted}/{len(bed_files)} files to narrowPeak format")
        return converted
    
    def create_sample_mapping(self):
        """Create mapping between BOCA files and sample metadata."""
        logger.info("Creating sample mapping...")
        
        # Find all narrowPeak files
        narrowpeak_files = list(self.narrowpeak_dir.glob("*.narrowPeak"))
        
        if not narrowpeak_files:
            logger.warning("No narrowPeak files found")
            return pd.DataFrame()
        
        # Parse filenames to extract metadata
        samples = []
        for np_file in narrowpeak_files:
            filename = np_file.stem
            
            # Try to parse BOCA filename format
            # Expected format might be like: brain_region_celltype.narrowPeak
            parts = filename.split('_')
            
            sample_info = {
                'filename': np_file.name,
                'filepath': str(np_file),
                'sample_id': filename,
                'file_size_mb': np_file.stat().st_size / (1024 * 1024)
            }
            
            # Try to extract metadata from filename
            if len(parts) >= 2:
                sample_info['region'] = parts[0] if len(parts) > 0 else 'unknown'
                sample_info['cell_type'] = parts[1] if len(parts) > 1 else 'unknown'
            
            samples.append(sample_info)
        
        df = pd.DataFrame(samples)
        
        # Save mapping
        mapping_file = self.download_dir / "boca_file_mapping.csv"
        df.to_csv(mapping_file, index=False)
        logger.info(f"Saved file mapping to {mapping_file}")
        
        return df


def main():
    parser = argparse.ArgumentParser(description='Download BOCA peak files')
    parser.add_argument('--genome', choices=['hg19', 'hg38'], default='hg19',
                       help='Genome build (default: hg19)')
    parser.add_argument('--no-consensus', action='store_true',
                       help='Skip consensus peaks download')
    parser.add_argument('--no-counts', action='store_true',
                       help='Skip count matrices download')
    parser.add_argument('--base-dir', default='.', help='Base directory (default: current)')
    
    args = parser.parse_args()
    
    # Initialize downloader
    downloader = BOCAPeakDownloader(base_dir=args.base_dir)
    
    # Download files
    success = downloader.download_peak_files(
        genome=args.genome,
        include_consensus=not args.no_consensus,
        include_counts=not args.no_counts
    )
    
    if success:
        # Convert to narrowPeak format
        converted = downloader.process_all_peaks()
        
        # Create sample mapping
        mapping_df = downloader.create_sample_mapping()
        
        logger.info(f"Processing complete:")
        logger.info(f"  - Converted {converted} files to narrowPeak format")
        logger.info(f"  - Created mapping for {len(mapping_df)} samples")
    else:
        logger.error("Download failed")


if __name__ == "__main__":
    main()
