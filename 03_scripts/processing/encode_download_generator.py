#!/usr/bin/env python3
"""
Generate download list for ENCODE files with specific filters.
"""

import requests
import pandas as pd
import json
import argparse
from urllib.parse import urlencode


def query_encode_api(file_format="bed narrowPeak", output_type="IDR thresholded peaks", 
                    assay_title=None, biosample_term_name=None, organism="Homo sapiens",
                    status="released", limit=1000):
    """
    Query ENCODE API for files matching specific criteria.
    
    Args:
        file_format: File format (e.g., "bed narrowPeak")
        output_type: Output type (e.g., "IDR thresholded peaks")
        assay_title: Assay type (e.g., "ATAC-seq", "ChIP-seq")
        biosample_term_name: Specific tissue/cell type
        organism: Organism name
        status: File status
        limit: Maximum number of results
    
    Returns:
        List of file information dictionaries
    """
    
    # Base ENCODE API URL
    base_url = "https://www.encodeproject.org/search/"
    
    # Build query parameters
    params = {
        "type": "File",
        "file_format": file_format,
        "output_type": output_type,
        "status": status,
        "limit": limit,
        "format": "json"
    }
    
    # Add optional filters
    if assay_title:
        params["assay_title"] = assay_title

    if biosample_term_name:
        params["biosample_ontology.term_name"] = biosample_term_name

    if organism:
        params["replicates.library.biosample.donor.organism.scientific_name"] = organism
    
    print(f"Querying ENCODE API...")
    print(f"Filters:")
    print(f"  File format: {file_format}")
    print(f"  Output type: {output_type}")
    if assay_title:
        print(f"  Assay: {assay_title}")
    if biosample_term_name:
        print(f"  Biosample: {biosample_term_name}")
    print(f"  Organism: {organism}")
    print(f"  Status: {status}")
    
    try:
        response = requests.get(base_url, params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        files = data.get("@graph", [])
        
        print(f"Found {len(files)} files")
        
        return files
        
    except requests.exceptions.RequestException as e:
        print(f"Error querying ENCODE API: {e}")
        return []


def extract_file_info(files):
    """
    Extract relevant information from ENCODE file records.
    
    Args:
        files: List of file records from ENCODE API
    
    Returns:
        DataFrame with file information
    """
    file_info = []
    
    for file_record in files:
        try:
            # Extract basic file information
            info = {
                "accession": file_record.get("accession", ""),
                "file_format": file_record.get("file_format", ""),
                "output_type": file_record.get("output_type", ""),
                "file_size": file_record.get("file_size", 0),
                "assembly": file_record.get("assembly", ""),
                "status": file_record.get("status", ""),
                "date_created": file_record.get("date_created", ""),
                "download_url": f"https://www.encodeproject.org{file_record.get('href', '')}"
            }
            
            # Extract dataset information
            dataset = file_record.get("dataset", {})
            if isinstance(dataset, str):
                # If dataset is just a path, extract accession
                info["dataset_accession"] = dataset.split("/")[-2] if "/" in dataset else dataset
                info["assay_title"] = ""
                info["biosample_term_name"] = ""
                info["biosample_type"] = ""
                info["lab"] = ""
            else:
                info["dataset_accession"] = dataset.get("accession", "")
                info["assay_title"] = dataset.get("assay_title", "")
                
                # Extract biosample information
                biosample_ontology = dataset.get("biosample_ontology", {})
                info["biosample_term_name"] = biosample_ontology.get("term_name", "")
                info["biosample_type"] = biosample_ontology.get("classification", "")
                
                # Extract lab information
                lab = dataset.get("lab", {})
                if isinstance(lab, dict):
                    info["lab"] = lab.get("title", "")
                else:
                    info["lab"] = ""
            
            # Generate download command
            info["wget_command"] = f"wget -O {info['accession']}.bed.gz '{info['download_url']}@@download/{info['accession']}.bed.gz'"
            
            file_info.append(info)
            
        except Exception as e:
            print(f"Error processing file record: {e}")
            continue
    
    return pd.DataFrame(file_info)


def generate_download_scripts(df, output_prefix="encode_download"):
    """
    Generate download scripts and file lists.
    
    Args:
        df: DataFrame with file information
        output_prefix: Prefix for output files
    """
    if len(df) == 0:
        print("No files to process")
        return
    
    # Save detailed file list
    csv_file = f"{output_prefix}_file_list.csv"
    df.to_csv(csv_file, index=False)
    print(f"Saved detailed file list to: {csv_file}")
    
    # Generate wget script
    wget_script = f"{output_prefix}_wget.sh"
    with open(wget_script, 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# ENCODE file download script\n")
        f.write(f"# Generated for {len(df)} files\n\n")
        
        for _, row in df.iterrows():
            f.write(f"{row['wget_command']}\n")
    
    print(f"Saved wget script to: {wget_script}")
    print(f"Make executable with: chmod +x {wget_script}")
    
    # Generate URL list for other download tools
    url_list = f"{output_prefix}_urls.txt"
    with open(url_list, 'w') as f:
        for _, row in df.iterrows():
            download_url = f"{row['download_url']}@@download/{row['accession']}.bed.gz"
            f.write(f"{download_url}\n")
    
    print(f"Saved URL list to: {url_list}")
    
    # Generate summary
    print(f"\nSummary:")
    print(f"Total files: {len(df)}")
    print(f"Total size: {df['file_size'].sum() / (1024**3):.2f} GB")
    
    if 'assay_title' in df.columns:
        print(f"\nAssay distribution:")
        assay_counts = df['assay_title'].value_counts()
        for assay, count in assay_counts.head(10).items():
            print(f"  {assay}: {count}")
    
    if 'biosample_term_name' in df.columns:
        print(f"\nTop biosamples:")
        biosample_counts = df['biosample_term_name'].value_counts()
        for biosample, count in biosample_counts.head(10).items():
            print(f"  {biosample}: {count}")


def main():
    parser = argparse.ArgumentParser(description='Generate ENCODE download list')
    parser.add_argument('--file-format', default='bed narrowPeak',
                       help='File format (default: "bed narrowPeak")')
    parser.add_argument('--output-type', default='IDR thresholded peaks',
                       help='Output type (default: "IDR thresholded peaks")')
    parser.add_argument('--assay', help='Assay title (e.g., "ATAC-seq", "ChIP-seq")')
    parser.add_argument('--biosample', help='Biosample term name (e.g., "heart left ventricle")')
    parser.add_argument('--organism', default='Homo sapiens',
                       help='Organism (default: "Homo sapiens")')
    parser.add_argument('--limit', type=int, default=1000,
                       help='Maximum number of results (default: 1000)')
    parser.add_argument('--output-prefix', default='encode_download',
                       help='Output file prefix (default: "encode_download")')
    
    args = parser.parse_args()
    
    # Query ENCODE API
    files = query_encode_api(
        file_format=args.file_format,
        output_type=args.output_type,
        assay_title=args.assay,
        biosample_term_name=args.biosample,
        organism=args.organism,
        limit=args.limit
    )
    
    if not files:
        print("No files found matching criteria")
        return
    
    # Extract file information
    df = extract_file_info(files)
    
    if len(df) == 0:
        print("No valid file information extracted")
        return
    
    # Generate download scripts
    generate_download_scripts(df, args.output_prefix)
    
    print(f"\nExample usage:")
    print(f"  # Download all files:")
    print(f"  bash {args.output_prefix}_wget.sh")
    print(f"  ")
    print(f"  # Or use curl with URL list:")
    print(f"  cat {args.output_prefix}_urls.txt | xargs -n 1 curl -O")


if __name__ == "__main__":
    main()
