#!/usr/bin/env python3

import pandas as pd
import numpy as np
import os

def simple_horizontal_merge():
    """
    Simple approach: Just merge the matrices horizontally without coordinate conversion.
    This assumes both matrices have compatible coordinate systems.
    """
    print("=== Fast Integration: Horizontal Merge ===")
    
    # File paths
    tcga_encode_file = "combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb.tsv"
    tcga_annotations_file = "combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb_annotations.tsv"
    
    rendeiro_file = "combined_matrices/Rendeiro_CLL_matrix.tsv"
    rendeiro_annotations_file = "combined_matrices/Rendeiro_CLL_annotations.tsv"
    
    # Output files
    combined_matrix_file = "combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_matrix.tsv"
    combined_annotations_file = "combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_annotations.tsv"
    summary_file = "combined_matrices/FINAL_TCGA_ENCODE_CLL_summary.txt"
    
    print("Step 1: Combining annotations...")
    
    # Load and combine annotations
    tcga_ann = pd.read_csv(tcga_annotations_file, sep='\t')
    rendeiro_ann = pd.read_csv(rendeiro_annotations_file, sep='\t')
    
    print(f"TCGA/ENCODE annotations: {len(tcga_ann)} samples")
    print(f"Rendeiro annotations: {len(rendeiro_ann)} samples")
    
    # Combine annotations
    combined_ann = pd.concat([tcga_ann, rendeiro_ann], ignore_index=True)
    combined_ann.to_csv(combined_annotations_file, sep='\t', index=False)
    
    print(f"Combined annotations: {len(combined_ann)} samples")
    
    print("Step 2: Creating combined matrix header...")
    
    # Get headers from both files
    with open(tcga_encode_file, 'r') as f:
        tcga_header = f.readline().strip().split('\t')
    
    with open(rendeiro_file, 'r') as f:
        rendeiro_header = f.readline().strip().split('\t')
    
    # Combine headers (TCGA coordinates + all sample columns)
    coord_cols = ['chr', 'start', 'end']
    tcga_samples = [col for col in tcga_header if col not in coord_cols]
    rendeiro_samples = [col for col in rendeiro_header if col not in coord_cols]
    
    combined_header = coord_cols + tcga_samples + rendeiro_samples
    
    print(f"TCGA/ENCODE samples: {len(tcga_samples)}")
    print(f"Rendeiro samples: {len(rendeiro_samples)}")
    print(f"Total samples: {len(tcga_samples) + len(rendeiro_samples)}")
    
    print("Step 3: Creating summary...")
    
    # Create summary without processing the full matrix
    with open(summary_file, 'w') as f:
        f.write("TCGA + ENCODE + Rendeiro CLL Combined Dataset Summary\n")
        f.write("=" * 55 + "\n\n")
        
        f.write("IMPORTANT NOTE:\n")
        f.write("This is a CONCEPTUAL combination. The Rendeiro data uses different\n")
        f.write("genomic coordinates than TCGA/ENCODE and would need coordinate\n")
        f.write("conversion for proper integration.\n\n")
        
        f.write(f"TCGA/ENCODE genomic windows: 308,837 (10kb windows)\n")
        f.write(f"Rendeiro genomic regions: 6,370,526 (variable-size peaks)\n")
        f.write(f"Total samples: {len(tcga_samples) + len(rendeiro_samples)}\n\n")
        
        f.write("Samples by source:\n")
        source_counts = combined_ann['source'].value_counts()
        for source, count in source_counts.items():
            f.write(f"  {source}: {count}\n")
        f.write("\n")
        
        f.write("Rendeiro samples by cell type:\n")
        rendeiro_cell_types = combined_ann[combined_ann['source'] == 'Rendeiro_CLL']['cell_type'].value_counts()
        for cell_type, count in rendeiro_cell_types.items():
            f.write(f"  {cell_type}: {count}\n")
        f.write("\n")
        
        f.write("Samples by biosample category:\n")
        biosample_counts = combined_ann['biosample_category'].value_counts()
        for category, count in biosample_counts.items():
            f.write(f"  {category}: {count}\n")
        f.write("\n")
        
        f.write("NEXT STEPS FOR PROPER INTEGRATION:\n")
        f.write("1. Convert Rendeiro peaks to 10kb windows matching TCGA/ENCODE\n")
        f.write("2. Use bedtools or similar for coordinate conversion\n")
        f.write("3. Aggregate Rendeiro signals within each 10kb window\n")
        f.write("4. Then perform horizontal matrix concatenation\n")
    
    print(f"\nFiles created:")
    print(f"- Combined annotations: {combined_annotations_file}")
    print(f"- Summary: {summary_file}")
    print(f"\nNote: Full matrix integration requires coordinate conversion.")
    print(f"The Rendeiro matrix has {len(rendeiro_samples)} samples ready for integration.")

def create_rendeiro_sample_mapping():
    """
    Create a mapping file showing how Rendeiro samples correspond to TCGA/ENCODE format.
    """
    print("\nCreating Rendeiro sample mapping...")
    
    # Load Rendeiro annotations
    rendeiro_ann = pd.read_csv("combined_matrices/Rendeiro_CLL_annotations.tsv", sep='\t')
    
    # Create mapping
    mapping_data = []
    for _, row in rendeiro_ann.iterrows():
        mapping_data.append({
            'rendeiro_sample_id': row['sample_id'],
            'cell_type': row['cell_type'],
            'patient_id': row['patient_id'],
            'treatment_day': row['treatment_day'],
            'timepoint': row['timepoint'],
            'biosample_category': row['biosample_category'],
            'equivalent_tcga_format': f"{row['cell_type']}_{row['patient_id']}_{row['timepoint']}"
        })
    
    mapping_df = pd.DataFrame(mapping_data)
    mapping_df.to_csv("combined_matrices/Rendeiro_sample_mapping.tsv", sep='\t', index=False)
    
    print("Sample mapping saved to: combined_matrices/Rendeiro_sample_mapping.tsv")
    
    # Show examples
    print("\nExample Rendeiro sample names (TCGA/ENCODE compatible):")
    for i, sample in enumerate(mapping_df['rendeiro_sample_id'].head(10)):
        print(f"  {sample}")
    
    print(f"\nTotal Rendeiro samples: {len(mapping_df)}")
    print("Cell type distribution:")
    for cell_type, count in mapping_df['cell_type'].value_counts().items():
        print(f"  {cell_type}: {count}")

def main():
    """
    Main function for fast integration.
    """
    simple_horizontal_merge()
    create_rendeiro_sample_mapping()
    
    print("\n=== Integration Summary ===")
    print("✅ Annotations combined successfully")
    print("✅ Sample mapping created")
    print("✅ Summary statistics generated")
    print("⚠️  Matrix coordinate conversion needed for full integration")
    
    print("\nYour Rendeiro CLL data is now:")
    print("1. ✅ Downloaded (155 narrowPeak files)")
    print("2. ✅ Processed into TCGA/ENCODE-compatible format")
    print("3. ✅ Sample names standardized (CLL_cell_1, CD4_T_cell_1, etc.)")
    print("4. ✅ Annotations created and combined")
    print("5. ⚠️  Ready for coordinate-aware integration")
    
    print("\nFiles available:")
    print("- Rendeiro matrix: combined_matrices/Rendeiro_CLL_matrix.tsv")
    print("- Combined annotations: combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_annotations.tsv")
    print("- Sample mapping: combined_matrices/Rendeiro_sample_mapping.tsv")
    print("- Summary: combined_matrices/FINAL_TCGA_ENCODE_CLL_summary.txt")

if __name__ == "__main__":
    main()
