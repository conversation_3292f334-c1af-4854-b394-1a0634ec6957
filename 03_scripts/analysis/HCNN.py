import torch
import torch.nn as nn
import torch.nn.functional as F

class MultiScaleMultiOutputCNN(nn.Module):
    def __init__(self,
                 in_channels_1mb=1, in_channels_100kb=1,
                 in_channels_10kb=1, in_channels_1kb=1,
                 num_filters=16,
                 hidden_dim=64):
        """
        A multi-scale CNN with separate output heads for each scale.
        """

        super().__init__()

        # ----- 1MB Branch -----
        self.branch_1mb = nn.Sequential(
            nn.Conv1d(in_channels_1mb, num_filters, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),  # compress along length dimension
            nn.Flatten()  # -> shape [batch, num_filters]
        )

        # ----- 100KB Branch -----
        self.branch_100kb = nn.Sequential(
            nn.Conv1d(in_channels_100kb, num_filters, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten()
        )

        # ----- 10KB Branch -----
        self.branch_10kb = nn.Sequential(
            nn.Conv1d(in_channels_10kb, num_filters, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten()
        )

        # ----- 1KB Branch -----
        self.branch_1kb = nn.Sequential(
            nn.Conv1d(in_channels_1kb, num_filters, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten()
        )

        # Shared hidden layer after concatenation
        # We'll combine features from all 4 branches -> total dimension = num_filters*4
        concat_dim = num_filters * 4
        self.shared_fc = nn.Sequential(
            nn.Linear(concat_dim, hidden_dim),
            nn.ReLU(),
        )

        # Now separate heads for each scale's mutation rate:
        self.head_1mb   = nn.Linear(hidden_dim, 1)
        self.head_100kb = nn.Linear(hidden_dim, 1)
        self.head_10kb  = nn.Linear(hidden_dim, 1)
        self.head_1kb   = nn.Linear(hidden_dim, 1)

    def forward(self, x_1mb, x_100kb, x_10kb, x_1kb):
        """
        x_1mb:    [batch, in_channels_1mb, length_1mb]
        x_100kb:  [batch, in_channels_100kb, length_100kb]
        x_10kb:   [batch, in_channels_10kb, length_10kb]
        x_1kb:    [batch, in_channels_1kb, length_1kb]
        """

        # Extract features from each scale
        f1mb   = self.branch_1mb(x_1mb)      # [batch, num_filters]
        f100kb = self.branch_100kb(x_100kb)  # [batch, num_filters]
        f10kb  = self.branch_10kb(x_10kb)    # ...
        f1kb   = self.branch_1kb(x_1kb)      # ...

        # Concatenate
        merged = torch.cat([f1mb, f100kb, f10kb, f1kb], dim=1)  # [batch, num_filters*4]

        # Shared hidden representation
        shared = self.shared_fc(merged)  # [batch, hidden_dim]

        # Separate heads for each scale
        out_1mb   = self.head_1mb(shared).squeeze(-1)   # [batch]
        out_100kb = self.head_100kb(shared).squeeze(-1) # [batch]
        out_10kb  = self.head_10kb(shared).squeeze(-1)  # [batch]
        out_1kb   = self.head_1kb(shared).squeeze(-1)   # [batch]

        # Return all 4 at once
        return out_1mb, out_100kb, out_10kb, out_1kb


# ---- Example training loop snippet ----
if __name__ == "__main__":
    # Suppose we have a batch size = 8
    batch_size = 8

    # Dummy lengths
    len_1mb   = 100
    len_100kb = 200
    len_10kb  = 500
    len_1kb   = 1000

    # Create random inputs for CA
    x_1mb   = torch.randn(batch_size, 1, len_1mb)
    x_100kb = torch.randn(batch_size, 1, len_100kb)
    x_10kb  = torch.randn(batch_size, 1, len_10kb)
    x_1kb   = torch.randn(batch_size, 1, len_1kb)

    # Create random targets for mutation rates (1D scalar per sample)
    y_1mb   = torch.randn(batch_size)
    y_100kb = torch.randn(batch_size)
    y_10kb  = torch.randn(batch_size)
    y_1kb   = torch.randn(batch_size)

    model = MultiScaleMultiOutputCNN()

    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    criterion = nn.MSELoss()

    # Forward pass
    out_1mb, out_100kb, out_10kb, out_1kb = model(x_1mb, x_100kb, x_10kb, x_1kb)

    # Compute losses at each scale
    loss_1mb   = criterion(out_1mb, y_1mb)
    loss_100kb = criterion(out_100kb, y_100kb)
    loss_10kb  = criterion(out_10kb, y_10kb)
    loss_1kb   = criterion(out_1kb, y_1kb)

    # Combine (e.g., sum)
    total_loss = loss_1mb + loss_100kb + loss_10kb + loss_1kb

    # Backprop
    optimizer.zero_grad()
    total_loss.backward()
    optimizer.step()

    print("Total Loss =", total_loss.item())
    print("Loss at 1MB scale   =", loss_1mb.item())
    print("Loss at 100KB scale =", loss_100kb.item())
    print("Loss at 10KB scale  =", loss_10kb.item())
    print("Loss at 1KB scale   =", loss_1kb.item())
