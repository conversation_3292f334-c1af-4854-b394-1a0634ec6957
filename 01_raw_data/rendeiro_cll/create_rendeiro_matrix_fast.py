#!/usr/bin/env python3

import os
import pandas as pd
import numpy as np
import gzip
from pathlib import Path
import argparse
from collections import Counter

def create_rendeiro_sample_names(metadata_df):
    """
    Create standardized sample names for Rendeiro data to match TCGA/ENCODE format.
    Format: CELLTYPE_NUMBER (e.g., CLL_cell_1, CD4_T_cell_1, B_cell_1)
    """
    # Count samples per cell type to create numbered series
    cell_type_counts = {}
    sample_names = []
    
    for _, row in metadata_df.iterrows():
        # Clean and standardize cell type name
        cell_type = row['cell_type'].replace('_1', '').replace('_2', '')
        
        # Map to standardized cell type names matching ENCODE format
        if cell_type == 'CLL':
            standard_cell_type = 'CLL_cell'
        elif cell_type == 'CD4':
            standard_cell_type = 'CD4_T_cell'
        elif cell_type == 'CD8':
            standard_cell_type = 'CD8_T_cell'
        elif cell_type == 'Bcell':
            standard_cell_type = 'B_cell'
        elif cell_type == 'NK':
            standard_cell_type = 'natural_killer_cell'
        elif cell_type == 'Mono':
            standard_cell_type = 'monocyte'
        else:
            standard_cell_type = f"{cell_type}_cell"
        
        # Count samples per cell type
        if standard_cell_type not in cell_type_counts:
            cell_type_counts[standard_cell_type] = 0
        cell_type_counts[standard_cell_type] += 1
        
        # Create sample name: CELLTYPE_NUMBER
        sample_name = f"{standard_cell_type}_{cell_type_counts[standard_cell_type]}"
        sample_names.append(sample_name)
    
    return sample_names

def process_narrowpeak_to_windows_fast(narrowpeak_file, genomic_windows, sample_name):
    """
    Fast processing using vectorized operations and interval overlap.
    """
    print(f"Processing {sample_name}...")
    
    # Read narrowPeak file
    columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
               'signalValue', 'pValue', 'qValue', 'peak']
    
    if narrowpeak_file.endswith('.gz'):
        with gzip.open(narrowpeak_file, 'rt') as f:
            peaks_df = pd.read_csv(f, sep='\t', header=None, names=columns)
    else:
        peaks_df = pd.read_csv(narrowpeak_file, sep='\t', header=None, names=columns)
    
    print(f"  Loaded {len(peaks_df)} peaks")
    
    # Initialize signal array
    signal_values = np.zeros(len(genomic_windows))
    
    # Process by chromosome for efficiency
    for chrom in genomic_windows['chr'].unique():
        if chrom not in peaks_df['chr'].values:
            continue
            
        # Get windows and peaks for this chromosome
        chrom_windows = genomic_windows[genomic_windows['chr'] == chrom].copy()
        chrom_peaks = peaks_df[peaks_df['chr'] == chrom].copy()
        
        if len(chrom_peaks) == 0:
            continue
        
        print(f"    Processing chromosome {chrom}: {len(chrom_windows)} windows, {len(chrom_peaks)} peaks")
        
        # Use pandas interval operations for fast overlap detection
        # Create intervals for windows and peaks
        window_intervals = pd.IntervalIndex.from_arrays(
            chrom_windows['start'], chrom_windows['end'], closed='both'
        )
        
        # For each peak, find overlapping windows
        for _, peak in chrom_peaks.iterrows():
            peak_interval = pd.Interval(peak['start'], peak['end'], closed='both')
            
            # Find overlapping windows
            overlapping_mask = window_intervals.overlaps(peak_interval)
            overlapping_indices = chrom_windows.index[overlapping_mask]
            
            # Add signal to overlapping windows
            for window_idx in overlapping_indices:
                original_idx = genomic_windows.index.get_loc(window_idx)
                signal_values[original_idx] += peak['signalValue']
        
        # Average signal for windows with multiple overlapping peaks
        for window_idx in chrom_windows.index:
            original_idx = genomic_windows.index.get_loc(window_idx)
            window_interval = pd.Interval(
                chrom_windows.loc[window_idx, 'start'], 
                chrom_windows.loc[window_idx, 'end'], 
                closed='both'
            )
            
            # Count overlapping peaks for averaging
            overlapping_peaks = 0
            for _, peak in chrom_peaks.iterrows():
                peak_interval = pd.Interval(peak['start'], peak['end'], closed='both')
                if window_interval.overlaps(peak_interval):
                    overlapping_peaks += 1
            
            if overlapping_peaks > 1:
                signal_values[original_idx] /= overlapping_peaks
    
    print(f"  Windows with signal: {(signal_values > 0).sum()}")
    print(f"  Signal range: {signal_values.min():.4f} - {signal_values.max():.4f}")
    
    return signal_values

def create_rendeiro_matrix_simple(narrowpeak_dir, metadata_file, output_file):
    """
    Create Rendeiro matrix using a simpler approach - just create the matrix without TCGA/ENCODE coordinates.
    """
    print("=== Creating Rendeiro Matrix (Standalone) ===")
    
    # Load metadata
    metadata_df = pd.read_csv(metadata_file, sep='\t')
    print(f"Processing {len(metadata_df)} Rendeiro samples...")
    
    # Create sample names
    sample_names = create_rendeiro_sample_names(metadata_df)
    
    # Create a union of all peaks across samples to define genomic windows
    print("Creating union of all peaks...")
    all_peaks = []
    
    for _, row in metadata_df.iterrows():
        narrowpeak_file = os.path.join(narrowpeak_dir, row['filename'])
        if os.path.exists(narrowpeak_file):
            # Read peaks
            columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
                       'signalValue', 'pValue', 'qValue', 'peak']
            
            if narrowpeak_file.endswith('.gz'):
                with gzip.open(narrowpeak_file, 'rt') as f:
                    peaks_df = pd.read_csv(f, sep='\t', header=None, names=columns)
            else:
                peaks_df = pd.read_csv(narrowpeak_file, sep='\t', header=None, names=columns)
            
            # Keep only coordinates
            peaks_coords = peaks_df[['chr', 'start', 'end']].copy()
            all_peaks.append(peaks_coords)
    
    # Combine all peaks and create union
    union_peaks = pd.concat(all_peaks, ignore_index=True)
    union_peaks = union_peaks.drop_duplicates().sort_values(['chr', 'start', 'end']).reset_index(drop=True)
    
    print(f"Created union of {len(union_peaks)} unique peak regions")
    
    # Initialize results matrix
    results_df = union_peaks.copy()
    
    # Process each sample
    for i, (_, row) in enumerate(metadata_df.iterrows()):
        narrowpeak_file = os.path.join(narrowpeak_dir, row['filename'])
        sample_name = sample_names[i]
        
        print(f"Processing {sample_name} ({i+1}/{len(metadata_df)})...")
        
        if os.path.exists(narrowpeak_file):
            # Read peaks with signal values
            columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
                       'signalValue', 'pValue', 'qValue', 'peak']
            
            if narrowpeak_file.endswith('.gz'):
                with gzip.open(narrowpeak_file, 'rt') as f:
                    peaks_df = pd.read_csv(f, sep='\t', header=None, names=columns)
            else:
                peaks_df = pd.read_csv(narrowpeak_file, sep='\t', header=None, names=columns)
            
            # Merge with union peaks to get signal values
            merged = union_peaks.merge(
                peaks_df[['chr', 'start', 'end', 'signalValue']], 
                on=['chr', 'start', 'end'], 
                how='left'
            )
            
            # Fill missing values with 0
            results_df[sample_name] = merged['signalValue'].fillna(0)
            
        else:
            print(f"Warning: File not found: {narrowpeak_file}")
            results_df[sample_name] = 0.0
    
    # Save results
    print(f"Saving Rendeiro matrix to {output_file}...")
    results_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"Rendeiro matrix created: {len(results_df)} regions × {len(results_df.columns)} columns")
    
    return results_df, sample_names

def create_rendeiro_annotations(metadata_file, sample_names, output_file):
    """
    Create Rendeiro sample annotations.
    """
    print("Creating Rendeiro sample annotations...")
    
    # Load Rendeiro metadata
    rendeiro_df = pd.read_csv(metadata_file, sep='\t')
    
    # Create Rendeiro annotations
    rendeiro_annotations = []
    
    for i, (_, row) in enumerate(rendeiro_df.iterrows()):
        sample_name = sample_names[i]
        
        # Extract standardized cell type from sample name
        cell_type_from_name = sample_name.rsplit('_', 1)[0]  # Remove the number suffix
        
        # Map cell types to biosample categories
        original_cell_type = row['cell_type'].replace('_1', '').replace('_2', '')
        if original_cell_type in ['CLL']:
            biosample_category = 'tumor_tissue'
            source_type = 'CLL_tumor'
        elif original_cell_type in ['CD4', 'CD8', 'NK', 'Bcell', 'Mono']:
            biosample_category = 'immune_cell'
            source_type = 'CLL_immune'
        else:
            biosample_category = 'other_cell'
            source_type = 'CLL_other'
        
        rendeiro_annotations.append({
            'sample_id': sample_name,
            'source': 'Rendeiro_CLL',
            'sample_type': source_type,
            'cancer_type': 'CLL',
            'cell_type': cell_type_from_name,
            'biosample_category': biosample_category,
            'patient_id': row['patient_id'],
            'treatment_day': row['treatment_day'],
            'timepoint': row['timepoint']
        })
    
    # Save annotations
    rendeiro_ann_df = pd.DataFrame(rendeiro_annotations)
    rendeiro_ann_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"Rendeiro annotations saved: {len(rendeiro_ann_df)} samples")
    
    return rendeiro_ann_df

def main():
    """
    Main function to create Rendeiro matrix.
    """
    # Set paths
    narrowpeak_dir = "narrowpeak"
    metadata_file = "processed_data/sample_metadata.tsv"
    
    output_matrix = "../../02_processed_data/combined_matrices/Rendeiro_CLL_matrix.tsv"
    output_annotations = "../../02_processed_data/combined_matrices/Rendeiro_CLL_annotations.tsv"
    
    print("=== Creating Rendeiro CLL Matrix ===")
    
    # Create Rendeiro matrix
    rendeiro_matrix, sample_names = create_rendeiro_matrix_simple(
        narrowpeak_dir, metadata_file, output_matrix
    )
    
    # Create annotations
    rendeiro_annotations = create_rendeiro_annotations(
        metadata_file, sample_names, output_annotations
    )
    
    print("\n=== Processing Complete ===")
    print(f"Rendeiro matrix: {output_matrix}")
    print(f"Rendeiro annotations: {output_annotations}")
    print(f"Total samples: {len(sample_names)}")
    
    # Summary statistics
    print("\nSample name examples:")
    for i, name in enumerate(sample_names[:10]):
        print(f"  {name}")
    if len(sample_names) > 10:
        print(f"  ... and {len(sample_names) - 10} more")
    
    print("\nCell type distribution:")
    cell_types = [name.rsplit('_', 1)[0] for name in sample_names]
    for cell_type, count in Counter(cell_types).items():
        print(f"  {cell_type}: {count} samples")

if __name__ == "__main__":
    main()
