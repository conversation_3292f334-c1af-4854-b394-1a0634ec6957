#!/usr/bin/env python3

import os
import pandas as pd
import numpy as np
import gzip
from pathlib import Path
import argparse

def create_rendeiro_sample_names(metadata_df):
    """
    Create standardized sample names for Rendeiro data to match TCGA/ENCODE format.
    Format: CELLTYPE_NUMBER (e.g., CLL_cell_1, CD4_T_cell_1, B_cell_1)
    """
    # Count samples per cell type to create numbered series
    cell_type_counts = {}
    sample_names = []
    
    for _, row in metadata_df.iterrows():
        # Clean and standardize cell type name
        cell_type = row['cell_type'].replace('_1', '').replace('_2', '')
        
        # Map to standardized cell type names matching ENCODE format
        if cell_type == 'CLL':
            standard_cell_type = 'CLL_cell'
        elif cell_type == 'CD4':
            standard_cell_type = 'CD4_T_cell'
        elif cell_type == 'CD8':
            standard_cell_type = 'CD8_T_cell'
        elif cell_type == 'Bcell':
            standard_cell_type = 'B_cell'
        elif cell_type == 'NK':
            standard_cell_type = 'natural_killer_cell'
        elif cell_type == 'Mono':
            standard_cell_type = 'monocyte'
        else:
            standard_cell_type = f"{cell_type}_cell"
        
        # Count samples per cell type
        if standard_cell_type not in cell_type_counts:
            cell_type_counts[standard_cell_type] = 0
        cell_type_counts[standard_cell_type] += 1
        
        # Create sample name: CELLTYPE_NUMBER
        sample_name = f"{standard_cell_type}_{cell_type_counts[standard_cell_type]}"
        sample_names.append(sample_name)
    
    return sample_names

def process_rendeiro_narrowpeak_to_windows(narrowpeak_file, genomic_windows, sample_name):
    """
    Process a single Rendeiro narrowPeak file to genomic windows.
    """
    print(f"Processing {sample_name}...")
    
    # Read narrowPeak file
    columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
               'signalValue', 'pValue', 'qValue', 'peak']
    
    if narrowpeak_file.endswith('.gz'):
        with gzip.open(narrowpeak_file, 'rt') as f:
            peaks_df = pd.read_csv(f, sep='\t', header=None, names=columns)
    else:
        peaks_df = pd.read_csv(narrowpeak_file, sep='\t', header=None, names=columns)
    
    print(f"  Loaded {len(peaks_df)} peaks")
    
    # Initialize signal array
    signal_values = np.zeros(len(genomic_windows))
    
    # Process by chromosome for efficiency
    for chrom in genomic_windows['chr'].unique():
        if chrom not in peaks_df['chr'].values:
            continue
            
        # Get windows and peaks for this chromosome
        chrom_windows = genomic_windows[genomic_windows['chr'] == chrom].reset_index()
        chrom_peaks = peaks_df[peaks_df['chr'] == chrom]
        
        if len(chrom_peaks) == 0:
            continue
        
        print(f"    Processing chromosome {chrom}: {len(chrom_windows)} windows, {len(chrom_peaks)} peaks")
        
        # For each window, find overlapping peaks
        for idx, window in chrom_windows.iterrows():
            original_idx = window['index']  # Original index in full genomic_windows
            
            # Check for overlap: peak_start < window_end AND peak_end > window_start
            overlapping = chrom_peaks[
                (chrom_peaks['start'] < window['end']) & 
                (chrom_peaks['end'] > window['start'])
            ]
            
            if len(overlapping) > 0:
                # Use mean signalValue for overlapping peaks
                signal_values[original_idx] = overlapping['signalValue'].mean()
    
    print(f"  Windows with signal: {(signal_values > 0).sum()}")
    print(f"  Signal range: {signal_values.min():.4f} - {signal_values.max():.4f}")
    
    return signal_values

def create_rendeiro_matrix_efficient(narrowpeak_dir, metadata_file, tcga_encode_file, output_file):
    """
    Create Rendeiro matrix matching TCGA/ENCODE format efficiently.
    """
    print("=== Creating Rendeiro Matrix in TCGA/ENCODE Format ===")
    
    # Load metadata
    metadata_df = pd.read_csv(metadata_file, sep='\t')
    print(f"Processing {len(metadata_df)} Rendeiro samples...")
    
    # Create sample names
    sample_names = create_rendeiro_sample_names(metadata_df)
    
    # Load genomic windows from TCGA/ENCODE matrix (coordinates only)
    print("Loading genomic windows from TCGA/ENCODE matrix...")
    coords_df = pd.read_csv(tcga_encode_file, sep='\t', usecols=['chr', 'start', 'end'])
    print(f"Found {len(coords_df)} genomic windows")
    
    # Initialize results matrix with coordinates
    results_df = coords_df.copy()
    
    # Process each sample
    for i, (_, row) in enumerate(metadata_df.iterrows()):
        narrowpeak_file = os.path.join(narrowpeak_dir, row['filename'])
        sample_name = sample_names[i]
        
        if os.path.exists(narrowpeak_file):
            signal_values = process_rendeiro_narrowpeak_to_windows(
                narrowpeak_file, coords_df, sample_name
            )
            results_df[sample_name] = signal_values
        else:
            print(f"Warning: File not found: {narrowpeak_file}")
            results_df[sample_name] = 0.0
    
    # Save results
    print(f"Saving Rendeiro matrix to {output_file}...")
    results_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"Rendeiro matrix created: {len(results_df)} windows × {len(results_df.columns)} columns")
    
    return results_df, sample_names

def create_rendeiro_annotations(metadata_file, sample_names, output_file):
    """
    Create Rendeiro sample annotations matching TCGA/ENCODE format.
    """
    print("Creating Rendeiro sample annotations...")
    
    # Load Rendeiro metadata
    rendeiro_df = pd.read_csv(metadata_file, sep='\t')
    
    # Create Rendeiro annotations
    rendeiro_annotations = []
    
    for i, (_, row) in enumerate(rendeiro_df.iterrows()):
        sample_name = sample_names[i]
        
        # Extract standardized cell type from sample name
        cell_type_from_name = sample_name.rsplit('_', 1)[0]  # Remove the number suffix
        
        # Map cell types to biosample categories
        original_cell_type = row['cell_type'].replace('_1', '').replace('_2', '')
        if original_cell_type in ['CLL']:
            biosample_category = 'tumor_tissue'
            source_type = 'CLL_tumor'
        elif original_cell_type in ['CD4', 'CD8', 'NK', 'Bcell', 'Mono']:
            biosample_category = 'immune_cell'
            source_type = 'CLL_immune'
        else:
            biosample_category = 'other_cell'
            source_type = 'CLL_other'
        
        rendeiro_annotations.append({
            'sample_id': sample_name,
            'source': 'Rendeiro_CLL',
            'sample_type': source_type,
            'cancer_type': 'CLL',
            'cell_type': cell_type_from_name,
            'biosample_category': biosample_category,
            'patient_id': row['patient_id'],
            'treatment_day': row['treatment_day'],
            'timepoint': row['timepoint']
        })
    
    # Save annotations
    rendeiro_ann_df = pd.DataFrame(rendeiro_annotations)
    rendeiro_ann_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"Rendeiro annotations saved: {len(rendeiro_ann_df)} samples")
    
    return rendeiro_ann_df

def main():
    """
    Main function to create Rendeiro matrix in TCGA/ENCODE format.
    """
    # Set paths
    tcga_encode_file = "../../02_processed_data/combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb.tsv"
    
    narrowpeak_dir = "narrowpeak"
    metadata_file = "processed_data/sample_metadata.tsv"
    
    output_matrix = "../../02_processed_data/combined_matrices/Rendeiro_CLL_matrix_10kb.tsv"
    output_annotations = "../../02_processed_data/combined_matrices/Rendeiro_CLL_annotations.tsv"
    
    print("=== Creating Rendeiro CLL Matrix in TCGA/ENCODE Format ===")
    
    # Create Rendeiro matrix
    rendeiro_matrix, sample_names = create_rendeiro_matrix_efficient(
        narrowpeak_dir, metadata_file, tcga_encode_file, output_matrix
    )
    
    # Create annotations
    rendeiro_annotations = create_rendeiro_annotations(
        metadata_file, sample_names, output_annotations
    )
    
    print("\n=== Processing Complete ===")
    print(f"Rendeiro matrix: {output_matrix}")
    print(f"Rendeiro annotations: {output_annotations}")
    print(f"Total samples: {len(sample_names)}")
    
    # Summary statistics
    print("\nSample name examples:")
    for i, name in enumerate(sample_names[:10]):
        print(f"  {name}")
    if len(sample_names) > 10:
        print(f"  ... and {len(sample_names) - 10} more")
    
    print("\nCell type distribution:")
    cell_types = [name.rsplit('_', 1)[0] for name in sample_names]
    from collections import Counter
    for cell_type, count in Counter(cell_types).items():
        print(f"  {cell_type}: {count} samples")

if __name__ == "__main__":
    main()
