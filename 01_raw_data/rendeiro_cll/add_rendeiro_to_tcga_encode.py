#!/usr/bin/env python3

import os
import pandas as pd
import numpy as np
import gzip
from pathlib import Path
import argparse

def create_genomic_windows_from_tcga_encode(tcga_encode_file):
    """
    Extract genomic windows from existing TCGA/ENCODE matrix.
    """
    print("Loading TCGA/ENCODE matrix to get genomic windows...")
    
    # Read just the coordinate columns to save memory
    coords_df = pd.read_csv(tcga_encode_file, sep='\t', usecols=['chr', 'start', 'end'])
    
    print(f"Found {len(coords_df)} genomic windows")
    print(f"Chromosomes: {sorted(coords_df['chr'].unique())}")
    
    # Create window ID for merging
    coords_df['window_id'] = coords_df['chr'] + ':' + coords_df['start'].astype(str) + '-' + coords_df['end'].astype(str)
    
    return coords_df

def process_rendeiro_narrowpeak_to_windows(narrowpeak_file, genomic_windows, sample_name):
    """
    Process a single Rendeiro narrowPeak file to genomic windows.
    """
    print(f"Processing {sample_name}...")
    
    # Read narrowPeak file
    columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
               'signalValue', 'pValue', 'qValue', 'peak']
    
    if narrowpeak_file.endswith('.gz'):
        with gzip.open(narrowpeak_file, 'rt') as f:
            peaks_df = pd.read_csv(f, sep='\t', header=None, names=columns)
    else:
        peaks_df = pd.read_csv(narrowpeak_file, sep='\t', header=None, names=columns)
    
    print(f"  Loaded {len(peaks_df)} peaks")
    
    # Initialize signal array
    signal_values = np.zeros(len(genomic_windows))
    
    # For each genomic window, calculate mean signal from overlapping peaks
    for i, (_, window) in enumerate(genomic_windows.iterrows()):
        if i % 10000 == 0:
            print(f"  Processing window {i}/{len(genomic_windows)}")
        
        # Find overlapping peaks
        chr_peaks = peaks_df[peaks_df['chr'] == window['chr']]
        
        if len(chr_peaks) == 0:
            continue
            
        # Check for overlap: peak_start < window_end AND peak_end > window_start
        overlapping = chr_peaks[
            (chr_peaks['start'] < window['end']) & 
            (chr_peaks['end'] > window['start'])
        ]
        
        if len(overlapping) > 0:
            # Use mean signalValue for overlapping peaks
            signal_values[i] = overlapping['signalValue'].mean()
    
    print(f"  Windows with signal: {(signal_values > 0).sum()}")
    print(f"  Signal range: {signal_values.min():.4f} - {signal_values.max():.4f}")
    
    return signal_values

def create_rendeiro_sample_names(metadata_df):
    """
    Create standardized sample names for Rendeiro data to match TCGA/ENCODE format.
    Format: CELLTYPE_NUMBER (e.g., CLL_cell_1, CD4_T_cell_1, B_cell_1)
    """
    # Count samples per cell type to create numbered series
    cell_type_counts = {}
    sample_names = []

    for _, row in metadata_df.iterrows():
        # Clean and standardize cell type name
        cell_type = row['cell_type'].replace('_1', '').replace('_2', '')

        # Map to standardized cell type names matching ENCODE format
        if cell_type == 'CLL':
            standard_cell_type = 'CLL_cell'
        elif cell_type == 'CD4':
            standard_cell_type = 'CD4_T_cell'
        elif cell_type == 'CD8':
            standard_cell_type = 'CD8_T_cell'
        elif cell_type == 'Bcell':
            standard_cell_type = 'B_cell'
        elif cell_type == 'NK':
            standard_cell_type = 'natural_killer_cell'
        elif cell_type == 'Mono':
            standard_cell_type = 'monocyte'
        else:
            standard_cell_type = f"{cell_type}_cell"

        # Count samples per cell type
        if standard_cell_type not in cell_type_counts:
            cell_type_counts[standard_cell_type] = 0
        cell_type_counts[standard_cell_type] += 1

        # Create sample name: CELLTYPE_NUMBER
        sample_name = f"{standard_cell_type}_{cell_type_counts[standard_cell_type]}"
        sample_names.append(sample_name)

    return sample_names

def process_all_rendeiro_samples(narrowpeak_dir, metadata_file, genomic_windows):
    """
    Process all Rendeiro samples to genomic windows.
    """
    # Load metadata
    metadata_df = pd.read_csv(metadata_file, sep='\t')
    print(f"Processing {len(metadata_df)} Rendeiro samples...")
    
    # Create sample names
    sample_names = create_rendeiro_sample_names(metadata_df)
    
    # Initialize results matrix
    results_matrix = pd.DataFrame(index=range(len(genomic_windows)))
    
    # Process each sample
    for i, (_, row) in enumerate(metadata_df.iterrows()):
        narrowpeak_file = os.path.join(narrowpeak_dir, row['filename'])
        sample_name = sample_names[i]
        
        if os.path.exists(narrowpeak_file):
            signal_values = process_rendeiro_narrowpeak_to_windows(
                narrowpeak_file, genomic_windows, sample_name
            )
            results_matrix[sample_name] = signal_values
        else:
            print(f"Warning: File not found: {narrowpeak_file}")
            results_matrix[sample_name] = 0.0
    
    return results_matrix

def add_rendeiro_to_tcga_encode_matrix(tcga_encode_file, rendeiro_matrix, output_file):
    """
    Add Rendeiro samples to existing TCGA/ENCODE matrix.
    """
    print("Loading TCGA/ENCODE matrix...")
    tcga_encode_df = pd.read_csv(tcga_encode_file, sep='\t')

    print(f"TCGA/ENCODE matrix: {len(tcga_encode_df)} windows × {len(tcga_encode_df.columns)} columns")
    print(f"Rendeiro matrix: {len(rendeiro_matrix)} windows × {len(rendeiro_matrix.columns)} columns")

    # Add Rendeiro columns to TCGA/ENCODE matrix
    for col in rendeiro_matrix.columns:
        tcga_encode_df[col] = rendeiro_matrix[col].values

    print(f"Combined matrix: {len(tcga_encode_df)} windows × {len(tcga_encode_df.columns)} columns")

    # Save combined matrix
    print(f"Saving combined matrix to {output_file}...")
    tcga_encode_df.to_csv(output_file, sep='\t', index=False)

    return output_file

def create_updated_annotations(original_annotations, rendeiro_metadata, output_file):
    """
    Create updated sample annotations including Rendeiro samples.
    """
    print("Creating updated sample annotations...")

    # Load original annotations
    orig_df = pd.read_csv(original_annotations, sep='\t')

    # Load Rendeiro metadata
    rendeiro_df = pd.read_csv(rendeiro_metadata, sep='\t')

    # Create Rendeiro annotations
    rendeiro_annotations = []
    sample_names = create_rendeiro_sample_names(rendeiro_df)

    for i, (_, row) in enumerate(rendeiro_df.iterrows()):
        sample_name = sample_names[i]

        # Extract standardized cell type from sample name
        cell_type_from_name = sample_name.rsplit('_', 1)[0]  # Remove the number suffix

        # Map cell types to biosample categories
        original_cell_type = row['cell_type'].replace('_1', '').replace('_2', '')
        if original_cell_type in ['CLL']:
            biosample_category = 'tumor_tissue'
            source_type = 'CLL_tumor'
        elif original_cell_type in ['CD4', 'CD8', 'NK', 'Bcell', 'Mono']:
            biosample_category = 'immune_cell'
            source_type = 'CLL_immune'
        else:
            biosample_category = 'other_cell'
            source_type = 'CLL_other'

        rendeiro_annotations.append({
            'sample_id': sample_name,
            'source': 'Rendeiro_CLL',
            'sample_type': source_type,
            'cancer_type': 'CLL',
            'cell_type': cell_type_from_name,
            'biosample_category': biosample_category,
            'patient_id': row['patient_id'],
            'treatment_day': row['treatment_day'],
            'timepoint': row['timepoint']
        })

    # Combine annotations
    rendeiro_ann_df = pd.DataFrame(rendeiro_annotations)
    combined_df = pd.concat([orig_df, rendeiro_ann_df], ignore_index=True)

    # Save updated annotations
    combined_df.to_csv(output_file, sep='\t', index=False)

    print(f"Updated annotations saved: {len(combined_df)} total samples")
    print(f"  Original: {len(orig_df)} samples")
    print(f"  Rendeiro: {len(rendeiro_ann_df)} samples")

    return output_file

def main():
    """
    Main function to add Rendeiro CLL data to TCGA/ENCODE matrix.
    """
    # Set paths
    tcga_encode_file = "../../02_processed_data/combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb.tsv"
    annotations_file = "../../02_processed_data/combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb_annotations.tsv"

    narrowpeak_dir = "narrowpeak"
    metadata_file = "processed_data/sample_metadata.tsv"

    output_matrix = "../../02_processed_data/combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_matrix_10kb.tsv"
    output_annotations = "../../02_processed_data/combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_matrix_10kb_annotations.tsv"

    print("=== Adding Rendeiro CLL Data to TCGA/ENCODE Matrix ===")

    # Step 1: Extract genomic windows from TCGA/ENCODE matrix
    genomic_windows = create_genomic_windows_from_tcga_encode(tcga_encode_file)

    # Step 2: Process all Rendeiro samples to genomic windows
    rendeiro_matrix = process_all_rendeiro_samples(narrowpeak_dir, metadata_file, genomic_windows)

    # Step 3: Add Rendeiro data to TCGA/ENCODE matrix
    combined_matrix_file = add_rendeiro_to_tcga_encode_matrix(tcga_encode_file, rendeiro_matrix, output_matrix)

    # Step 4: Create updated annotations
    updated_annotations_file = create_updated_annotations(annotations_file, metadata_file, output_annotations)

    print("\n=== Integration Complete ===")
    print(f"Combined matrix: {combined_matrix_file}")
    print(f"Updated annotations: {updated_annotations_file}")
    print(f"Total Rendeiro samples added: {len(rendeiro_matrix.columns)}")

    # Summary statistics
    print("\nSummary statistics:")
    print(f"Rendeiro samples with signal: {(rendeiro_matrix.sum() > 0).sum()}")
    print(f"Mean signal per sample: {rendeiro_matrix.mean().mean():.4f}")
    print(f"Max signal per sample: {rendeiro_matrix.max().max():.4f}")

if __name__ == "__main__":
    main()
