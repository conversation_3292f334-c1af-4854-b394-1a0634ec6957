#!/usr/bin/env python3
"""
Download and process BOCA Brain Open Chromatin Atlas data to match ENCODE/TCGA structure.
Converts BigWig files to narrowPeak format and organizes data consistently.

BOCA Dataset: 115 human brain ATAC-seq samples from <PERSON><PERSON> et al. (2018)
GEO: GSE96949
Paper: https://doi.org/10.1101/gr.232488.117
"""

import os
import sys
import requests
import pandas as pd
import numpy as np
import argparse
import subprocess
import time
from pathlib import Path
from urllib.parse import urlparse
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BOCAProcessor:
    """Process BOCA brain ATAC-seq data into ENCODE/TCGA-like structure."""
    
    def __init__(self, base_dir=".", download_dir="01_raw_data/boca_brain", 
                 processed_dir="02_processed_data/boca_brain"):
        self.base_dir = Path(base_dir)
        self.download_dir = self.base_dir / download_dir
        self.processed_dir = self.base_dir / processed_dir
        self.narrowpeak_dir = self.download_dir / "narrowpeaks"
        
        # Create directories
        self.download_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        self.narrowpeak_dir.mkdir(parents=True, exist_ok=True)
        
        # BOCA sample metadata
        self.sample_metadata = self._create_sample_metadata()
        
    def _create_sample_metadata(self):
        """Create metadata for all 115 BOCA samples."""
        
        # Sample IDs from GEO GSE96949
        subjects = ['351', '372', '437', '440', '446']
        cell_types = ['G', 'N']  # G=Non-neuronal (Glia), N=Neuronal
        
        # Brain regions (not all subjects have all regions)
        regions = {
            '351': ['ACC', 'DLPFC', 'INS', 'ITC', 'MDT', 'OFC', 'PMC', 'PUT', 'PVC', 'STC', 'VLPFC'],
            '372': ['ACC', 'AMY', 'DLPFC', 'HIPP', 'INS', 'ITC', 'MDT', 'NAC', 'OFC', 'PMC', 'PUT', 'STC', 'VLPFC'],
            '437': ['ACC', 'DLPFC', 'HIPP', 'INS', 'ITC', 'OFC', 'PMC', 'PUT', 'PVC', 'STC', 'VLPFC'],
            '440': ['ACC', 'DLPFC', 'INS', 'ITC', 'MDT', 'OFC', 'PMC', 'PUT', 'PVC', 'STC', 'VLPFC'],
            '446': ['ACC', 'AMY', 'DLPFC', 'HIPP', 'INS', 'ITC', 'MDT', 'OFC', 'PMC', 'PUT', 'PVC', 'STC', 'VLPFC']
        }
        
        # Region full names
        region_names = {
            'ACC': 'Anterior_Cingulate_Cortex',
            'AMY': 'Amygdala',
            'DLPFC': 'Dorsolateral_Prefrontal_Cortex',
            'HIPP': 'Hippocampus',
            'INS': 'Insula',
            'ITC': 'Inferior_Temporal_Cortex',
            'MDT': 'Mediodorsal_Thalamus',
            'NAC': 'Nucleus_Accumbens',
            'OFC': 'Orbitofrontal_Cortex',
            'PMC': 'Primary_Motor_Cortex',
            'PUT': 'Putamen',
            'PVC': 'Primary_Visual_Cortex',
            'STC': 'Superior_Temporal_Cortex',
            'VLPFC': 'Ventrolateral_Prefrontal_Cortex'
        }
        
        cell_type_names = {
            'G': 'Non_Neuronal',
            'N': 'Neuronal'
        }
        
        samples = []
        geo_id = 2546422  # Starting GEO sample ID
        
        for subject in subjects:
            for cell_type in cell_types:
                for region in regions[subject]:
                    sample_id = f"{subject}_{cell_type}_{region}"
                    geo_sample_id = f"GSM{geo_id}"
                    
                    sample_info = {
                        'sample_id': sample_id,
                        'geo_id': geo_sample_id,
                        'subject': subject,
                        'cell_type': cell_type,
                        'cell_type_name': cell_type_names[cell_type],
                        'brain_region': region,
                        'brain_region_name': region_names[region],
                        'bigwig_filename': f"{sample_id}_norm_sorted_rmdup_nochrM.bw",
                        'narrowpeak_filename': f"{sample_id}.narrowPeak",
                        'download_url': f"https://ftp.ncbi.nlm.nih.gov/geo/samples/GSM{geo_id//1000}nnn/{geo_sample_id}/suppl/{geo_sample_id}_{sample_id}_norm_sorted_rmdup_nochrM.bw"
                    }
                    samples.append(sample_info)
                    geo_id += 1
        
        return pd.DataFrame(samples)
    
    def download_bigwig_files(self, max_files=None, resume=True):
        """Download BigWig files from GEO."""
        logger.info(f"Starting download of {len(self.sample_metadata)} BigWig files...")
        
        if max_files:
            samples_to_download = self.sample_metadata.head(max_files)
            logger.info(f"Limiting download to first {max_files} files for testing")
        else:
            samples_to_download = self.sample_metadata
        
        downloaded = 0
        skipped = 0
        failed = 0
        
        for idx, sample in samples_to_download.iterrows():
            output_path = self.download_dir / sample['bigwig_filename']
            
            # Skip if file exists and resume is True
            if resume and output_path.exists():
                logger.info(f"Skipping {sample['sample_id']} - file already exists")
                skipped += 1
                continue
            
            logger.info(f"Downloading {sample['sample_id']} ({idx+1}/{len(samples_to_download)})...")
            
            try:
                response = requests.get(sample['download_url'], stream=True)
                response.raise_for_status()
                
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.info(f"Successfully downloaded {sample['bigwig_filename']}")
                downloaded += 1
                
                # Small delay to be respectful to the server
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Failed to download {sample['sample_id']}: {e}")
                failed += 1
                continue
        
        logger.info(f"Download complete: {downloaded} downloaded, {skipped} skipped, {failed} failed")
        return downloaded, skipped, failed
    
    def convert_to_narrowpeak(self, max_files=None, threshold=None):
        """Convert BigWig files to narrowPeak format using existing script."""
        logger.info("Converting BigWig files to narrowPeak format...")
        
        # Find BigWig files
        bigwig_files = list(self.download_dir.glob("*.bw"))
        
        if max_files:
            bigwig_files = bigwig_files[:max_files]
            logger.info(f"Processing first {max_files} files for testing")
        
        if not bigwig_files:
            logger.error("No BigWig files found to convert")
            return 0, 0
        
        logger.info(f"Found {len(bigwig_files)} BigWig files to convert")
        
        converted = 0
        failed = 0
        
        # Path to conversion script
        convert_script = self.base_dir / "03_scripts/processing/convert_bw_to_narrowpeak.py"
        
        if not convert_script.exists():
            logger.error(f"Conversion script not found: {convert_script}")
            return 0, len(bigwig_files)
        
        for bw_file in bigwig_files:
            sample_id = bw_file.stem.replace("_norm_sorted_rmdup_nochrM", "")
            narrowpeak_file = self.narrowpeak_dir / f"{sample_id}.narrowPeak"
            
            # Skip if already exists
            if narrowpeak_file.exists():
                logger.info(f"Skipping {sample_id} - narrowPeak already exists")
                converted += 1
                continue
            
            logger.info(f"Converting {sample_id}...")
            
            try:
                cmd = [
                    "python3", str(convert_script),
                    "--input", str(bw_file),
                    "--output", str(narrowpeak_file)
                ]
                
                if threshold:
                    cmd.extend(["--threshold", str(threshold)])
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
                
                if result.returncode == 0:
                    logger.info(f"Successfully converted {sample_id}")
                    converted += 1
                else:
                    logger.error(f"Failed to convert {sample_id}: {result.stderr}")
                    failed += 1
                    
            except subprocess.TimeoutExpired:
                logger.error(f"Timeout converting {sample_id}")
                failed += 1
            except Exception as e:
                logger.error(f"Error converting {sample_id}: {e}")
                failed += 1
        
        logger.info(f"Conversion complete: {converted} converted, {failed} failed")
        return converted, failed
    
    def save_metadata(self):
        """Save sample metadata to CSV."""
        metadata_file = self.processed_dir / "boca_sample_metadata.csv"
        self.sample_metadata.to_csv(metadata_file, index=False)
        logger.info(f"Saved metadata to {metadata_file}")
        
        # Also save a summary
        summary_file = self.processed_dir / "boca_summary.txt"
        with open(summary_file, 'w') as f:
            f.write("BOCA Brain Open Chromatin Atlas Summary\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Total samples: {len(self.sample_metadata)}\n")
            f.write(f"Subjects: {', '.join(self.sample_metadata['subject'].unique())}\n")
            f.write(f"Cell types: {', '.join(self.sample_metadata['cell_type_name'].unique())}\n")
            f.write(f"Brain regions: {', '.join(sorted(self.sample_metadata['brain_region_name'].unique()))}\n\n")
            
            f.write("Samples per cell type:\n")
            cell_type_counts = self.sample_metadata['cell_type_name'].value_counts()
            for cell_type, count in cell_type_counts.items():
                f.write(f"  {cell_type}: {count}\n")
            
            f.write("\nSamples per brain region:\n")
            region_counts = self.sample_metadata['brain_region_name'].value_counts()
            for region, count in region_counts.items():
                f.write(f"  {region}: {count}\n")
        
        logger.info(f"Saved summary to {summary_file}")


def main():
    parser = argparse.ArgumentParser(description='Process BOCA brain ATAC-seq data')
    parser.add_argument('--download', action='store_true', help='Download BigWig files')
    parser.add_argument('--convert', action='store_true', help='Convert to narrowPeak format')
    parser.add_argument('--max-files', type=int, help='Limit number of files for testing')
    parser.add_argument('--threshold', type=float, help='Signal threshold for peak calling')
    parser.add_argument('--base-dir', default='.', help='Base directory (default: current)')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = BOCAProcessor(base_dir=args.base_dir)
    
    # Save metadata
    processor.save_metadata()
    
    if args.download:
        processor.download_bigwig_files(max_files=args.max_files)
    
    if args.convert:
        processor.convert_to_narrowpeak(max_files=args.max_files, threshold=args.threshold)
    
    if not args.download and not args.convert:
        logger.info("No action specified. Use --download and/or --convert")
        logger.info(f"Metadata saved for {len(processor.sample_metadata)} samples")


if __name__ == "__main__":
    main()
