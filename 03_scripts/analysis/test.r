breast_patient_ids <- readLines("HMF_sample_lists/Breast.txt")
filtered_maf_HMF_breast <- maf_HMF[maf_HMF$patient_id %in% breast_patient_ids, ]
all_breast <- all(filtered_maf_HMF$cancer_type == "breast")
library(dplyr)
window_size <- 1000000  # Set the size of each genomic window
windows_df <- windows_df %>%
mutate(end = start + window_size - 1)
unique_chromosomes <- unique(maf_HMF$chr)
windows_df <- do.call(rbind, lapply(unique_chromosomes, function(chr) {
# Get the maximum start for the current chromosome in maf_HMF
max_start_chr <- max(maf_HMF$start[maf_HMF$chr == chr])
# Create windows for this chromosome up to max_start_chr
data.frame(
chr = chr,
start = seq(1, max_start_chr, by = window_size),
end = pmin(seq(1, max_start_chr, by = window_size) + window_size - 1, max_start_chr)
)
}))
View(windows_df)
windows_df$end <- NULL
# Initialize a count column in windows_df
windows_df$mutation_count <- 0
# Loop through each window and count mutations
for (i in 1:nrow(windows_df)) {
# Get the chromosome and start position for the current window
chr <- windows_df$chr[i]
start <- windows_df$start[i]
end <- start + window_size - 1
# Count mutations in filtered_maf_HMF_breast within this window
count <- nrow(filtered_maf_HMF_breast %>%
filter(chr == chr & start >= start & start <= end))
# Store the count in the windows_df
windows_df$mutation_count[i] <- count
}
View(windows_df)
windows_df$mutation_count <- NULL
# Initialize a count column in windows_df
windows_df$mutation_count <- 0
# Loop through each window and count mutations
for (i in 1:nrow(windows_df)) {
# Get the chromosome, start, and end positions for the current window
chr <- windows_df$chr[i]
window_start <- windows_df$start[i]
window_end <- window_start + window_size - 1
# Count mutations in filtered_maf_HMF_breast within this specific window
count <- nrow(filtered_maf_HMF_breast %>%
filter(chr == chr & start >= window_start & start <= window_end))
# Store the count in the windows_df
windows_df$mutation_count[i] <- count
}
View(windows_df)
# Calculate the sum of mutation counts
total_mutation_count <- sum(windows_df$mutation_count)
# Print the result
print(total_mutation_count)
View(windows_df)
View(filtered_maf_HMF_breast)
View(maf_HMF)
filtered_maf_HMF_breast <- filtered_maf_HMF_breast %>%
mutate(window_start = ((start - 1) %/% window_size) * window_size + 1)