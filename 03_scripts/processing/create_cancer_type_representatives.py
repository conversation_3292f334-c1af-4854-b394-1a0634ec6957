#!/usr/bin/env python3
"""
Create cancer-type-specific representative narrowPeak files and process them.
Uses the first sample as representative for each cancer type.
"""

import pandas as pd
import os
import subprocess
import shutil
import argparse


def create_representative_narrowpeaks(mapping_file, output_dir="cancer_type_representatives"):
    """
    Create representative narrowPeak files for each cancer type.
    Uses the first sample as representative for each cancer type.
    """
    # Load mapping
    mapping_df = pd.read_csv(mapping_file, sep='\t')
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Group by cancer type and take first sample as representative
    representatives = mapping_df.groupby('cancer_type').first().reset_index()
    
    representative_files = []
    
    print(f"Creating representative files for {len(representatives)} cancer types...")
    
    for _, row in representatives.iterrows():
        cancer_type = row['cancer_type']
        input_file = row['file_path']
        barcode = row['barcode']
        
        # Create output filename
        output_file = os.path.join(output_dir, f"representative_{cancer_type}.narrowPeak")
        
        # Copy file
        shutil.copy2(input_file, output_file)
        representative_files.append(output_file)
        
        print(f"  {cancer_type}: Using {barcode} as representative")
    
    return representative_files


def process_representative_files(representative_files, window_size=10000):
    """
    Process representative narrowPeak files to create genomic windows.
    """
    print(f"\nProcessing {len(representative_files)} representative files...")
    
    results = []
    
    for representative_file in representative_files:
        # Extract cancer type from filename
        basename = os.path.basename(representative_file)
        cancer_type = basename.replace('representative_', '').replace('.narrowPeak', '')
        
        print(f"Processing {cancer_type}...")
        
        # Generate output filename
        if window_size >= 1000:
            size_label = f"{window_size // 1000}kb"
        else:
            size_label = f"{window_size}bp"
        
        output_file = f"representative_{cancer_type}_windows_{size_label}.tsv"
        
        # Run processing script
        cmd = [
            'python', 'process_narrowpeak_memory_efficient.py',
            '--main-chr-only',
            '--window-size', str(window_size),
            '--pattern', representative_file,
            '--output', output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                results.append({
                    'cancer_type': cancer_type,
                    'representative_file': representative_file,
                    'windows_file': output_file,
                    'status': 'success'
                })
                print(f"  ✓ Created {output_file}")
            else:
                print(f"  ✗ Error processing {cancer_type}: {result.stderr}")
                results.append({
                    'cancer_type': cancer_type,
                    'representative_file': representative_file,
                    'windows_file': None,
                    'status': 'failed'
                })
                
        except Exception as e:
            print(f"  ✗ Exception processing {cancer_type}: {e}")
            results.append({
                'cancer_type': cancer_type,
                'representative_file': representative_file,
                'windows_file': None,
                'status': 'failed'
            })
    
    return results


def create_numbered_samples(mapping_file, max_per_type=5, window_size=10000):
    """
    Create numbered samples for cancer types with multiple samples (e.g., LUSC_1, LUSC_2, etc.)
    """
    # Load mapping
    mapping_df = pd.read_csv(mapping_file, sep='\t')
    
    print(f"\nCreating numbered samples (max {max_per_type} per cancer type)...")
    
    results = []
    
    # Group by cancer type
    cancer_groups = mapping_df.groupby('cancer_type')
    
    for cancer_type, group in cancer_groups:
        # Take up to max_per_type samples
        samples_to_process = group.head(max_per_type)
        
        print(f"\n{cancer_type}: Processing {len(samples_to_process)} samples")
        
        for i, (_, sample) in enumerate(samples_to_process.iterrows(), 1):
            # Create numbered annotation
            if len(group) > 1:
                numbered_name = f"{cancer_type}_{i}"
            else:
                numbered_name = cancer_type
            
            # Generate output filename
            if window_size >= 1000:
                size_label = f"{window_size // 1000}kb"
            else:
                size_label = f"{window_size}bp"
            
            output_file = f"{numbered_name}_windows_{size_label}.tsv"
            
            print(f"  Processing {sample['barcode']} -> {numbered_name}")
            
            # Run processing script
            cmd = [
                'python', 'process_narrowpeak_memory_efficient.py',
                '--main-chr-only',
                '--window-size', str(window_size),
                '--pattern', sample['file_path'],
                '--output', output_file
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    results.append({
                        'cancer_type': cancer_type,
                        'numbered_name': numbered_name,
                        'barcode': sample['barcode'],
                        'windows_file': output_file,
                        'status': 'success'
                    })
                    print(f"    ✓ Created {output_file}")
                else:
                    print(f"    ✗ Error: {result.stderr}")
                    results.append({
                        'cancer_type': cancer_type,
                        'numbered_name': numbered_name,
                        'barcode': sample['barcode'],
                        'windows_file': None,
                        'status': 'failed'
                    })
                    
            except Exception as e:
                print(f"    ✗ Exception: {e}")
                results.append({
                    'cancer_type': cancer_type,
                    'numbered_name': numbered_name,
                    'barcode': sample['barcode'],
                    'windows_file': None,
                    'status': 'failed'
                })
    
    return results


def main():
    parser = argparse.ArgumentParser(description='Create cancer-type-specific analyses')
    parser.add_argument('--mapping-file', default='tcga_cancer_type_mapping.tsv',
                       help='Sample mapping file (default: tcga_cancer_type_mapping.tsv)')
    parser.add_argument('--output-dir', default='cancer_type_representatives',
                       help='Output directory for representative files (default: cancer_type_representatives)')
    parser.add_argument('--window-size', type=int, default=10000,
                       help='Window size for genomic analysis (default: 10000)')
    parser.add_argument('--mode', choices=['representatives', 'numbered', 'both'], default='numbered',
                       help='Analysis mode: representatives (1 per type), numbered (multiple per type), or both')
    parser.add_argument('--max-per-type', type=int, default=5,
                       help='Maximum samples per cancer type for numbered mode (default: 5)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.mapping_file):
        print(f"Error: Mapping file {args.mapping_file} not found")
        print("Run: python analyze_tcga_cancer_types.py first")
        return
    
    all_results = []
    
    if args.mode in ['representatives', 'both']:
        # Create representative files
        representative_files = create_representative_narrowpeaks(args.mapping_file, args.output_dir)
        
        # Process representative files
        rep_results = process_representative_files(representative_files, args.window_size)
        all_results.extend(rep_results)
    
    if args.mode in ['numbered', 'both']:
        # Create numbered samples
        numbered_results = create_numbered_samples(args.mapping_file, args.max_per_type, args.window_size)
        all_results.extend(numbered_results)
    
    # Summary
    successful = [r for r in all_results if r['status'] == 'success']
    failed = [r for r in all_results if r['status'] == 'failed']
    
    print(f"\n{'='*60}")
    print(f"Cancer-type-specific analysis completed!")
    print(f"Successful: {len(successful)}")
    print(f"Failed: {len(failed)}")
    
    if successful:
        print(f"\nGenerated window files:")
        for result in successful[:20]:  # Show first 20
            if 'numbered_name' in result:
                print(f"  {result['numbered_name']}: {result['windows_file']}")
            else:
                print(f"  {result['cancer_type']}: {result['windows_file']}")
        
        if len(successful) > 20:
            print(f"  ... and {len(successful) - 20} more files")
    
    # Save results summary
    results_df = pd.DataFrame(successful)
    if not results_df.empty:
        results_df.to_csv('cancer_type_analysis_results.tsv', sep='\t', index=False)
        print(f"\nSaved results summary to cancer_type_analysis_results.tsv")


if __name__ == "__main__":
    main()
