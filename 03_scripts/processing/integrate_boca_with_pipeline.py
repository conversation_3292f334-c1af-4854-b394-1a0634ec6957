#!/usr/bin/env python3
"""
Integrate BOCA brain ATAC-seq data with existing ENCODE/TCGA processing pipeline.
Creates unified data structure and processing workflow.
"""

import os
import sys
import pandas as pd
import numpy as np
import argparse
from pathlib import Path
import logging
import subprocess

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BOCAIntegrator:
    """Integrate BOCA data with ENCODE/TCGA pipeline."""
    
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        
        # Directory structure
        self.boca_dir = self.base_dir / "01_raw_data/boca_brain"
        self.boca_narrowpeaks = self.boca_dir / "narrowpeaks"
        self.other_narrowpeaks = self.base_dir / "01_raw_data/other_narrowpeaks"
        self.processed_dir = self.base_dir / "02_processed_data"
        
        # Create directories
        self.other_narrowpeaks.mkdir(parents=True, exist_ok=True)
        
    def create_boca_metadata(self):
        """Create standardized metadata for BOCA samples."""
        logger.info("Creating BOCA metadata...")
        
        # BOCA sample information
        subjects = ['351', '372', '437', '440', '446']
        cell_types = {'G': 'Non_Neuronal', 'N': 'Neuronal'}
        
        # Brain regions with full names
        regions = {
            'ACC': 'Anterior_Cingulate_Cortex',
            'AMY': 'Amygdala',
            'DLPFC': 'Dorsolateral_Prefrontal_Cortex',
            'HIPP': 'Hippocampus',
            'INS': 'Insula',
            'ITC': 'Inferior_Temporal_Cortex',
            'MDT': 'Mediodorsal_Thalamus',
            'NAC': 'Nucleus_Accumbens',
            'OFC': 'Orbitofrontal_Cortex',
            'PMC': 'Primary_Motor_Cortex',
            'PUT': 'Putamen',
            'PVC': 'Primary_Visual_Cortex',
            'STC': 'Superior_Temporal_Cortex',
            'VLPFC': 'Ventrolateral_Prefrontal_Cortex'
        }
        
        # Subject-specific regions (from GEO metadata)
        subject_regions = {
            '351': ['ACC', 'DLPFC', 'INS', 'ITC', 'MDT', 'OFC', 'PMC', 'PUT', 'PVC', 'STC', 'VLPFC'],
            '372': ['ACC', 'AMY', 'DLPFC', 'HIPP', 'INS', 'ITC', 'MDT', 'NAC', 'OFC', 'PMC', 'PUT', 'STC', 'VLPFC'],
            '437': ['ACC', 'DLPFC', 'HIPP', 'INS', 'ITC', 'OFC', 'PMC', 'PUT', 'PVC', 'STC', 'VLPFC'],
            '440': ['ACC', 'DLPFC', 'INS', 'ITC', 'MDT', 'OFC', 'PMC', 'PUT', 'PVC', 'STC', 'VLPFC'],
            '446': ['ACC', 'AMY', 'DLPFC', 'HIPP', 'INS', 'ITC', 'MDT', 'OFC', 'PMC', 'PUT', 'PVC', 'STC', 'VLPFC']
        }
        
        samples = []
        for subject in subjects:
            for cell_code, cell_name in cell_types.items():
                for region_code in subject_regions[subject]:
                    if region_code in regions:
                        sample_id = f"{subject}_{cell_code}_{region_code}"
                        
                        sample_info = {
                            'sample_id': sample_id,
                            'dataset': 'BOCA',
                            'data_type': 'ATAC-seq',
                            'species': 'Homo_sapiens',
                            'assembly': 'GRCh37',
                            'subject_id': subject,
                            'cell_type': cell_name,
                            'tissue': 'Brain',
                            'tissue_specific': regions[region_code],
                            'brain_region': region_code,
                            'brain_region_full': regions[region_code],
                            'condition': 'Normal',
                            'file_format': 'narrowPeak',
                            'filename': f"{sample_id}.narrowPeak",
                            'source': 'Fullard_et_al_2018',
                            'pmid': '29945882',
                            'geo_accession': 'GSE96949'
                        }
                        samples.append(sample_info)
        
        df = pd.DataFrame(samples)
        
        # Save metadata
        metadata_file = self.processed_dir / "boca_standardized_metadata.csv"
        df.to_csv(metadata_file, index=False)
        logger.info(f"Saved BOCA metadata: {metadata_file}")
        logger.info(f"Created metadata for {len(df)} samples")
        
        return df
    
    def copy_narrowpeaks_to_standard_location(self):
        """Copy BOCA narrowPeak files to standard location with other datasets."""
        logger.info("Copying BOCA narrowPeak files to standard location...")
        
        if not self.boca_narrowpeaks.exists():
            logger.error(f"BOCA narrowPeak directory not found: {self.boca_narrowpeaks}")
            return 0
        
        # Find all narrowPeak files
        narrowpeak_files = list(self.boca_narrowpeaks.glob("*.narrowPeak"))
        
        if not narrowpeak_files:
            logger.warning("No narrowPeak files found in BOCA directory")
            return 0
        
        copied = 0
        for np_file in narrowpeak_files:
            # Create standardized filename
            dest_file = self.other_narrowpeaks / f"BOCA_{np_file.name}"
            
            try:
                # Copy file
                import shutil
                shutil.copy2(np_file, dest_file)
                logger.info(f"Copied {np_file.name} -> {dest_file.name}")
                copied += 1
                
            except Exception as e:
                logger.error(f"Failed to copy {np_file.name}: {e}")
        
        logger.info(f"Copied {copied}/{len(narrowpeak_files)} files")
        return copied
    
    def create_unified_metadata(self):
        """Create unified metadata combining BOCA with existing datasets."""
        logger.info("Creating unified metadata...")
        
        metadata_files = []
        
        # Look for existing metadata files
        existing_metadata = [
            self.processed_dir / "encode_standardized_metadata.csv",
            self.processed_dir / "tcga_standardized_metadata.csv",
            self.processed_dir / "boca_standardized_metadata.csv"
        ]
        
        dataframes = []
        
        for metadata_file in existing_metadata:
            if metadata_file.exists():
                logger.info(f"Loading {metadata_file.name}")
                df = pd.read_csv(metadata_file)
                dataframes.append(df)
                logger.info(f"  - {len(df)} samples")
            else:
                logger.warning(f"Metadata file not found: {metadata_file}")
        
        if not dataframes:
            logger.error("No metadata files found")
            return None
        
        # Combine all metadata
        combined_df = pd.concat(dataframes, ignore_index=True, sort=False)
        
        # Standardize columns
        required_columns = [
            'sample_id', 'dataset', 'data_type', 'species', 'assembly',
            'cell_type', 'tissue', 'condition', 'file_format', 'filename'
        ]
        
        for col in required_columns:
            if col not in combined_df.columns:
                combined_df[col] = 'Unknown'
        
        # Save unified metadata
        unified_file = self.processed_dir / "unified_metadata.csv"
        combined_df.to_csv(unified_file, index=False)
        
        logger.info(f"Created unified metadata: {unified_file}")
        logger.info(f"Total samples: {len(combined_df)}")
        
        # Print summary
        logger.info("Dataset summary:")
        dataset_counts = combined_df['dataset'].value_counts()
        for dataset, count in dataset_counts.items():
            logger.info(f"  {dataset}: {count} samples")
        
        return combined_df
    
    def run_processing_pipeline(self, window_size=1000, step_size=500):
        """Run the existing processing pipeline on BOCA data."""
        logger.info("Running processing pipeline on BOCA data...")
        
        # Check if processing scripts exist
        scripts_to_run = [
            "process_narrowpeak_files.py",
            "combine_all_tcga_profiles.py"  # This can be adapted for BOCA
        ]
        
        scripts_dir = self.base_dir / "03_scripts/processing"
        
        for script_name in scripts_to_run:
            script_path = scripts_dir / script_name
            
            if not script_path.exists():
                logger.warning(f"Script not found: {script_path}")
                continue
            
            logger.info(f"Running {script_name}...")
            
            try:
                # Run the script
                cmd = [
                    "python3", str(script_path),
                    "--input-dir", str(self.other_narrowpeaks),
                    "--output-dir", str(self.processed_dir),
                    "--window-size", str(window_size),
                    "--step-size", str(step_size)
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
                
                if result.returncode == 0:
                    logger.info(f"Successfully ran {script_name}")
                else:
                    logger.error(f"Failed to run {script_name}: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                logger.error(f"Timeout running {script_name}")
            except Exception as e:
                logger.error(f"Error running {script_name}: {e}")
    
    def create_analysis_summary(self):
        """Create summary of integrated datasets for analysis."""
        logger.info("Creating analysis summary...")
        
        # Load unified metadata
        unified_file = self.processed_dir / "unified_metadata.csv"
        
        if not unified_file.exists():
            logger.error("Unified metadata not found. Run create_unified_metadata first.")
            return
        
        df = pd.read_csv(unified_file)
        
        # Create summary
        summary = {
            'total_samples': len(df),
            'datasets': df['dataset'].value_counts().to_dict(),
            'data_types': df['data_type'].value_counts().to_dict(),
            'species': df['species'].value_counts().to_dict(),
            'assemblies': df['assembly'].value_counts().to_dict(),
            'cell_types': df['cell_type'].value_counts().to_dict(),
            'tissues': df['tissue'].value_counts().to_dict()
        }
        
        # Save summary
        summary_file = self.processed_dir / "integrated_analysis_summary.txt"
        
        with open(summary_file, 'w') as f:
            f.write("Integrated ENCODE/TCGA/BOCA Analysis Summary\n")
            f.write("=" * 50 + "\n\n")
            
            for key, value in summary.items():
                f.write(f"{key.replace('_', ' ').title()}:\n")
                if isinstance(value, dict):
                    for item, count in value.items():
                        f.write(f"  {item}: {count}\n")
                else:
                    f.write(f"  {value}\n")
                f.write("\n")
        
        logger.info(f"Saved analysis summary: {summary_file}")
        
        return summary


def main():
    parser = argparse.ArgumentParser(description='Integrate BOCA data with ENCODE/TCGA pipeline')
    parser.add_argument('--create-metadata', action='store_true',
                       help='Create standardized BOCA metadata')
    parser.add_argument('--copy-files', action='store_true',
                       help='Copy narrowPeak files to standard location')
    parser.add_argument('--unified-metadata', action='store_true',
                       help='Create unified metadata for all datasets')
    parser.add_argument('--run-pipeline', action='store_true',
                       help='Run processing pipeline on BOCA data')
    parser.add_argument('--summary', action='store_true',
                       help='Create analysis summary')
    parser.add_argument('--all', action='store_true',
                       help='Run all integration steps')
    parser.add_argument('--base-dir', default='.', help='Base directory (default: current)')
    
    args = parser.parse_args()
    
    # Initialize integrator
    integrator = BOCAIntegrator(base_dir=args.base_dir)
    
    if args.all:
        args.create_metadata = True
        args.copy_files = True
        args.unified_metadata = True
        args.summary = True
    
    if args.create_metadata:
        integrator.create_boca_metadata()
    
    if args.copy_files:
        integrator.copy_narrowpeaks_to_standard_location()
    
    if args.unified_metadata:
        integrator.create_unified_metadata()
    
    if args.run_pipeline:
        integrator.run_processing_pipeline()
    
    if args.summary:
        integrator.create_analysis_summary()
    
    if not any([args.create_metadata, args.copy_files, args.unified_metadata, 
                args.run_pipeline, args.summary, args.all]):
        logger.info("No action specified. Use --help for options or --all for complete integration")


if __name__ == "__main__":
    main()
