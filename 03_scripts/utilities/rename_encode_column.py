#!/usr/bin/env python3
"""
Rename ENCODE column to reflect the actual tissue type.
"""

import pandas as pd


def rename_encode_column():
    """
    Rename ENCODE column from generic name to tissue-specific name.
    """
    print("Renaming ENCODE column to reflect tissue type...")
    
    # Load the matrix
    print("Loading matrix...")
    df = pd.read_csv("TCGA_all_samples_with_ENCODE_10kb.tsv", sep='\t')
    
    print(f"Current matrix: {len(df)} windows × {len(df.columns)} columns")
    
    # Check current column name
    if 'ENCODE_ENCFF135EYC' in df.columns:
        # Rename to reflect the actual tissue type
        df = df.rename(columns={'ENCODE_ENCFF135EYC': 'HEART_LEFT_VENTRICLE'})
        print("✓ Renamed 'ENCODE_ENCFF135EYC' to 'HEART_LEFT_VENTRICLE'")
    else:
        print("Column 'ENCODE_ENCFF135EYC' not found!")
        return
    
    # Save the updated matrix
    output_file = "TCGA_all_samples_with_HEART_10kb.tsv"
    print(f"Saving updated matrix to {output_file}...")
    df.to_csv(output_file, sep='\t', index=False)
    
    print(f"✓ Matrix updated successfully!")
    print(f"New column name: 'HEART_LEFT_VENTRICLE'")
    
    # Show the last few columns to confirm
    print(f"\nLast 5 column names:")
    for col in df.columns[-5:]:
        print(f"  {col}")
    
    return output_file


if __name__ == "__main__":
    rename_encode_column()
