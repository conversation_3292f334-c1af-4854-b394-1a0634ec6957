#!/usr/bin/env python3

import os
import pandas as pd
import gzip
import re
from pathlib import Path

def parse_filename(filename):
    """Parse the filename to extract metadata"""
    # Remove GSM prefix and suffix
    name = filename.replace('.peaks.narrowPeak.gz', '')
    parts = name.split('_')
    
    # Extract GSM ID
    gsm_id = parts[0]
    
    # Extract patient/donor info
    if 'CLL' in name:
        patient_id = parts[2]  # CLL1, CLL2, etc.
        timepoint = parts[3]   # 0d, 3d, 120d, etc.
        cell_type = '_'.join(parts[4:])  # Handle cases like CD4_1, CD4_2
        sample_type = 'CLL_patient'
    else:
        # This would be for normal donors, but we don't seem to have them
        patient_id = 'Unknown'
        timepoint = 'Unknown'
        cell_type = 'Unknown'
        sample_type = 'Normal_donor'
    
    return {
        'gsm_id': gsm_id,
        'filename': filename,
        'patient_id': patient_id,
        'timepoint': timepoint,
        'cell_type': cell_type,
        'sample_type': sample_type,
        'treatment_day': extract_treatment_day(timepoint)
    }

def extract_treatment_day(timepoint):
    """Extract numeric day from timepoint string"""
    if 'd' in timepoint:
        return int(timepoint.replace('d', ''))
    return 0

def read_narrowpeak_file(filepath):
    """Read a narrowPeak file and return DataFrame"""
    columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
               'signalValue', 'pValue', 'qValue', 'peak']
    
    if filepath.endswith('.gz'):
        with gzip.open(filepath, 'rt') as f:
            df = pd.read_csv(f, sep='\t', header=None, names=columns)
    else:
        df = pd.read_csv(filepath, sep='\t', header=None, names=columns)
    
    return df

def create_metadata_file(narrowpeak_dir):
    """Create metadata file from narrowPeak filenames"""
    files = [f for f in os.listdir(narrowpeak_dir) if f.endswith('.narrowPeak.gz')]
    
    metadata = []
    for filename in files:
        meta = parse_filename(filename)
        metadata.append(meta)
    
    df = pd.DataFrame(metadata)
    
    # Sort by patient, timepoint, cell type
    df = df.sort_values(['patient_id', 'treatment_day', 'cell_type'])
    
    return df

def combine_narrowpeak_files(narrowpeak_dir, metadata_df, output_dir):
    """Combine narrowPeak files into organized structure"""
    
    # Create output directories
    os.makedirs(f"{output_dir}/combined", exist_ok=True)
    os.makedirs(f"{output_dir}/by_celltype", exist_ok=True)
    os.makedirs(f"{output_dir}/by_patient", exist_ok=True)
    os.makedirs(f"{output_dir}/by_timepoint", exist_ok=True)
    
    # 1. Create a master combined file with all peaks
    print("Creating master combined file...")
    all_peaks = []
    
    for _, row in metadata_df.iterrows():
        filepath = os.path.join(narrowpeak_dir, row['filename'])
        df = read_narrowpeak_file(filepath)
        
        # Add metadata columns
        df['sample_id'] = row['gsm_id']
        df['patient_id'] = row['patient_id']
        df['cell_type'] = row['cell_type']
        df['timepoint'] = row['timepoint']
        df['treatment_day'] = row['treatment_day']
        
        all_peaks.append(df)
    
    # Combine all peaks
    combined_df = pd.concat(all_peaks, ignore_index=True)
    
    # Save master file
    combined_df.to_csv(f"{output_dir}/combined/all_peaks_combined.tsv", 
                      sep='\t', index=False)
    
    print(f"Master file saved: {len(combined_df)} total peaks from {len(metadata_df)} samples")
    
    # 2. Create files organized by cell type
    print("Creating files by cell type...")
    for cell_type in combined_df['cell_type'].unique():
        cell_df = combined_df[combined_df['cell_type'] == cell_type]
        cell_df.to_csv(f"{output_dir}/by_celltype/{cell_type}_peaks.tsv",
                      sep='\t', index=False)
        print(f"  {cell_type}: {len(cell_df)} peaks")

    # 3. Create files organized by patient
    print("Creating files by patient...")
    for patient in combined_df['patient_id'].unique():
        patient_df = combined_df[combined_df['patient_id'] == patient]
        patient_df.to_csv(f"{output_dir}/by_patient/{patient}_peaks.tsv",
                         sep='\t', index=False)
        print(f"  {patient}: {len(patient_df)} peaks")

    # 4. Create files organized by timepoint
    print("Creating files by timepoint...")
    for timepoint in sorted(combined_df['treatment_day'].unique()):
        time_df = combined_df[combined_df['treatment_day'] == timepoint]
        time_df.to_csv(f"{output_dir}/by_timepoint/day_{timepoint:03d}_peaks.tsv",
                      sep='\t', index=False)
        print(f"  Day {timepoint}: {len(time_df)} peaks")

    return combined_df

def create_summary_stats(combined_df, metadata_df, output_dir):
    """Create summary statistics"""

    stats = {
        'total_samples': len(metadata_df),
        'total_peaks': len(combined_df),
        'unique_patients': len(metadata_df['patient_id'].unique()),
        'unique_cell_types': len(metadata_df['cell_type'].unique()),
        'unique_timepoints': len(metadata_df['treatment_day'].unique()),
        'peaks_per_sample': combined_df.groupby('sample_id').size().describe(),
        'samples_per_cell_type': metadata_df['cell_type'].value_counts(),
        'samples_per_patient': metadata_df['patient_id'].value_counts(),
        'samples_per_timepoint': metadata_df['treatment_day'].value_counts()
    }

    # Save summary
    with open(f"{output_dir}/dataset_summary.txt", 'w') as f:
        f.write("Rendeiro CLL Chromatin Accessibility Dataset Summary\n")
        f.write("=" * 50 + "\n\n")

        f.write(f"Total samples: {stats['total_samples']}\n")
        f.write(f"Total peaks: {stats['total_peaks']}\n")
        f.write(f"Unique patients: {stats['unique_patients']}\n")
        f.write(f"Unique cell types: {stats['unique_cell_types']}\n")
        f.write(f"Unique timepoints: {stats['unique_timepoints']}\n\n")

        f.write("Peaks per sample statistics:\n")
        f.write(str(stats['peaks_per_sample']) + "\n\n")

        f.write("Samples per cell type:\n")
        for cell_type, count in stats['samples_per_cell_type'].items():
            f.write(f"  {cell_type}: {count}\n")
        f.write("\n")

        f.write("Samples per patient:\n")
        for patient, count in stats['samples_per_patient'].items():
            f.write(f"  {patient}: {count}\n")
        f.write("\n")

        f.write("Samples per timepoint:\n")
        for timepoint, count in sorted(stats['samples_per_timepoint'].items()):
            f.write(f"  Day {timepoint}: {count}\n")

    return stats

def main():
    # Set paths
    narrowpeak_dir = "narrowpeak"
    output_dir = "processed_data"

    print("=== Rendeiro CLL Dataset Processing ===")
    print("Creating TCGA/ENCODE-like structure...")

    # Create metadata
    print("1. Creating metadata...")
    metadata_df = create_metadata_file(narrowpeak_dir)

    # Save metadata
    os.makedirs(output_dir, exist_ok=True)
    metadata_df.to_csv(f"{output_dir}/sample_metadata.tsv", sep='\t', index=False)
    print(f"   Metadata saved: {len(metadata_df)} samples")

    # Combine narrowPeak files
    print("2. Combining narrowPeak files...")
    combined_df = combine_narrowpeak_files(narrowpeak_dir, metadata_df, output_dir)

    # Create summary statistics
    print("3. Creating summary statistics...")
    stats = create_summary_stats(combined_df, metadata_df, output_dir)

    print("\n=== Processing Complete ===")
    print(f"Output directory: {output_dir}")
    print(f"Total samples processed: {len(metadata_df)}")
    print(f"Total peaks: {len(combined_df)}")

    print("\nOutput structure:")
    print("├── sample_metadata.tsv")
    print("├── dataset_summary.txt")
    print("├── combined/")
    print("│   └── all_peaks_combined.tsv")
    print("├── by_celltype/")
    print("│   ├── Bcell_peaks.tsv")
    print("│   ├── CD4_peaks.tsv")
    print("│   └── ...")
    print("├── by_patient/")
    print("│   ├── CLL1_peaks.tsv")
    print("│   ├── CLL2_peaks.tsv")
    print("│   └── ...")
    print("└── by_timepoint/")
    print("    ├── day_000_peaks.tsv")
    print("    ├── day_001_peaks.tsv")
    print("    └── ...")

if __name__ == "__main__":
    main()
