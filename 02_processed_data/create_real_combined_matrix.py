#!/usr/bin/env python3

import pandas as pd
import numpy as np
import os
from collections import defaultdict

def load_tcga_encode_coordinates():
    """
    Load just the coordinates from TCGA/ENCODE matrix.
    """
    print("Loading TCGA/ENCODE coordinates...")
    coords_df = pd.read_csv("combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb.tsv", 
                           sep='\t', usecols=['chr', 'start', 'end'])
    print(f"Loaded {len(coords_df)} 10kb windows")
    return coords_df

def convert_rendeiro_to_10kb_windows(rendeiro_file, tcga_coords):
    """
    Convert Rendeiro peaks to 10kb windows using efficient binning.
    """
    print("Converting Rendeiro data to 10kb windows...")
    
    # Load Rendeiro matrix
    print("Loading Rendeiro matrix...")
    rendeiro_df = pd.read_csv(rendeiro_file, sep='\t')
    
    # Get sample columns
    sample_cols = [col for col in rendeiro_df.columns if col not in ['chr', 'start', 'end']]
    print(f"Processing {len(sample_cols)} Rendeiro samples...")
    
    # Initialize result matrix
    result_df = tcga_coords.copy()
    
    # Process each chromosome separately for memory efficiency
    for chrom in tcga_coords['chr'].unique():
        print(f"Processing chromosome {chrom}...")
        
        # Get windows and peaks for this chromosome
        chrom_windows = tcga_coords[tcga_coords['chr'] == chrom].copy()
        chrom_peaks = rendeiro_df[rendeiro_df['chr'] == chrom].copy()
        
        if len(chrom_peaks) == 0:
            print(f"  No peaks found for {chrom}")
            continue
        
        print(f"  {len(chrom_windows)} windows, {len(chrom_peaks)} peaks")
        
        # Create a mapping from windows to overlapping peaks
        for window_idx, window in chrom_windows.iterrows():
            # Find overlapping peaks using vectorized operations
            overlapping_mask = (
                (chrom_peaks['start'] < window['end']) & 
                (chrom_peaks['end'] > window['start'])
            )
            overlapping_peaks = chrom_peaks[overlapping_mask]
            
            if len(overlapping_peaks) > 0:
                # Calculate mean signal for each sample
                for sample in sample_cols:
                    mean_signal = overlapping_peaks[sample].mean()
                    result_df.loc[window_idx, sample] = mean_signal
    
    # Fill NaN values with 0
    for sample in sample_cols:
        if sample not in result_df.columns:
            result_df[sample] = 0.0
        else:
            result_df[sample] = result_df[sample].fillna(0.0)
    
    return result_df

def combine_matrices_efficiently():
    """
    Efficiently combine TCGA/ENCODE and Rendeiro matrices.
    """
    print("=== Creating Real Combined Matrix ===")
    
    # File paths
    tcga_encode_file = "combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb.tsv"
    rendeiro_file = "combined_matrices/Rendeiro_CLL_matrix.tsv"
    output_file = "combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_matrix_10kb.tsv"
    
    # Step 1: Load TCGA/ENCODE coordinates
    tcga_coords = load_tcga_encode_coordinates()
    
    # Step 2: Convert Rendeiro to 10kb windows
    print("Converting Rendeiro data to match TCGA/ENCODE windows...")
    rendeiro_10kb = convert_rendeiro_to_10kb_windows(rendeiro_file, tcga_coords)
    
    # Step 3: Load TCGA/ENCODE matrix in chunks and combine
    print("Loading TCGA/ENCODE matrix and combining...")
    
    # Read TCGA/ENCODE matrix
    tcga_df = pd.read_csv(tcga_encode_file, sep='\t')
    print(f"TCGA/ENCODE matrix: {len(tcga_df)} windows × {len(tcga_df.columns)} columns")
    
    # Get Rendeiro sample columns
    rendeiro_samples = [col for col in rendeiro_10kb.columns if col not in ['chr', 'start', 'end']]
    
    # Add Rendeiro columns to TCGA matrix
    for sample in rendeiro_samples:
        tcga_df[sample] = rendeiro_10kb[sample]
    
    # Save combined matrix
    print(f"Saving combined matrix to {output_file}...")
    tcga_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"Combined matrix: {len(tcga_df)} windows × {len(tcga_df.columns)} columns")
    print(f"Added {len(rendeiro_samples)} Rendeiro samples")
    
    return output_file

def create_memory_efficient_version():
    """
    Create a memory-efficient version that processes data in chunks.
    """
    print("=== Memory-Efficient Matrix Creation ===")
    
    # File paths
    tcga_encode_file = "combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb.tsv"
    rendeiro_file = "combined_matrices/Rendeiro_CLL_matrix.tsv"
    output_file = "combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_matrix_10kb.tsv"
    
    print("Step 1: Getting matrix dimensions...")
    
    # Get headers
    with open(tcga_encode_file, 'r') as f:
        tcga_header = f.readline().strip().split('\t')
    
    with open(rendeiro_file, 'r') as f:
        rendeiro_header = f.readline().strip().split('\t')
    
    # Combine headers
    coord_cols = ['chr', 'start', 'end']
    tcga_samples = [col for col in tcga_header if col not in coord_cols]
    rendeiro_samples = [col for col in rendeiro_header if col not in coord_cols]
    
    combined_header = coord_cols + tcga_samples + rendeiro_samples
    
    print(f"TCGA/ENCODE samples: {len(tcga_samples)}")
    print(f"Rendeiro samples: {len(rendeiro_samples)}")
    print(f"Total samples: {len(tcga_samples) + len(rendeiro_samples)}")
    
    print("Step 2: Processing coordinates...")
    
    # Load coordinates
    tcga_coords = pd.read_csv(tcga_encode_file, sep='\t', usecols=coord_cols)
    
    print("Step 3: Creating Rendeiro 10kb matrix...")
    
    # Convert Rendeiro to 10kb windows
    rendeiro_10kb = convert_rendeiro_to_10kb_windows(rendeiro_file, tcga_coords)
    
    print("Step 4: Combining matrices...")
    
    # Process TCGA/ENCODE matrix in chunks
    chunk_size = 10000
    first_chunk = True
    
    with open(output_file, 'w') as out_f:
        # Write header
        out_f.write('\t'.join(combined_header) + '\n')
        
        # Process TCGA/ENCODE in chunks
        for chunk in pd.read_csv(tcga_encode_file, sep='\t', chunksize=chunk_size):
            print(f"Processing chunk of {len(chunk)} rows...")
            
            # Get corresponding Rendeiro data
            start_idx = chunk.index[0]
            end_idx = chunk.index[-1]
            rendeiro_chunk = rendeiro_10kb.iloc[start_idx:end_idx+1]
            
            # Combine chunks
            combined_chunk = chunk.copy()
            for sample in rendeiro_samples:
                combined_chunk[sample] = rendeiro_chunk[sample].values
            
            # Write to file (skip header for subsequent chunks)
            combined_chunk.to_csv(out_f, sep='\t', index=False, header=False)
    
    print(f"Combined matrix saved to: {output_file}")
    
    # Verify the result
    print("Verifying result...")
    result_df = pd.read_csv(output_file, sep='\t', nrows=5)
    print(f"Final matrix: {result_df.shape[1]} columns")
    print("Sample columns:", result_df.columns.tolist()[:10], "...")
    
    return output_file

def main():
    """
    Main function to create the real combined matrix.
    """
    try:
        # Try memory-efficient approach first
        output_file = create_memory_efficient_version()
        
        print("\n=== Success! ===")
        print(f"Real combined matrix created: {output_file}")
        print("This matrix contains actual chromatin accessibility data from:")
        print("- TCGA cancer samples")
        print("- ENCODE cell lines and tissues")
        print("- Rendeiro CLL time-course samples")
        print("\nThe matrix uses 10kb genomic windows as coordinates.")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Trying simpler approach...")
        
        # Fallback to simpler approach
        try:
            output_file = combine_matrices_efficiently()
            print(f"Combined matrix created: {output_file}")
        except Exception as e2:
            print(f"Error in fallback approach: {e2}")
            print("Please check memory availability and file paths.")

if __name__ == "__main__":
    main()
