#!/usr/bin/env python3
"""
Organize the chromatin accessibility analysis folder.
"""

import os
import shutil
import glob
import argparse


def create_folder_structure():
    """
    Create organized folder structure.
    """
    folders = {
        '01_raw_data': {
            'tcga_narrowpeaks': 'TCGA ATAC-seq narrowPeak files',
            'encode_narrowpeaks': 'ENCODE narrowPeak files', 
            'other_narrowpeaks': 'Other narrowPeak files (GEO, etc.)',
            'reference_files': 'Reference files (genes, CGC, etc.)'
        },
        '02_processed_data': {
            'individual_windows': 'Individual sample window files',
            'combined_matrices': 'Combined sample matrices',
            'annotations': 'Sample annotation files'
        },
        '03_scripts': {
            'processing': 'Data processing scripts',
            'analysis': 'Analysis scripts',
            'utilities': 'Utility scripts'
        },
        '04_logs': 'Log files from processing',
        '05_downloads': 'Downloaded files (temporary)',
        '06_documentation': 'Documentation and notes'
    }
    
    print("Creating organized folder structure...")
    
    for main_folder, subfolders in folders.items():
        os.makedirs(main_folder, exist_ok=True)
        print(f"Created: {main_folder}/")
        
        if isinstance(subfolders, dict):
            for subfolder, description in subfolders.items():
                subfolder_path = os.path.join(main_folder, subfolder)
                os.makedirs(subfolder_path, exist_ok=True)
                print(f"  Created: {subfolder_path}/ - {description}")
        else:
            print(f"  Description: {subfolders}")


def organize_files():
    """
    Move files to appropriate folders.
    """
    print("\nOrganizing files...")
    
    # File organization rules
    file_rules = [
        # Raw data - TCGA
        {
            'pattern': 'TCGA_ATAC_*.narrowPeak',
            'destination': '01_raw_data/tcga_narrowpeaks/',
            'description': 'TCGA narrowPeak files'
        },
        
        # Raw data - ENCODE (move from downloads)
        {
            'pattern': 'encode_downloads/*.bed',
            'destination': '01_raw_data/encode_narrowpeaks/',
            'description': 'ENCODE narrowPeak files'
        },
        
        # Raw data - Other
        {
            'pattern': ['ENCFF*.bed', 'GSM*.narrowPeak', 'GSM*.bw', '*.bed'],
            'destination': '01_raw_data/other_narrowpeaks/',
            'description': 'Other narrowPeak and BED files',
            'exclude': ['genes.bed', 'regions.bed', 'intersected.bed', '_merged_glia.bed', 'VLPFC_neuron*.bed', 'boca_*.bed']
        },
        
        # Reference files
        {
            'pattern': ['*genes*.bed', '*genes*.tsv', '*genes*.gff', 'cgc_*.tsv', '*oncokb*.tsv', '*CGC*.csv', '*OncoKB*.csv'],
            'destination': '01_raw_data/reference_files/',
            'description': 'Reference annotation files'
        },
        
        # Individual window files
        {
            'pattern': ['*_windows_*.tsv', 'windows_with_CA_signal_*.tsv'],
            'destination': '02_processed_data/individual_windows/',
            'description': 'Individual sample window files',
            'exclude': ['*combined*.tsv', '*all_samples*.tsv']
        },
        
        # Combined matrices
        {
            'pattern': ['*combined*.tsv', '*all_samples*.tsv'],
            'destination': '02_processed_data/combined_matrices/',
            'description': 'Combined sample matrices'
        },
        
        # Annotations
        {
            'pattern': ['*mapping*.csv', '*annotations*.tsv', '*mapping*.tsv'],
            'destination': '02_processed_data/annotations/',
            'description': 'Sample annotation and mapping files'
        },
        
        # Processing scripts
        {
            'pattern': ['process_*.py', 'combine_*.py', 'create_*.py', 'batch_*.py', 'convert_*.py'],
            'destination': '03_scripts/processing/',
            'description': 'Data processing scripts'
        },
        
        # Analysis scripts
        {
            'pattern': ['analyze_*.py', '*.r', 'HCNN.py', 'match.py', 'combineCA.py'],
            'destination': '03_scripts/analysis/',
            'description': 'Analysis scripts'
        },
        
        # Utility scripts
        {
            'pattern': ['monitor_*.py', 'organize_*.py', 'rename_*.py', 'add_*.py'],
            'destination': '03_scripts/utilities/',
            'description': 'Utility scripts'
        },
        
        # Download scripts and files
        {
            'pattern': ['encode_*download*.sh', 'encode_*files*.csv', 'encode_*urls*.txt', 'download_*.sh'],
            'destination': '05_downloads/',
            'description': 'Download scripts and file lists'
        },
        
        # Log files
        {
            'pattern': ['*.log'],
            'destination': '04_logs/',
            'description': 'Processing log files'
        },
        
        # Documentation
        {
            'pattern': ['*.docx', '*.txt', 'files.txt'],
            'destination': '06_documentation/',
            'description': 'Documentation files'
        }
    ]
    
    moved_files = []
    
    for rule in file_rules:
        patterns = rule['pattern'] if isinstance(rule['pattern'], list) else [rule['pattern']]
        destination = rule['destination']
        exclude = rule.get('exclude', [])
        
        print(f"\n{rule['description']}:")
        
        for pattern in patterns:
            files = glob.glob(pattern)
            
            for file_path in files:
                # Skip if file matches exclude pattern
                if any(glob.fnmatch.fnmatch(os.path.basename(file_path), ex) for ex in exclude):
                    continue
                
                # Skip if already moved
                if file_path in moved_files:
                    continue
                
                # Skip if it's a directory
                if os.path.isdir(file_path):
                    continue
                
                try:
                    # Create destination directory if it doesn't exist
                    os.makedirs(destination, exist_ok=True)
                    
                    # Move file
                    filename = os.path.basename(file_path)
                    dest_path = os.path.join(destination, filename)
                    
                    # Handle duplicates
                    counter = 1
                    original_dest = dest_path
                    while os.path.exists(dest_path):
                        name, ext = os.path.splitext(original_dest)
                        dest_path = f"{name}_{counter}{ext}"
                        counter += 1
                    
                    shutil.move(file_path, dest_path)
                    print(f"  Moved: {file_path} -> {dest_path}")
                    moved_files.append(file_path)
                    
                except Exception as e:
                    print(f"  Error moving {file_path}: {e}")


def clean_empty_directories():
    """
    Remove empty directories.
    """
    print("\nCleaning up empty directories...")
    
    # Remove encode_downloads if empty
    if os.path.exists('encode_downloads') and not os.listdir('encode_downloads'):
        os.rmdir('encode_downloads')
        print("  Removed empty: encode_downloads/")
    
    # Remove cancer_type_consensus if empty
    if os.path.exists('cancer_type_consensus') and not os.listdir('cancer_type_consensus'):
        shutil.rmtree('cancer_type_consensus')
        print("  Removed empty: cancer_type_consensus/")


def create_readme():
    """
    Create README file explaining the folder structure.
    """
    readme_content = """# Chromatin Accessibility Analysis Project

This folder contains a comprehensive analysis of chromatin accessibility data from TCGA and ENCODE projects.

## Folder Structure

### 01_raw_data/
Raw data files organized by source:
- **tcga_narrowpeaks/**: TCGA ATAC-seq narrowPeak files (404 cancer samples)
- **encode_narrowpeaks/**: ENCODE ATAC-seq narrowPeak files (823 samples)
- **other_narrowpeaks/**: Other narrowPeak files from GEO and other sources
- **reference_files/**: Reference annotation files (genes, cancer gene census, etc.)

### 02_processed_data/
Processed analysis-ready data:
- **individual_windows/**: Individual sample files converted to genomic windows
- **combined_matrices/**: Combined matrices ready for analysis
  - TCGA_all_samples_combined_10kb.tsv (404 TCGA samples)
  - ENCODE_all_samples_combined_10kb.tsv (823 ENCODE samples)
  - TCGA_ENCODE_combined_matrix_10kb.tsv (1,227 total samples)
- **annotations/**: Sample metadata and mapping files

### 03_scripts/
Analysis and processing scripts:
- **processing/**: Data processing and conversion scripts
- **analysis/**: Analysis and visualization scripts
- **utilities/**: Utility and helper scripts

### 04_logs/
Log files from processing runs

### 05_downloads/
Download scripts and temporary files

### 06_documentation/
Documentation and notes

## Key Files

### Combined Matrices (02_processed_data/combined_matrices/)
- **TCGA_ENCODE_combined_matrix_10kb.tsv**: Master matrix with 1,227 samples
  - 404 TCGA cancer samples (BRCA_1, LUSC_1, etc.)
  - 823 ENCODE samples (K562_1, T_helper_17_cell_1, etc.)
  - 308,837 genomic windows (10kb resolution)
  - Ready for machine learning and comparative analysis

### Sample Annotations (02_processed_data/annotations/)
- **TCGA_ENCODE_combined_matrix_10kb_annotations.tsv**: Sample metadata
  - Source (TCGA/ENCODE)
  - Sample type (cancer/normal/cell_line)
  - Cancer type, cell type, biosample category

## Usage

1. **Load combined matrix**: Use TCGA_ENCODE_combined_matrix_10kb.tsv for analysis
2. **Sample metadata**: Use annotations file to understand sample types
3. **Individual samples**: Access raw narrowPeak files in 01_raw_data/
4. **Processing**: Use scripts in 03_scripts/ to reproduce or extend analysis

## Data Summary

- **Total samples**: 1,227 (404 TCGA + 823 ENCODE)
- **Genomic resolution**: 10kb windows
- **Genome coverage**: Main chromosomes (chr1-22, chrX, chrY)
- **File format**: Tab-separated values (TSV)
- **Analysis-ready**: Yes

## Contact

Generated by automated organization script.
Last updated: """ + __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """
"""
    
    with open('README.md', 'w') as f:
        f.write(readme_content)
    
    print("\nCreated: README.md")


def show_summary():
    """
    Show summary of organized folder.
    """
    print("\n" + "="*60)
    print("FOLDER ORGANIZATION COMPLETE")
    print("="*60)
    
    # Count files in each directory
    for root, dirs, files in os.walk('.'):
        if root == '.':
            continue
        
        level = root.replace('.', '').count(os.sep)
        indent = ' ' * 2 * level
        folder_name = os.path.basename(root)
        file_count = len([f for f in files if not f.startswith('.')])
        
        if file_count > 0:
            print(f"{indent}{folder_name}/ ({file_count} files)")
    
    print(f"\nKey files for analysis:")
    key_files = [
        '02_processed_data/combined_matrices/TCGA_ENCODE_combined_matrix_10kb.tsv',
        '02_processed_data/annotations/TCGA_ENCODE_combined_matrix_10kb_annotations.tsv',
        'README.md'
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024**2)
            print(f"  {file_path} ({size_mb:.1f} MB)")


def main():
    parser = argparse.ArgumentParser(description='Organize chromatin accessibility analysis folder')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be moved without moving')
    
    args = parser.parse_args()
    
    if args.dry_run:
        print("DRY RUN MODE - No files will be moved")
        print("="*50)
    
    # Create folder structure
    create_folder_structure()
    
    if not args.dry_run:
        # Organize files
        organize_files()
        
        # Clean up
        clean_empty_directories()
        
        # Create documentation
        create_readme()
        
        # Show summary
        show_summary()
    else:
        print("\nDry run completed. Use without --dry-run to actually organize files.")


if __name__ == "__main__":
    main()
