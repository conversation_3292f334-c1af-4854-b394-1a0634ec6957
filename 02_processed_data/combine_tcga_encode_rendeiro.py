#!/usr/bin/env python3

import pandas as pd
import numpy as np
import os
from pathlib import Path

def load_matrices_efficiently(tcga_encode_file, rendeiro_file):
    """
    Load both matrices efficiently by reading coordinates first.
    """
    print("Loading TCGA/ENCODE matrix coordinates...")
    tcga_coords = pd.read_csv(tcga_encode_file, sep='\t', usecols=['chr', 'start', 'end'])
    print(f"TCGA/ENCODE: {len(tcga_coords)} genomic windows")
    
    print("Loading Rendeiro matrix coordinates...")
    rendeiro_coords = pd.read_csv(rendeiro_file, sep='\t', usecols=['chr', 'start', 'end'])
    print(f"Rendeiro: {len(rendeiro_coords)} genomic regions")
    
    return tcga_coords, rendeiro_coords

def find_coordinate_overlap(tcga_coords, rendeiro_coords):
    """
    Find overlapping genomic regions between TCGA/ENCODE and Rendeiro matrices.
    """
    print("Finding coordinate overlap...")
    
    # Create coordinate keys for matching
    tcga_coords['coord_key'] = tcga_coords['chr'] + ':' + tcga_coords['start'].astype(str) + '-' + tcga_coords['end'].astype(str)
    rendeiro_coords['coord_key'] = rendeiro_coords['chr'] + ':' + rendeiro_coords['start'].astype(str) + '-' + rendeiro_coords['end'].astype(str)
    
    # Find exact matches
    exact_matches = set(tcga_coords['coord_key']).intersection(set(rendeiro_coords['coord_key']))
    print(f"Exact coordinate matches: {len(exact_matches)}")
    
    if len(exact_matches) > 1000:  # If we have good overlap, use exact matching
        print("Using exact coordinate matching...")
        return 'exact', exact_matches
    else:
        print("Limited exact matches. Will use TCGA/ENCODE coordinates as reference...")
        return 'tcga_reference', None

def create_10kb_windows_from_rendeiro(rendeiro_file, tcga_coords, output_file):
    """
    Convert Rendeiro data to 10kb windows matching TCGA/ENCODE coordinates.
    """
    print("Converting Rendeiro data to 10kb windows...")
    
    # Load Rendeiro matrix
    print("Loading Rendeiro matrix...")
    rendeiro_df = pd.read_csv(rendeiro_file, sep='\t')
    
    # Initialize result matrix with TCGA coordinates
    result_df = tcga_coords.copy()
    
    # Get Rendeiro sample columns (exclude coordinate columns)
    rendeiro_samples = [col for col in rendeiro_df.columns if col not in ['chr', 'start', 'end']]
    print(f"Processing {len(rendeiro_samples)} Rendeiro samples...")
    
    # Process each sample
    for i, sample in enumerate(rendeiro_samples):
        if i % 10 == 0:
            print(f"  Processing sample {i+1}/{len(rendeiro_samples)}: {sample}")
        
        # Initialize signal array for this sample
        signal_values = np.zeros(len(tcga_coords))
        
        # Process by chromosome for efficiency
        for chrom in tcga_coords['chr'].unique():
            # Get windows for this chromosome
            chrom_windows = tcga_coords[tcga_coords['chr'] == chrom].copy()
            chrom_rendeiro = rendeiro_df[rendeiro_df['chr'] == chrom].copy()
            
            if len(chrom_rendeiro) == 0:
                continue
            
            # For each TCGA window, find overlapping Rendeiro regions
            for window_idx, window in chrom_windows.iterrows():
                # Find Rendeiro regions that overlap with this window
                overlapping = chrom_rendeiro[
                    (chrom_rendeiro['start'] < window['end']) & 
                    (chrom_rendeiro['end'] > window['start'])
                ]
                
                if len(overlapping) > 0:
                    # Calculate weighted average based on overlap
                    total_signal = 0
                    total_weight = 0
                    
                    for _, region in overlapping.iterrows():
                        # Calculate overlap length
                        overlap_start = max(window['start'], region['start'])
                        overlap_end = min(window['end'], region['end'])
                        overlap_length = max(0, overlap_end - overlap_start)
                        
                        if overlap_length > 0:
                            weight = overlap_length / (region['end'] - region['start'])
                            total_signal += region[sample] * weight
                            total_weight += weight
                    
                    if total_weight > 0:
                        signal_values[tcga_coords.index.get_loc(window_idx)] = total_signal / total_weight
        
        # Add sample to result
        result_df[sample] = signal_values
    
    # Save result
    print(f"Saving converted matrix to {output_file}...")
    result_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"Conversion complete: {len(result_df)} windows × {len(result_df.columns)} columns")
    return result_df

def combine_matrices_horizontally(tcga_encode_file, rendeiro_10kb_file, output_file):
    """
    Combine TCGA/ENCODE and Rendeiro matrices horizontally.
    """
    print("Combining matrices horizontally...")
    
    # Load TCGA/ENCODE matrix
    print("Loading TCGA/ENCODE matrix...")
    tcga_df = pd.read_csv(tcga_encode_file, sep='\t')
    print(f"TCGA/ENCODE: {len(tcga_df)} windows × {len(tcga_df.columns)} columns")
    
    # Load Rendeiro 10kb matrix
    print("Loading Rendeiro 10kb matrix...")
    rendeiro_df = pd.read_csv(rendeiro_10kb_file, sep='\t')
    print(f"Rendeiro: {len(rendeiro_df)} windows × {len(rendeiro_df.columns)} columns")
    
    # Verify coordinates match
    coord_cols = ['chr', 'start', 'end']
    if not tcga_df[coord_cols].equals(rendeiro_df[coord_cols]):
        print("ERROR: Coordinates don't match between matrices!")
        return None
    
    # Get sample columns from Rendeiro (exclude coordinates)
    rendeiro_samples = [col for col in rendeiro_df.columns if col not in coord_cols]
    
    # Add Rendeiro samples to TCGA matrix
    for sample in rendeiro_samples:
        tcga_df[sample] = rendeiro_df[sample]
    
    # Save combined matrix
    print(f"Saving combined matrix to {output_file}...")
    tcga_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"Combined matrix: {len(tcga_df)} windows × {len(tcga_df.columns)} columns")
    print(f"Added {len(rendeiro_samples)} Rendeiro samples")
    
    return tcga_df

def combine_annotations(tcga_annotations_file, rendeiro_annotations_file, output_file):
    """
    Combine TCGA/ENCODE and Rendeiro annotations.
    """
    print("Combining annotations...")
    
    # Load annotations
    tcga_ann = pd.read_csv(tcga_annotations_file, sep='\t')
    rendeiro_ann = pd.read_csv(rendeiro_annotations_file, sep='\t')
    
    print(f"TCGA/ENCODE annotations: {len(tcga_ann)} samples")
    print(f"Rendeiro annotations: {len(rendeiro_ann)} samples")
    
    # Combine annotations
    combined_ann = pd.concat([tcga_ann, rendeiro_ann], ignore_index=True)
    
    # Save combined annotations
    combined_ann.to_csv(output_file, sep='\t', index=False)
    
    print(f"Combined annotations: {len(combined_ann)} samples")
    return combined_ann

def create_summary_stats(combined_matrix_file, combined_annotations_file, output_file):
    """
    Create summary statistics for the combined dataset.
    """
    print("Creating summary statistics...")
    
    # Load annotations for stats
    ann_df = pd.read_csv(combined_annotations_file, sep='\t')
    
    # Count samples by source
    source_counts = ann_df['source'].value_counts()
    
    # Count samples by cell type
    cell_type_counts = ann_df['cell_type'].value_counts()
    
    # Count samples by biosample category
    biosample_counts = ann_df['biosample_category'].value_counts()
    
    # Get matrix info
    with open(combined_matrix_file, 'r') as f:
        header = f.readline().strip().split('\t')
    
    total_samples = len(header) - 3  # Exclude chr, start, end
    
    # Write summary
    with open(output_file, 'w') as f:
        f.write("TCGA + ENCODE + Rendeiro CLL Combined Dataset Summary\n")
        f.write("=" * 55 + "\n\n")
        
        # Get number of windows
        temp_df = pd.read_csv(combined_matrix_file, sep='\t', nrows=1)
        num_windows = len(temp_df.columns) - 3
        f.write(f"Total genomic windows: {num_windows}\n")
        f.write(f"Total samples: {total_samples}\n\n")
        
        f.write("Samples by source:\n")
        for source, count in source_counts.items():
            f.write(f"  {source}: {count}\n")
        f.write("\n")
        
        f.write("Samples by cell type (top 20):\n")
        for cell_type, count in cell_type_counts.head(20).items():
            f.write(f"  {cell_type}: {count}\n")
        f.write("\n")
        
        f.write("Samples by biosample category:\n")
        for category, count in biosample_counts.items():
            f.write(f"  {category}: {count}\n")
    
    print(f"Summary saved to {output_file}")

def main():
    """
    Main function to integrate TCGA/ENCODE and Rendeiro matrices.
    """
    # File paths
    tcga_encode_file = "combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb.tsv"
    tcga_annotations_file = "combined_matrices/FINAL_TCGA_ENCODE_combined_matrix_10kb_annotations.tsv"
    
    rendeiro_file = "combined_matrices/Rendeiro_CLL_matrix.tsv"
    rendeiro_annotations_file = "combined_matrices/Rendeiro_CLL_annotations.tsv"
    
    # Output files
    rendeiro_10kb_file = "combined_matrices/Rendeiro_CLL_matrix_10kb.tsv"
    combined_matrix_file = "combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_matrix_10kb.tsv"
    combined_annotations_file = "combined_matrices/FINAL_TCGA_ENCODE_CLL_combined_annotations.tsv"
    summary_file = "combined_matrices/FINAL_TCGA_ENCODE_CLL_summary.txt"
    
    print("=== Integrating TCGA/ENCODE and Rendeiro CLL Data ===")
    
    # Step 1: Load and analyze coordinate systems
    tcga_coords, rendeiro_coords = load_matrices_efficiently(tcga_encode_file, rendeiro_file)
    
    # Step 2: Convert Rendeiro to 10kb windows
    rendeiro_10kb_df = create_10kb_windows_from_rendeiro(rendeiro_file, tcga_coords, rendeiro_10kb_file)
    
    # Step 3: Combine matrices
    combined_df = combine_matrices_horizontally(tcga_encode_file, rendeiro_10kb_file, combined_matrix_file)
    
    # Step 4: Combine annotations
    combined_ann = combine_annotations(tcga_annotations_file, rendeiro_annotations_file, combined_annotations_file)
    
    # Step 5: Create summary
    create_summary_stats(combined_matrix_file, combined_annotations_file, summary_file)
    
    print("\n=== Integration Complete ===")
    print(f"Combined matrix: {combined_matrix_file}")
    print(f"Combined annotations: {combined_annotations_file}")
    print(f"Summary: {summary_file}")

if __name__ == "__main__":
    main()
