#!/usr/bin/env python3
"""
Quick start script to download BOCA brain ATAC-seq data.
Downloads pre-processed peak files and creates narrowPeak format.
"""

import os
import requests
import zipfile
import pandas as pd
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_file(url, output_path):
    """Download a file from URL."""
    logger.info(f"Downloading {output_path.name}...")
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        logger.info(f"Successfully downloaded {output_path.name}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to download {output_path.name}: {e}")
        return False

def main():
    """Quick start download of BOCA data."""
    
    # Create directories
    boca_dir = Path("01_raw_data/boca_brain")
    boca_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info("BOCA Brain Open Chromatin Atlas - Quick Start Download")
    logger.info("=" * 60)
    
    # Download URLs
    downloads = [
        {
            'name': 'Region-specific peaks (hg19)',
            'url': 'https://bendlj01.dmz.hpc.mssm.edu/multireg/resources/boca_peaks.zip',
            'file': boca_dir / 'boca_peaks_hg19.zip'
        },
        {
            'name': 'Consensus peaks (hg19)',
            'url': 'https://multireg.s3.amazonaws.com/resources/boca_peaks_consensus_no_blacklisted_regions.bed',
            'file': boca_dir / 'boca_consensus_peaks_hg19.bed'
        },
        {
            'name': 'Raw count matrix',
            'url': 'https://multireg.s3.amazonaws.com/resources/boca_raw_count_matrix.tsv.gz',
            'file': boca_dir / 'boca_raw_count_matrix.tsv.gz'
        },
        {
            'name': 'Normalized count matrix',
            'url': 'https://multireg.s3.amazonaws.com/resources/boca_norm_count_matrix.tsv.gz',
            'file': boca_dir / 'boca_norm_count_matrix.tsv.gz'
        }
    ]
    
    # Download files
    success_count = 0
    for item in downloads:
        if item['file'].exists():
            logger.info(f"Skipping {item['name']} - already exists")
            success_count += 1
        else:
            if download_file(item['url'], item['file']):
                success_count += 1
    
    logger.info(f"Downloaded {success_count}/{len(downloads)} files")
    
    # Extract zip file
    zip_file = boca_dir / 'boca_peaks_hg19.zip'
    if zip_file.exists():
        logger.info("Extracting region-specific peaks...")
        peaks_dir = boca_dir / 'peaks'
        peaks_dir.mkdir(exist_ok=True)
        
        try:
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(peaks_dir)
            logger.info("Successfully extracted peak files")
            
            # List extracted files
            extracted_files = list(peaks_dir.glob("*"))
            logger.info(f"Extracted {len(extracted_files)} files")
            
        except Exception as e:
            logger.error(f"Failed to extract zip file: {e}")
    
    # Create sample information
    logger.info("Creating sample information...")
    
    sample_info = {
        'dataset': 'BOCA Brain Open Chromatin Atlas',
        'paper': 'Fullard et al. (2018) Genome Research',
        'pmid': '29945882',
        'geo_accession': 'GSE96949',
        'total_samples': 115,
        'subjects': 5,
        'cell_types': ['Neuronal', 'Non-neuronal'],
        'brain_regions': [
            'Anterior Cingulate Cortex', 'Amygdala', 'Dorsolateral Prefrontal Cortex',
            'Hippocampus', 'Insula', 'Inferior Temporal Cortex', 'Mediodorsal Thalamus',
            'Nucleus Accumbens', 'Orbitofrontal Cortex', 'Primary Motor Cortex',
            'Putamen', 'Primary Visual Cortex', 'Superior Temporal Cortex',
            'Ventrolateral Prefrontal Cortex'
        ],
        'data_type': 'ATAC-seq',
        'genome_build': 'GRCh37/hg19',
        'file_formats': ['BigWig', 'BED peaks', 'Count matrices']
    }
    
    # Save sample info
    info_file = boca_dir / 'dataset_info.txt'
    with open(info_file, 'w') as f:
        f.write("BOCA Brain Open Chromatin Atlas Dataset Information\n")
        f.write("=" * 50 + "\n\n")
        
        for key, value in sample_info.items():
            f.write(f"{key.replace('_', ' ').title()}: ")
            if isinstance(value, list):
                f.write(f"\n")
                for item in value:
                    f.write(f"  - {item}\n")
            else:
                f.write(f"{value}\n")
            f.write("\n")
    
    logger.info(f"Saved dataset information: {info_file}")
    
    # Next steps
    logger.info("\n" + "=" * 60)
    logger.info("DOWNLOAD COMPLETE!")
    logger.info("=" * 60)
    logger.info("\nNext steps:")
    logger.info("1. To download BigWig files and convert to narrowPeak:")
    logger.info("   python3 03_scripts/processing/process_boca_brain_data.py --download --convert")
    logger.info("\n2. To use pre-processed peaks:")
    logger.info("   python3 03_scripts/processing/download_boca_peaks.py")
    logger.info("\n3. To integrate with ENCODE/TCGA pipeline:")
    logger.info("   python3 03_scripts/processing/integrate_boca_with_pipeline.py --all")
    logger.info("\nFiles downloaded to: 01_raw_data/boca_brain/")

if __name__ == "__main__":
    main()
