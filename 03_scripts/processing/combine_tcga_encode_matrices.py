#!/usr/bin/env python3
"""
Combine TCGA and ENCODE matrices into a single comprehensive matrix.
"""

import pandas as pd
import argparse
import os


def combine_tcga_encode_matrices(tcga_file, encode_file, output_file=None):
    """
    Combine TCGA and ENCODE matrices.
    
    Args:
        tcga_file: Path to TCGA matrix file
        encode_file: Path to ENCODE matrix file
        output_file: Output file name
    """
    print("Combining TCGA and ENCODE matrices...")
    
    # Load matrices
    print("Loading TCGA matrix...")
    tcga_df = pd.read_csv(tcga_file, sep='\t')
    print(f"TCGA matrix: {len(tcga_df)} windows × {len(tcga_df.columns)} columns")
    
    print("Loading ENCODE matrix...")
    encode_df = pd.read_csv(encode_file, sep='\t')
    print(f"ENCODE matrix: {len(encode_df)} windows × {len(encode_df.columns)} columns")
    
    # Verify coordinates match
    coords_match = (
        (tcga_df['chr'] == encode_df['chr']).all() and
        (tcga_df['start'] == encode_df['start']).all() and
        (tcga_df['end'] == encode_df['end']).all()
    )
    
    if coords_match:
        print("✓ Window coordinates match perfectly!")
        
        # Get sample columns (exclude coordinate columns)
        tcga_samples = [col for col in tcga_df.columns if col not in ['chr', 'start', 'end']]
        encode_samples = [col for col in encode_df.columns if col not in ['chr', 'start', 'end']]
        
        # Combine matrices
        combined_df = tcga_df.copy()
        
        # Add ENCODE columns
        for col in encode_samples:
            combined_df[col] = encode_df[col]
        
        print(f"✓ Combined matrix created!")
        print(f"TCGA samples: {len(tcga_samples)}")
        print(f"ENCODE samples: {len(encode_samples)}")
        print(f"Total samples: {len(tcga_samples) + len(encode_samples)}")
        
    else:
        print("Window coordinates don't match. Using merge approach...")
        
        # Create merge keys
        tcga_df['merge_key'] = tcga_df['chr'] + ':' + tcga_df['start'].astype(str) + '-' + tcga_df['end'].astype(str)
        encode_df['merge_key'] = encode_df['chr'] + ':' + encode_df['start'].astype(str) + '-' + encode_df['end'].astype(str)
        
        # Get sample columns
        encode_samples = [col for col in encode_df.columns if col not in ['chr', 'start', 'end', 'merge_key']]
        
        # Merge
        combined_df = pd.merge(
            tcga_df,
            encode_df[['merge_key'] + encode_samples],
            on='merge_key',
            how='left'
        )
        
        # Fill missing values and remove merge key
        for col in encode_samples:
            combined_df[col] = combined_df[col].fillna(0)
        
        combined_df = combined_df.drop('merge_key', axis=1)
        
        tcga_samples = [col for col in tcga_df.columns if col not in ['chr', 'start', 'end', 'merge_key']]
        
        print(f"✓ Combined matrix created using merge!")
        print(f"TCGA samples: {len(tcga_samples)}")
        print(f"ENCODE samples: {len(encode_samples)}")
        print(f"Total samples: {len(tcga_samples) + len(encode_samples)}")
    
    # Generate output filename if not provided
    if output_file is None:
        output_file = "TCGA_ENCODE_combined_matrix_10kb.tsv"
    
    # Save combined matrix
    print(f"Saving combined matrix to {output_file}...")
    combined_df.to_csv(output_file, sep='\t', index=False)
    
    # Summary statistics
    print(f"\n{'='*60}")
    print(f"Combined Matrix Summary")
    print(f"{'='*60}")
    print(f"Total windows: {len(combined_df)}")
    print(f"Total columns: {len(combined_df.columns)}")
    print(f"File size: {os.path.getsize(output_file) / (1024**3):.2f} GB")
    
    # Show sample of column names
    all_samples = [col for col in combined_df.columns if col not in ['chr', 'start', 'end']]
    
    print(f"\nSample column structure:")
    print(f"First 10 TCGA samples:")
    tcga_sample_cols = [col for col in all_samples if any(cancer in col for cancer in ['BRCA_', 'LUSC_', 'LUAD_', 'BLCA_', 'COAD_', 'GBM_', 'PRAD_', 'CHOL_', 'LGG_', 'UNKNOWN_'])]
    for i, col in enumerate(tcga_sample_cols[:10]):
        print(f"  {col}")
    
    print(f"\nFirst 10 ENCODE samples:")
    encode_sample_cols = [col for col in all_samples if col not in tcga_sample_cols]
    for i, col in enumerate(encode_sample_cols[:10]):
        print(f"  {col}")
    
    if len(encode_sample_cols) > 10:
        print(f"  ... and {len(encode_sample_cols) - 10} more ENCODE samples")
    
    # Show data sample
    print(f"\nSample of combined matrix (first 3 rows, last 5 columns):")
    print(combined_df.iloc[:3, -5:])
    
    return output_file


def create_sample_annotation(combined_file):
    """
    Create a sample annotation file for the combined matrix.
    """
    print("Creating sample annotation file...")
    
    # Load the combined matrix to get column names
    df = pd.read_csv(combined_file, sep='\t', nrows=1)
    sample_columns = [col for col in df.columns if col not in ['chr', 'start', 'end']]
    
    annotations = []
    
    for sample in sample_columns:
        # Determine sample type and category
        if any(cancer in sample for cancer in ['BRCA_', 'LUSC_', 'LUAD_', 'BLCA_', 'COAD_', 'GBM_', 'PRAD_', 'CHOL_', 'LGG_', 'UNKNOWN_']):
            # TCGA cancer sample
            cancer_type = sample.split('_')[0]
            sample_number = sample.split('_')[1] if '_' in sample else '1'
            
            annotations.append({
                'sample_id': sample,
                'source': 'TCGA',
                'sample_type': 'cancer',
                'cancer_type': cancer_type,
                'cell_type': f"{cancer_type}_tumor",
                'biosample_category': 'tumor_tissue',
                'sample_number': sample_number
            })
            
        elif sample == 'HEART_LEFT_VENTRICLE':
            # Heart tissue sample
            annotations.append({
                'sample_id': sample,
                'source': 'ENCODE',
                'sample_type': 'normal_tissue',
                'cancer_type': 'NA',
                'cell_type': 'heart_left_ventricle',
                'biosample_category': 'normal_tissue',
                'sample_number': '1'
            })
            
        else:
            # ENCODE cell line/primary cell sample
            # Determine cell type category
            if 'K562' in sample:
                cell_category = 'cell_line'
                cell_type = 'K562_leukemia'
            elif any(term in sample.lower() for term in ['cd4', 'cd8', 't_cell', 't_helper', 'regulatory']):
                cell_category = 'immune_cell'
                cell_type = sample.replace('_', ' ').lower()
            else:
                cell_category = 'other_cell'
                cell_type = sample.replace('_', ' ').lower()
            
            # Extract sample number
            sample_number = sample.split('_')[-1] if sample.split('_')[-1].isdigit() else '1'
            
            annotations.append({
                'sample_id': sample,
                'source': 'ENCODE',
                'sample_type': 'cell_culture',
                'cancer_type': 'NA',
                'cell_type': cell_type,
                'biosample_category': cell_category,
                'sample_number': sample_number
            })
    
    # Create annotation DataFrame
    annotation_df = pd.DataFrame(annotations)
    
    # Save annotation file
    annotation_file = combined_file.replace('.tsv', '_annotations.tsv')
    annotation_df.to_csv(annotation_file, sep='\t', index=False)
    
    print(f"Saved sample annotations to: {annotation_file}")
    
    # Summary
    print(f"\nSample annotation summary:")
    print(f"Total samples: {len(annotation_df)}")
    print(f"\nBy source:")
    print(annotation_df['source'].value_counts())
    print(f"\nBy sample type:")
    print(annotation_df['sample_type'].value_counts())
    print(f"\nBy biosample category:")
    print(annotation_df['biosample_category'].value_counts())
    
    return annotation_file


def main():
    parser = argparse.ArgumentParser(description='Combine TCGA and ENCODE matrices')
    parser.add_argument('--tcga-file', default='TCGA_all_samples_with_HEART_10kb.tsv',
                       help='TCGA matrix file (default: TCGA_all_samples_with_HEART_10kb.tsv)')
    parser.add_argument('--encode-file', default='ENCODE_all_samples_combined_10kb.tsv',
                       help='ENCODE matrix file (default: ENCODE_all_samples_combined_10kb.tsv)')
    parser.add_argument('--output', default='TCGA_ENCODE_combined_matrix_10kb.tsv',
                       help='Output file name')
    parser.add_argument('--create-annotations', action='store_true',
                       help='Create sample annotation file')
    
    args = parser.parse_args()
    
    # Check input files exist
    if not os.path.exists(args.tcga_file):
        print(f"Error: TCGA file {args.tcga_file} not found")
        return
    
    if not os.path.exists(args.encode_file):
        print(f"Error: ENCODE file {args.encode_file} not found")
        return
    
    # Combine matrices
    combined_file = combine_tcga_encode_matrices(args.tcga_file, args.encode_file, args.output)
    
    # Create annotations if requested
    if args.create_annotations:
        create_sample_annotation(combined_file)
    
    print(f"\n✓ Matrix combination completed!")
    print(f"Combined matrix: {combined_file}")


if __name__ == "__main__":
    main()
