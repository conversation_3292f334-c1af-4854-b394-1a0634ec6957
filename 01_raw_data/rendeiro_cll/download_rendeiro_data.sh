#!/bin/bash

# Download script for <PERSON><PERSON><PERSON> et al. CLL chromatin accessibility data
# Paper: Chromatin mapping and single-cell immune profiling define the temporal dynamics of ibrutinib response in CLL
# Nature Communications (2020), PMID: 31996669
# GEO Accession: GSE111013 (ATAC-seq), GSE111014 (scRNA-seq), GSE111015 (SuperSeries)

set -e  # Exit on any error

# Create directory structure
echo "Creating directory structure..."
mkdir -p 01_raw_data/rendeiro_cll/{atac_seq,scrna_seq,metadata}

# Change to the data directory
cd 01_raw_data/rendeiro_cll

echo "=== Downloading Rendeiro et al. CLL Chromatin Accessibility Data ==="
echo "Study: Chromatin mapping and single-cell immune profiling define the temporal dynamics of ibrutinib response in CLL"
echo "Authors: <AUTHORS>
echo "Journal: Nature Communications (2020)"
echo "PMID: 31996669"
echo ""

# Download metadata files
echo "Downloading metadata files..."
cd metadata

# Download series matrix files for sample information
echo "Downloading ATAC-seq metadata (GSE111013)..."
wget -O GSE111013_series_matrix.txt.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111013/matrix/GSE111013_series_matrix.txt.gz"

echo "Downloading scRNA-seq metadata (GSE111014)..."
wget -O GSE111014_series_matrix.txt.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111014/matrix/GSE111014_series_matrix.txt.gz"

# Download SOFT files for detailed sample information
echo "Downloading detailed sample information..."
wget -O GSE111013_family.soft.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111013/soft/GSE111013_family.soft.gz"
wget -O GSE111014_family.soft.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111014/soft/GSE111014_family.soft.gz"

cd ..

# Download ATAC-seq processed data (BigWig and narrowPeak files)
echo ""
echo "Downloading ATAC-seq processed data (19.6 GB)..."
echo "This includes BigWig and narrowPeak files for 176 samples"
cd atac_seq

# Download the large TAR file containing all processed ATAC-seq data
wget -O GSE111013_RAW.tar "https://www.ncbi.nlm.nih.gov/geo/download/?acc=GSE111013&format=file"

echo "Extracting ATAC-seq files..."
tar -xf GSE111013_RAW.tar

# Organize files by type
echo "Organizing files by type..."
mkdir -p bigwig narrowpeak

# Move BigWig files
find . -name "*.bw" -exec mv {} bigwig/ \;
find . -name "*.bigwig" -exec mv {} bigwig/ \;

# Move narrowPeak files  
find . -name "*.narrowPeak*" -exec mv {} narrowpeak/ \;

# Clean up
rm GSE111013_RAW.tar

cd ..

# Download scRNA-seq processed data
echo ""
echo "Downloading scRNA-seq processed data..."
cd scrna_seq

# Download count matrix files
echo "Downloading scRNA-seq count matrices..."
wget -O GSE111014_barcodes.tsv.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111014/suppl/GSE111014_barcodes.tsv.gz"
wget -O GSE111014_genes.tsv.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111014/suppl/GSE111014_genes.tsv.gz"
wget -O GSE111014_matrix.mtx.gz "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE111nnn/GSE111014/suppl/GSE111014_matrix.mtx.gz"

cd ..

echo ""
echo "=== Download Summary ==="
echo "ATAC-seq data: 176 samples (BigWig and narrowPeak files)"
echo "scRNA-seq data: 12 samples (count matrices)"
echo "Metadata: Series matrix and SOFT files"
echo ""
echo "Cell types included:"
echo "- Normal donors: B-cells, CD4+ T-cells, CD8+ T-cells, T-cells, Naive B-cells, Switched B-cells, Unswitched B-cells"
echo "- CLL patients: CLL cells, B-cells, CD4+ T-cells, CD8+ T-cells, NK cells, Monocytes"
echo "- Time points: 0, 1, 2, 3, 8, 30, 120, 150, 240, 280 days of ibrutinib treatment"
echo ""
echo "Genome build: GRCh37/hg19"
echo ""
echo "Download completed successfully!"
echo "Data location: $(pwd)"

# Create a summary file
echo "Creating data summary file..."
cat > data_summary.txt << EOF
Rendeiro et al. CLL Chromatin Accessibility Dataset
==================================================

Study Information:
- Title: Chromatin mapping and single-cell immune profiling define the temporal dynamics of ibrutinib response in CLL
- Authors: <AUTHORS>
- Journal: Nature Communications (2020)
- PMID: 31996669
- DOI: https://doi.org/10.1038/s41467-019-14081-6

GEO Accessions:
- SuperSeries: GSE111015
- ATAC-seq: GSE111013 (176 samples)
- scRNA-seq: GSE111014 (12 samples)

Dataset Description:
- 176 ATAC-seq samples of primary immune cell types
- 12 scRNA-seq samples from CLL patients
- Normal donors: 3 individuals
- CLL patients: 8 individuals
- Genome build: GRCh37/hg19

Cell Types:
Normal Donors:
- B-cells
- CD4+ T-cells  
- CD8+ T-cells
- T-cells
- Naive B-cells
- Switched B-cells
- Unswitched B-cells

CLL Patients:
- CLL cells
- B-cells
- CD4+ T-cells
- CD8+ T-cells
- NK cells
- Monocytes

Treatment Time Points:
- Day 0 (baseline)
- Day 1, 2, 3 (early response)
- Day 8, 30 (intermediate response)  
- Day 120, 150, 240, 280 (late response)

File Types:
ATAC-seq:
- BigWig files (signal tracks)
- narrowPeak files (peak calls)

scRNA-seq:
- Count matrix (MTX format)
- Gene annotations (TSV)
- Cell barcodes (TSV)

Download Date: $(date)
EOF

echo ""
echo "Data summary saved to: data_summary.txt"
