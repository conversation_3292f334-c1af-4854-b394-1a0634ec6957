TCGA + ENCODE + Rendeiro CLL Combined Dataset Summary
=======================================================

IMPORTANT NOTE:
This is a CONCEPTUAL combination. The Rendeiro data uses different
genomic coordinates than TCGA/ENCODE and would need coordinate
conversion for proper integration.

TCGA/ENCODE genomic windows: 308,837 (10kb windows)
Rendeiro genomic regions: 6,370,526 (variable-size peaks)
Total samples: 1383

Samples by source:
  ENCODE: 824
  TCGA: 404
  Rendeiro_CLL: 155

Rendeiro samples by cell type:
  CLL_cell: 33
  CD8_T_cell: 32
  CD4_T_cell: 31
  natural_killer_cell: 23
  B_cell: 21
  monocyte: 15

Samples by biosample category:
  other_cell: 555
  tumor_tissue: 437
  cell_line: 195
  immune_cell: 195
  normal_tissue: 1

NEXT STEPS FOR PROPER INTEGRATION:
1. Convert Rendeiro peaks to 10kb windows matching TCGA/ENCODE
2. Use bedtools or similar for coordinate conversion
3. Aggregate Rendeiro signals within each 10kb window
4. Then perform horizontal matrix concatenation
