#!/usr/bin/env python3
"""
Analyze TCGA samples to identify cancer types and create cancer-type-specific analyses.
"""

import pandas as pd
import glob
import os
import re
from collections import defaultdict, Counter
import argparse


def extract_cancer_type_from_barcode(barcode):
    """
    Extract cancer type from TCGA barcode.
    TCGA barcodes follow pattern: TCGA-XX-XXXX-XXX
    The cancer type is encoded in the first part.
    """
    # Common TCGA cancer type mappings based on project codes
    cancer_type_mapping = {
        'TCGA-DJ': 'CHOL',  # Cholangiocarcinoma
        'TCGA-AR': 'BRCA',  # Breast invasive carcinoma
        'TCGA-77': 'COAD',  # Colon adenocarcinoma
        'TCGA-P4': 'LUAD',  # Lung adenocarcinoma
        'TCGA-BR': 'BRCA',  # Breast invasive carcinoma
        'TCGA-OR': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-3C': 'LUAD',  # Lung adenocarcinoma
        'TCGA-A2': 'BRCA',  # Breast invasive carcinoma
        'TCGA-B9': 'BRCA',  # Breast invasive carcinoma
        'TCGA-B5': 'BRCA',  # Breast invasive carcinoma
        'TCGA-D9': 'GBM',   # Glioblastoma multiforme
        'TCGA-NH': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-AA': 'BRCA',  # Breast invasive carcinoma
        'TCGA-MP': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-IG': 'LUAD',  # Lung adenocarcinoma
        'TCGA-CC': 'BRCA',  # Breast invasive carcinoma
        'TCGA-BJ': 'BRCA',  # Breast invasive carcinoma
        'TCGA-76': 'COAD',  # Colon adenocarcinoma
        'TCGA-BH': 'BRCA',  # Breast invasive carcinoma
        'TCGA-85': 'BRCA',  # Breast invasive carcinoma
        'TCGA-FD': 'LUAD',  # Lung adenocarcinoma
        'TCGA-A8': 'BRCA',  # Breast invasive carcinoma
        'TCGA-F9': 'LUAD',  # Lung adenocarcinoma
        'TCGA-P5': 'LUAD',  # Lung adenocarcinoma
        'TCGA-98': 'LUAD',  # Lung adenocarcinoma
        'TCGA-AO': 'BRCA',  # Breast invasive carcinoma
        'TCGA-SH': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-KU': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-93': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-W9': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-UZ': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-LN': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-CF': 'BRCA',  # Breast invasive carcinoma
        'TCGA-L5': 'LUAD',  # Lung adenocarcinoma
        'TCGA-MF': 'LUAD',  # Lung adenocarcinoma
        'TCGA-B8': 'BRCA',  # Breast invasive carcinoma
        'TCGA-ZG': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-IA': 'BRCA',  # Breast invasive carcinoma
        'TCGA-D7': 'LUAD',  # Lung adenocarcinoma
        'TCGA-QT': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-BC': 'BRCA',  # Breast invasive carcinoma
        'TCGA-BG': 'BRCA',  # Breast invasive carcinoma
        'TCGA-HF': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-S3': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-EJ': 'BLCA',  # Bladder urothelial carcinoma
        'TCGA-86': 'COAD',  # Colon adenocarcinoma
        'TCGA-33': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-QG': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-HE': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-RM': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-DD': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-2G': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-OX': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-PA': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-AP': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-B2': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-06': 'GBM',   # Glioblastoma multiforme
        'TCGA-B1': 'BRCA',  # Breast invasive carcinoma
        'TCGA-BA': 'BRCA',  # Breast invasive carcinoma
        'TCGA-GK': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-3U': 'LUAD',  # Lung adenocarcinoma
        'TCGA-LG': 'LGG',   # Lower grade glioma
        'TCGA-J1': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-AQ': 'BRCA',  # Breast invasive carcinoma
        'TCGA-3X': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-VQ': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-W7': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-LK': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-DV': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-RT': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-F6': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-C8': 'BRCA',  # Breast invasive carcinoma
        'TCGA-ZH': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-CD': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-DU': 'PRAD',  # Prostate adenocarcinoma
        'TCGA-EB': 'PRAD',  # Prostate adenocarcinoma
        'TCGA-BK': 'BRCA',  # Breast invasive carcinoma
        'TCGA-MQ': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-44': 'COAD',  # Colon adenocarcinoma
        'TCGA-A7': 'BRCA',  # Breast invasive carcinoma
        'TCGA-IC': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-4C': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-WB': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-GD': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-G3': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-YL': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-XV': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-E1': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-QL': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-KC': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-73': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-BL': 'BRCA',  # Breast invasive carcinoma
        'TCGA-4A': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-4B': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-A6': 'BRCA',  # Breast invasive carcinoma
        'TCGA-L4': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-KV': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-DM': 'BRCA',  # Breast invasive carcinoma
        'TCGA-PK': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-EA': 'LUAD',  # Lung adenocarcinoma
        'TCGA-AY': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-PJ': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-FG': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-H4': 'COAD',  # Colon adenocarcinoma
        'TCGA-DK': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-SS': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-D3': 'GBM',   # Glioblastoma multiforme
        'TCGA-F7': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-2K': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-XE': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-Q2': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-6D': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-VP': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-W6': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-VN': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-37': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-CJ': 'COAD',  # Colon adenocarcinoma
        'TCGA-91': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-MM': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-M9': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-4H': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-49': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-DB': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-63': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-4N': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-L7': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-D6': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-L9': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-CN': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-DA': 'GBM',   # Glioblastoma multiforme
        'TCGA-O2': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-RR': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-4W': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-CU': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-RW': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-TT': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-4K': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-D8': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-SU': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-3L': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-AD': 'COAD',  # Colon adenocarcinoma
        'TCGA-CE': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-CV': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-GL': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-H7': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-XC': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-NJ': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-GV': 'LUSC',  # Lung squamous cell carcinoma
        'TCGA-A8': 'BRCA',  # Breast invasive carcinoma
    }
    
    # Extract the first part of the barcode (e.g., TCGA-DJ from TCGA-DJ-A13X-01A)
    parts = barcode.split('-')
    if len(parts) >= 2:
        prefix = f"{parts[0]}-{parts[1]}"
        return cancer_type_mapping.get(prefix, 'UNKNOWN')
    
    return 'UNKNOWN'


def analyze_tcga_samples():
    """
    Analyze all TCGA narrowPeak files to identify cancer types and create mapping.
    """
    # Find all TCGA narrowPeak files
    narrowpeak_files = glob.glob('TCGA_ATAC_*.narrowPeak')
    
    if not narrowpeak_files:
        print("No TCGA narrowPeak files found!")
        return None
    
    print(f"Found {len(narrowpeak_files)} TCGA narrowPeak files")
    
    # Extract sample information
    samples_info = []
    
    for file_path in narrowpeak_files:
        filename = os.path.basename(file_path)
        # Extract TCGA barcode from filename
        # Format: TCGA_ATAC_TCGA-XX-XXXX-XXX.narrowPeak
        barcode_match = re.search(r'TCGA-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+', filename)
        
        if barcode_match:
            barcode = barcode_match.group()
            cancer_type = extract_cancer_type_from_barcode(barcode)
            
            samples_info.append({
                'filename': filename,
                'barcode': barcode,
                'cancer_type': cancer_type,
                'file_path': file_path
            })
    
    # Create DataFrame
    samples_df = pd.DataFrame(samples_info)
    
    # Count samples per cancer type
    cancer_counts = samples_df['cancer_type'].value_counts()
    
    print(f"\nCancer type distribution:")
    for cancer_type, count in cancer_counts.items():
        print(f"  {cancer_type}: {count} samples")
    
    # Create numbered annotations for duplicates
    cancer_type_counters = defaultdict(int)
    samples_df['cancer_type_numbered'] = ''
    
    for idx, row in samples_df.iterrows():
        cancer_type = row['cancer_type']
        cancer_type_counters[cancer_type] += 1
        
        if cancer_counts[cancer_type] > 1:
            # Multiple samples of this cancer type
            numbered_name = f"{cancer_type}_{cancer_type_counters[cancer_type]}"
        else:
            # Single sample of this cancer type
            numbered_name = cancer_type
        
        samples_df.at[idx, 'cancer_type_numbered'] = numbered_name
    
    return samples_df


def create_cancer_type_consensus(samples_df, window_size=10000):
    """
    Create consensus narrowPeak files for each cancer type.
    """
    print(f"\nCreating cancer type consensus files...")
    
    # Group by cancer type (not numbered)
    cancer_groups = samples_df.groupby('cancer_type')
    
    consensus_files = []
    
    for cancer_type, group in cancer_groups:
        if len(group) == 1:
            # Single sample - just copy/rename
            sample = group.iloc[0]
            input_file = sample['file_path']
            output_file = f"consensus_{cancer_type}.narrowPeak"
            
            # Copy file
            import shutil
            shutil.copy2(input_file, output_file)
            consensus_files.append(output_file)
            
            print(f"  {cancer_type}: Copied single sample to {output_file}")
            
        else:
            # Multiple samples - create consensus
            print(f"  {cancer_type}: Creating consensus from {len(group)} samples")
            
            # For now, just use the first sample as representative
            # In a real analysis, you might want to merge peaks or take intersections
            sample = group.iloc[0]
            input_file = sample['file_path']
            output_file = f"consensus_{cancer_type}.narrowPeak"
            
            import shutil
            shutil.copy2(input_file, output_file)
            consensus_files.append(output_file)
            
            print(f"    Using {sample['barcode']} as representative")
    
    return consensus_files


def main():
    parser = argparse.ArgumentParser(description='Analyze TCGA cancer types and create cancer-specific analyses')
    parser.add_argument('--create-consensus', action='store_true', 
                       help='Create consensus narrowPeak files for each cancer type')
    parser.add_argument('--window-size', type=int, default=10000,
                       help='Window size for genomic analysis (default: 10000)')
    parser.add_argument('--output-mapping', default='tcga_cancer_type_mapping.tsv',
                       help='Output file for sample-to-cancer-type mapping')
    
    args = parser.parse_args()
    
    # Analyze samples
    samples_df = analyze_tcga_samples()
    
    if samples_df is None:
        return
    
    # Save mapping file
    samples_df.to_csv(args.output_mapping, sep='\t', index=False)
    print(f"\nSaved sample mapping to {args.output_mapping}")
    
    # Display numbered cancer types
    print(f"\nNumbered cancer type annotations:")
    for _, row in samples_df.iterrows():
        print(f"  {row['barcode']} -> {row['cancer_type_numbered']}")
    
    # Create consensus files if requested
    if args.create_consensus:
        consensus_files = create_cancer_type_consensus(samples_df, args.window_size)
        print(f"\nCreated {len(consensus_files)} consensus files")


if __name__ == "__main__":
    main()
