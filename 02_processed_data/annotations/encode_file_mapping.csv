accession,file_size,assembly,biosample_term_name,biosample_type,lab,dataset_accession,download_url,encode_label
ENCFF926KTI,1268446,G<PERSON>h38,K562,cell line,"Will Greenleaf, Stanford",ENCSR859USB,https://www.encodeproject.org/files/ENCFF926KTI/@@download/ENCFF926KTI.bed.gz,K562_1
ENCFF046GBZ,941262,G<PERSON>h38,K562,cell line,"Will Greenleaf, Stanford",ENCSR859USB,https://www.encodeproject.org/files/ENCFF046GBZ/@@download/ENCFF046GBZ.bed.gz,K562_2
ENCFF202HWV,700804,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR859USB,https://www.encodeproject.org/files/ENCFF202HWV/@@download/ENCFF202HWV.bed.gz,K562_3
ENCFF489GQF,1580278,G<PERSON><PERSON>38,K562,cell line,"<PERSON>, Stanford",ENCSR956DNB,https://www.encodeproject.org/files/ENCFF489GQF/@@download/ENCFF489GQF.bed.gz,K562_4
ENCFF540FNK,1259781,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR956DNB,https://www.encodeproject.org/files/ENCFF540FNK/@@download/ENCFF540FNK.bed.gz,K562_5
ENCFF738NOA,848664,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR956DNB,https://www.encodeproject.org/files/ENCFF738NOA/@@download/ENCFF738NOA.bed.gz,K562_6
ENCFF855OHJ,2777006,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR863TVR,https://www.encodeproject.org/files/ENCFF855OHJ/@@download/ENCFF855OHJ.bed.gz,stim_act_nv_CD8_pos_ab_T__1
ENCFF753NRZ,1337916,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR863TVR,https://www.encodeproject.org/files/ENCFF753NRZ/@@download/ENCFF753NRZ.bed.gz,stim_act_nv_CD8_pos_ab_T__2
ENCFF767EUF,2444596,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR863TVR,https://www.encodeproject.org/files/ENCFF767EUF/@@download/ENCFF767EUF.bed.gz,stim_act_nv_CD8_pos_ab_T__3
ENCFF767YBU,3145005,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR830ALP,https://www.encodeproject.org/files/ENCFF767YBU/@@download/ENCFF767YBU.bed.gz,activated_T_cell_1
ENCFF847WTI,2493144,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR830ALP,https://www.encodeproject.org/files/ENCFF847WTI/@@download/ENCFF847WTI.bed.gz,activated_T_cell_2
ENCFF564OCG,2543233,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR830ALP,https://www.encodeproject.org/files/ENCFF564OCG/@@download/ENCFF564OCG.bed.gz,activated_T_cell_3
ENCFF999QSA,1011845,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR803FKU,https://www.encodeproject.org/files/ENCFF999QSA/@@download/ENCFF999QSA.bed.gz,T_helper_17_cell_1
ENCFF870WGS,3259147,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR637OPZ,https://www.encodeproject.org/files/ENCFF870WGS/@@download/ENCFF870WGS.bed.gz,stim_act_nv_CD8_pos_ab_T__4
ENCFF865ZYY,2626440,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR637OPZ,https://www.encodeproject.org/files/ENCFF865ZYY/@@download/ENCFF865ZYY.bed.gz,stim_act_nv_CD8_pos_ab_T__5
ENCFF701COP,2583748,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR637OPZ,https://www.encodeproject.org/files/ENCFF701COP/@@download/ENCFF701COP.bed.gz,stim_act_nv_CD8_pos_ab_T__6
ENCFF273AWR,2160613,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR614JAG,https://www.encodeproject.org/files/ENCFF273AWR/@@download/ENCFF273AWR.bed.gz,nv_thy_CD8_pos_ab_T__1
ENCFF038ONH,1872245,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR614JAG,https://www.encodeproject.org/files/ENCFF038ONH/@@download/ENCFF038ONH.bed.gz,nv_thy_CD8_pos_ab_T__2
ENCFF246EKP,1740384,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR614JAG,https://www.encodeproject.org/files/ENCFF246EKP/@@download/ENCFF246EKP.bed.gz,nv_thy_CD8_pos_ab_T__3
ENCFF514SFC,2238784,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR611BQR,https://www.encodeproject.org/files/ENCFF514SFC/@@download/ENCFF514SFC.bed.gz,T_helper_17_cell_2
ENCFF679WGA,1892366,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR611BQR,https://www.encodeproject.org/files/ENCFF679WGA/@@download/ENCFF679WGA.bed.gz,T_helper_17_cell_3
ENCFF980FDH,1768654,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR611BQR,https://www.encodeproject.org/files/ENCFF980FDH/@@download/ENCFF980FDH.bed.gz,T_helper_17_cell_4
ENCFF242BPO,1009173,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR513EVP,https://www.encodeproject.org/files/ENCFF242BPO/@@download/ENCFF242BPO.bed.gz,nv_thy_CD8_pos_ab_T__4
ENCFF284FAV,1244971,GRCh38,"CD4-positive, CD25-positive, alpha-beta regulatory T cell",primary cell,"Michael Snyder, Stanford",ENCSR504OUW,https://www.encodeproject.org/files/ENCFF284FAV/@@download/ENCFF284FAV.bed.gz,CD4_pos_CD25_pos_ab_regul_1
ENCFF852LGZ,1987941,GRCh38,"activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR477YSU,https://www.encodeproject.org/files/ENCFF852LGZ/@@download/ENCFF852LGZ.bed.gz,activated_nv_CD8_pos_ab_T_1
ENCFF788CFY,2259172,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR452COS,https://www.encodeproject.org/files/ENCFF788CFY/@@download/ENCFF788CFY.bed.gz,nv_thy_CD4_pos_ab_T__1
ENCFF400WXD,1711125,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR452COS,https://www.encodeproject.org/files/ENCFF400WXD/@@download/ENCFF400WXD.bed.gz,nv_thy_CD4_pos_ab_T__2
ENCFF759ZEZ,1749927,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR452COS,https://www.encodeproject.org/files/ENCFF759ZEZ/@@download/ENCFF759ZEZ.bed.gz,nv_thy_CD4_pos_ab_T__3
ENCFF717CDB,2589193,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR373NFA,https://www.encodeproject.org/files/ENCFF717CDB/@@download/ENCFF717CDB.bed.gz,activated_nv_CD4_pos_ab_T_1
ENCFF961DWF,2093428,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR373NFA,https://www.encodeproject.org/files/ENCFF961DWF/@@download/ENCFF961DWF.bed.gz,activated_nv_CD4_pos_ab_T_2
ENCFF272RDL,2148959,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR373NFA,https://www.encodeproject.org/files/ENCFF272RDL/@@download/ENCFF272RDL.bed.gz,activated_nv_CD4_pos_ab_T_3
ENCFF331XQA,2030809,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335XRZ,https://www.encodeproject.org/files/ENCFF331XQA/@@download/ENCFF331XQA.bed.gz,stim_act_effector_memory__1
ENCFF816USW,1512891,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335XRZ,https://www.encodeproject.org/files/ENCFF816USW/@@download/ENCFF816USW.bed.gz,stim_act_effector_memory__2
ENCFF276FVU,1326091,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335XRZ,https://www.encodeproject.org/files/ENCFF276FVU/@@download/ENCFF276FVU.bed.gz,stim_act_effector_memory__3
ENCFF081RHG,1854895,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR326ESM,https://www.encodeproject.org/files/ENCFF081RHG/@@download/ENCFF081RHG.bed.gz,stim_act_nv_CD8_pos_ab_T__7
ENCFF939PQC,1818460,GRCh38,"activated CD4-positive, CD25-positive, alpha-beta regulatory T cell",primary cell,"Michael Snyder, Stanford",ENCSR261PWP,https://www.encodeproject.org/files/ENCFF939PQC/@@download/ENCFF939PQC.bed.gz,activated_CD4_pos_CD25_po_1
ENCFF518VXK,955681,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR248ZAC,https://www.encodeproject.org/files/ENCFF518VXK/@@download/ENCFF518VXK.bed.gz,T_helper_17_cell_5
ENCFF907HEI,3223732,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR174SUM,https://www.encodeproject.org/files/ENCFF907HEI/@@download/ENCFF907HEI.bed.gz,activated_nv_CD4_pos_ab_T_4
ENCFF338BCO,2502315,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR174SUM,https://www.encodeproject.org/files/ENCFF338BCO/@@download/ENCFF338BCO.bed.gz,activated_nv_CD4_pos_ab_T_5
ENCFF029EIQ,2636290,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR174SUM,https://www.encodeproject.org/files/ENCFF029EIQ/@@download/ENCFF029EIQ.bed.gz,activated_nv_CD4_pos_ab_T_6
ENCFF270DPY,1472645,GRCh38,"central memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR165FYY,https://www.encodeproject.org/files/ENCFF270DPY/@@download/ENCFF270DPY.bed.gz,central_memory_CD8_pos_ab_1
ENCFF016CIJ,1320140,GRCh38,"central memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR165FYY,https://www.encodeproject.org/files/ENCFF016CIJ/@@download/ENCFF016CIJ.bed.gz,central_memory_CD8_pos_ab_2
ENCFF441BUL,920355,GRCh38,"central memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR165FYY,https://www.encodeproject.org/files/ENCFF441BUL/@@download/ENCFF441BUL.bed.gz,central_memory_CD8_pos_ab_3
ENCFF519QMA,1368815,GRCh38,"CD4-positive, CD25-positive, alpha-beta regulatory T cell",primary cell,"Michael Snyder, Stanford",ENCSR159GFS,https://www.encodeproject.org/files/ENCFF519QMA/@@download/ENCFF519QMA.bed.gz,CD4_pos_CD25_pos_ab_regul_2
ENCFF846LCD,3184036,GRCh38,activated T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR115OIV,https://www.encodeproject.org/files/ENCFF846LCD/@@download/ENCFF846LCD.bed.gz,activated_T_helper_17__1
ENCFF168ZRB,2645151,GRCh38,activated T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR115OIV,https://www.encodeproject.org/files/ENCFF168ZRB/@@download/ENCFF168ZRB.bed.gz,activated_T_helper_17__2
ENCFF217QDP,2560568,GRCh38,activated T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR115OIV,https://www.encodeproject.org/files/ENCFF217QDP/@@download/ENCFF217QDP.bed.gz,activated_T_helper_17__3
ENCFF603GBI,1304821,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR019XCN,https://www.encodeproject.org/files/ENCFF603GBI/@@download/ENCFF603GBI.bed.gz,effector_memory_CD8_pos_a_1
ENCFF205RXD,1104818,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR019XCN,https://www.encodeproject.org/files/ENCFF205RXD/@@download/ENCFF205RXD.bed.gz,effector_memory_CD8_pos_a_2
ENCFF589DMP,912846,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR019XCN,https://www.encodeproject.org/files/ENCFF589DMP/@@download/ENCFF589DMP.bed.gz,effector_memory_CD8_pos_a_3
ENCFF928YBF,3375513,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR998BBI,https://www.encodeproject.org/files/ENCFF928YBF/@@download/ENCFF928YBF.bed.gz,HCT116_1
ENCFF109DYC,2777006,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR998BBI,https://www.encodeproject.org/files/ENCFF109DYC/@@download/ENCFF109DYC.bed.gz,HCT116_2
ENCFF246CQU,2621314,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR998BBI,https://www.encodeproject.org/files/ENCFF246CQU/@@download/ENCFF246CQU.bed.gz,HCT116_3
ENCFF153VAJ,2836442,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR988ITF,https://www.encodeproject.org/files/ENCFF153VAJ/@@download/ENCFF153VAJ.bed.gz,HCT116_4
ENCFF647VWG,2311490,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR988ITF,https://www.encodeproject.org/files/ENCFF647VWG/@@download/ENCFF647VWG.bed.gz,HCT116_5
ENCFF574SCZ,2294378,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR988ITF,https://www.encodeproject.org/files/ENCFF574SCZ/@@download/ENCFF574SCZ.bed.gz,HCT116_6
ENCFF867UIK,3881914,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR874GXS,https://www.encodeproject.org/files/ENCFF867UIK/@@download/ENCFF867UIK.bed.gz,HCT116_7
ENCFF191ZBE,3199623,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR874GXS,https://www.encodeproject.org/files/ENCFF191ZBE/@@download/ENCFF191ZBE.bed.gz,HCT116_8
ENCFF606QPO,3056539,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR874GXS,https://www.encodeproject.org/files/ENCFF606QPO/@@download/ENCFF606QPO.bed.gz,HCT116_9
ENCFF311OOH,3552026,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR816PPJ,https://www.encodeproject.org/files/ENCFF311OOH/@@download/ENCFF311OOH.bed.gz,HCT116_10
ENCFF492FZV,2661963,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR816PPJ,https://www.encodeproject.org/files/ENCFF492FZV/@@download/ENCFF492FZV.bed.gz,HCT116_11
ENCFF073MUV,3017645,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR816PPJ,https://www.encodeproject.org/files/ENCFF073MUV/@@download/ENCFF073MUV.bed.gz,HCT116_12
ENCFF370EDY,3434194,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR780PLN,https://www.encodeproject.org/files/ENCFF370EDY/@@download/ENCFF370EDY.bed.gz,HCT116_13
ENCFF389NUT,2706964,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR780PLN,https://www.encodeproject.org/files/ENCFF389NUT/@@download/ENCFF389NUT.bed.gz,HCT116_14
ENCFF958VRD,2726030,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR780PLN,https://www.encodeproject.org/files/ENCFF958VRD/@@download/ENCFF958VRD.bed.gz,HCT116_15
ENCFF679NSE,3650159,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR477YZA,https://www.encodeproject.org/files/ENCFF679NSE/@@download/ENCFF679NSE.bed.gz,HCT116_16
ENCFF257DMN,2904622,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR477YZA,https://www.encodeproject.org/files/ENCFF257DMN/@@download/ENCFF257DMN.bed.gz,HCT116_17
ENCFF209UDP,2927296,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR477YZA,https://www.encodeproject.org/files/ENCFF209UDP/@@download/ENCFF209UDP.bed.gz,HCT116_18
ENCFF692CRN,3544986,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR445VYQ,https://www.encodeproject.org/files/ENCFF692CRN/@@download/ENCFF692CRN.bed.gz,HCT116_19
ENCFF408VJA,2869916,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR445VYQ,https://www.encodeproject.org/files/ENCFF408VJA/@@download/ENCFF408VJA.bed.gz,HCT116_20
ENCFF902OZN,2862039,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR445VYQ,https://www.encodeproject.org/files/ENCFF902OZN/@@download/ENCFF902OZN.bed.gz,HCT116_21
ENCFF660PIO,3611400,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR441BDP,https://www.encodeproject.org/files/ENCFF660PIO/@@download/ENCFF660PIO.bed.gz,HCT116_22
ENCFF959FQW,2845393,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR441BDP,https://www.encodeproject.org/files/ENCFF959FQW/@@download/ENCFF959FQW.bed.gz,HCT116_23
ENCFF409NYB,2903475,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR441BDP,https://www.encodeproject.org/files/ENCFF409NYB/@@download/ENCFF409NYB.bed.gz,HCT116_24
ENCFF628DWR,3989767,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR389WJO,https://www.encodeproject.org/files/ENCFF628DWR/@@download/ENCFF628DWR.bed.gz,HCT116_25
ENCFF338EFN,3288933,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR389WJO,https://www.encodeproject.org/files/ENCFF338EFN/@@download/ENCFF338EFN.bed.gz,HCT116_26
ENCFF500OLT,3315186,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR389WJO,https://www.encodeproject.org/files/ENCFF500OLT/@@download/ENCFF500OLT.bed.gz,HCT116_27
ENCFF155NTI,3258243,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR345XUN,https://www.encodeproject.org/files/ENCFF155NTI/@@download/ENCFF155NTI.bed.gz,HCT116_28
ENCFF035BSF,2658082,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR345XUN,https://www.encodeproject.org/files/ENCFF035BSF/@@download/ENCFF035BSF.bed.gz,HCT116_29
ENCFF628WZB,2540283,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR345XUN,https://www.encodeproject.org/files/ENCFF628WZB/@@download/ENCFF628WZB.bed.gz,HCT116_30
ENCFF321FBR,3087705,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR328JGW,https://www.encodeproject.org/files/ENCFF321FBR/@@download/ENCFF321FBR.bed.gz,HCT116_31
ENCFF933TYP,2339733,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR328JGW,https://www.encodeproject.org/files/ENCFF933TYP/@@download/ENCFF933TYP.bed.gz,HCT116_32
ENCFF640GNQ,2465878,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR328JGW,https://www.encodeproject.org/files/ENCFF640GNQ/@@download/ENCFF640GNQ.bed.gz,HCT116_33
ENCFF847COV,2836413,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR302NVX,https://www.encodeproject.org/files/ENCFF847COV/@@download/ENCFF847COV.bed.gz,HCT116_34
ENCFF505PFO,1901755,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR302NVX,https://www.encodeproject.org/files/ENCFF505PFO/@@download/ENCFF505PFO.bed.gz,HCT116_35
ENCFF379NOI,2854565,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR302NVX,https://www.encodeproject.org/files/ENCFF379NOI/@@download/ENCFF379NOI.bed.gz,HCT116_36
ENCFF473QDC,2569523,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR260SWI,https://www.encodeproject.org/files/ENCFF473QDC/@@download/ENCFF473QDC.bed.gz,HCT116_37
ENCFF197IGM,2127948,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR260SWI,https://www.encodeproject.org/files/ENCFF197IGM/@@download/ENCFF197IGM.bed.gz,HCT116_38
ENCFF423FZD,2022538,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR260SWI,https://www.encodeproject.org/files/ENCFF423FZD/@@download/ENCFF423FZD.bed.gz,HCT116_39
ENCFF369SDE,3361247,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR182JAI,https://www.encodeproject.org/files/ENCFF369SDE/@@download/ENCFF369SDE.bed.gz,HCT116_40
ENCFF751WEU,2739889,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR182JAI,https://www.encodeproject.org/files/ENCFF751WEU/@@download/ENCFF751WEU.bed.gz,HCT116_41
ENCFF133CQF,2983877,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR182JAI,https://www.encodeproject.org/files/ENCFF133CQF/@@download/ENCFF133CQF.bed.gz,HCT116_42
ENCFF420AQB,3981535,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR135OML,https://www.encodeproject.org/files/ENCFF420AQB/@@download/ENCFF420AQB.bed.gz,HCT116_43
ENCFF676FLS,3181364,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR135OML,https://www.encodeproject.org/files/ENCFF676FLS/@@download/ENCFF676FLS.bed.gz,HCT116_44
ENCFF593ZFH,3179032,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR135OML,https://www.encodeproject.org/files/ENCFF593ZFH/@@download/ENCFF593ZFH.bed.gz,HCT116_45
ENCFF232YAJ,1662062,GRCh38,"activated CD4-positive, CD25-positive, alpha-beta regulatory T cell",primary cell,"Michael Snyder, Stanford",ENCSR094QFZ,https://www.encodeproject.org/files/ENCFF232YAJ/@@download/ENCFF232YAJ.bed.gz,activated_CD4_pos_CD25_po_2
ENCFF809MNF,3254022,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR059BAR,https://www.encodeproject.org/files/ENCFF809MNF/@@download/ENCFF809MNF.bed.gz,HCT116_46
ENCFF706KCD,2771152,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR059BAR,https://www.encodeproject.org/files/ENCFF706KCD/@@download/ENCFF706KCD.bed.gz,HCT116_47
ENCFF469HQE,2684751,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR059BAR,https://www.encodeproject.org/files/ENCFF469HQE/@@download/ENCFF469HQE.bed.gz,HCT116_48
ENCFF375TLF,2297698,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR977LVI,https://www.encodeproject.org/files/ENCFF375TLF/@@download/ENCFF375TLF.bed.gz,T_cell_1
ENCFF072PLZ,1879490,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR977LVI,https://www.encodeproject.org/files/ENCFF072PLZ/@@download/ENCFF072PLZ.bed.gz,T_cell_2
ENCFF753XDV,1943888,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR977LVI,https://www.encodeproject.org/files/ENCFF753XDV/@@download/ENCFF753XDV.bed.gz,T_cell_3
ENCFF358TXK,2193470,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR903WVU,https://www.encodeproject.org/files/ENCFF358TXK/@@download/ENCFF358TXK.bed.gz,naive_B_cell_1
ENCFF956UKN,1773879,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR903WVU,https://www.encodeproject.org/files/ENCFF956UKN/@@download/ENCFF956UKN.bed.gz,naive_B_cell_2
ENCFF590QLY,1864123,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR903WVU,https://www.encodeproject.org/files/ENCFF590QLY/@@download/ENCFF590QLY.bed.gz,naive_B_cell_3
ENCFF646NJM,2159434,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR854TTM,https://www.encodeproject.org/files/ENCFF646NJM/@@download/ENCFF646NJM.bed.gz,natural_killer_cell_1
ENCFF859WWL,1656787,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR854TTM,https://www.encodeproject.org/files/ENCFF859WWL/@@download/ENCFF859WWL.bed.gz,natural_killer_cell_2
ENCFF286EBA,1761437,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR854TTM,https://www.encodeproject.org/files/ENCFF286EBA/@@download/ENCFF286EBA.bed.gz,natural_killer_cell_3
ENCFF947LSQ,1955031,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR808HWS,https://www.encodeproject.org/files/ENCFF947LSQ/@@download/ENCFF947LSQ.bed.gz,natural_killer_cell_4
ENCFF704BAI,1679152,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR808HWS,https://www.encodeproject.org/files/ENCFF704BAI/@@download/ENCFF704BAI.bed.gz,natural_killer_cell_5
ENCFF507PGF,1404546,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR808HWS,https://www.encodeproject.org/files/ENCFF507PGF/@@download/ENCFF507PGF.bed.gz,natural_killer_cell_6
ENCFF219TJQ,2072378,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR685OFR,https://www.encodeproject.org/files/ENCFF219TJQ/@@download/ENCFF219TJQ.bed.gz,naive_B_cell_4
ENCFF950NID,1791657,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR685OFR,https://www.encodeproject.org/files/ENCFF950NID/@@download/ENCFF950NID.bed.gz,naive_B_cell_5
ENCFF380TOL,1729148,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR685OFR,https://www.encodeproject.org/files/ENCFF380TOL/@@download/ENCFF380TOL.bed.gz,naive_B_cell_6
ENCFF045RJR,3572625,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR653VSR,https://www.encodeproject.org/files/ENCFF045RJR/@@download/ENCFF045RJR.bed.gz,stim_act_nv_B__1
ENCFF922ICC,2999607,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR653VSR,https://www.encodeproject.org/files/ENCFF922ICC/@@download/ENCFF922ICC.bed.gz,stim_act_nv_B__2
ENCFF530QPE,2654486,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR653VSR,https://www.encodeproject.org/files/ENCFF530QPE/@@download/ENCFF530QPE.bed.gz,stim_act_nv_B__3
ENCFF628FAP,2331016,GRCh38,memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR610AQP,https://www.encodeproject.org/files/ENCFF628FAP/@@download/ENCFF628FAP.bed.gz,memory_B_cell_1
ENCFF883EEJ,1931743,GRCh38,memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR610AQP,https://www.encodeproject.org/files/ENCFF883EEJ/@@download/ENCFF883EEJ.bed.gz,memory_B_cell_2
ENCFF881FMP,1890802,GRCh38,memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR610AQP,https://www.encodeproject.org/files/ENCFF881FMP/@@download/ENCFF881FMP.bed.gz,memory_B_cell_3
ENCFF569WGY,2880863,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR558ZSN,https://www.encodeproject.org/files/ENCFF569WGY/@@download/ENCFF569WGY.bed.gz,activated_T_cell_4
ENCFF837BSF,2373465,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR558ZSN,https://www.encodeproject.org/files/ENCFF837BSF/@@download/ENCFF837BSF.bed.gz,activated_T_cell_5
ENCFF521UEK,2228711,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR558ZSN,https://www.encodeproject.org/files/ENCFF521UEK/@@download/ENCFF521UEK.bed.gz,activated_T_cell_6
ENCFF743FTR,2095512,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR392YGP,https://www.encodeproject.org/files/ENCFF743FTR/@@download/ENCFF743FTR.bed.gz,effector_memory_CD8_pos_a_4
ENCFF244HGF,1836831,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR392YGP,https://www.encodeproject.org/files/ENCFF244HGF/@@download/ENCFF244HGF.bed.gz,effector_memory_CD8_pos_a_5
ENCFF876PGP,1706942,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR392YGP,https://www.encodeproject.org/files/ENCFF876PGP/@@download/ENCFF876PGP.bed.gz,effector_memory_CD8_pos_a_6
ENCFF031HYC,3227957,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR379NMT,https://www.encodeproject.org/files/ENCFF031HYC/@@download/ENCFF031HYC.bed.gz,stim_act_nv_B__4
ENCFF721ADO,2486599,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR379NMT,https://www.encodeproject.org/files/ENCFF721ADO/@@download/ENCFF721ADO.bed.gz,stim_act_nv_B__5
ENCFF899EJR,2598525,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR379NMT,https://www.encodeproject.org/files/ENCFF899EJR/@@download/ENCFF899EJR.bed.gz,stim_act_nv_B__6
ENCFF007XEZ,3061123,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335LHS,https://www.encodeproject.org/files/ENCFF007XEZ/@@download/ENCFF007XEZ.bed.gz,stim_act_effector_memory__4
ENCFF394AHS,2583196,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335LHS,https://www.encodeproject.org/files/ENCFF394AHS/@@download/ENCFF394AHS.bed.gz,stim_act_effector_memory__5
ENCFF767JJK,2671206,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335LHS,https://www.encodeproject.org/files/ENCFF767JJK/@@download/ENCFF767JJK.bed.gz,stim_act_effector_memory__6
ENCFF024NFM,1742310,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR305QTE,https://www.encodeproject.org/files/ENCFF024NFM/@@download/ENCFF024NFM.bed.gz,natural_killer_cell_7
ENCFF367LOM,1193627,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR305QTE,https://www.encodeproject.org/files/ENCFF367LOM/@@download/ENCFF367LOM.bed.gz,natural_killer_cell_8
ENCFF961WQE,1312800,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR305QTE,https://www.encodeproject.org/files/ENCFF961WQE/@@download/ENCFF961WQE.bed.gz,natural_killer_cell_9
ENCFF141IXH,2324350,GRCh38,stimulated activated memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR302PTB,https://www.encodeproject.org/files/ENCFF141IXH/@@download/ENCFF141IXH.bed.gz,stim_act_memory_B__1
ENCFF713OLR,2000977,GRCh38,stimulated activated memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR302PTB,https://www.encodeproject.org/files/ENCFF713OLR/@@download/ENCFF713OLR.bed.gz,stim_act_memory_B__2
ENCFF369PGW,1794744,GRCh38,stimulated activated memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR302PTB,https://www.encodeproject.org/files/ENCFF369PGW/@@download/ENCFF369PGW.bed.gz,stim_act_memory_B__3
ENCFF513JVX,2211757,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR260LAN,https://www.encodeproject.org/files/ENCFF513JVX/@@download/ENCFF513JVX.bed.gz,nv_thy_CD4_pos_ab_T__4
ENCFF199DOD,1835235,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR260LAN,https://www.encodeproject.org/files/ENCFF199DOD/@@download/ENCFF199DOD.bed.gz,nv_thy_CD4_pos_ab_T__5
ENCFF400SHU,1820693,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR260LAN,https://www.encodeproject.org/files/ENCFF400SHU/@@download/ENCFF400SHU.bed.gz,nv_thy_CD4_pos_ab_T__6
ENCFF774APK,2438689,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR044ATC,https://www.encodeproject.org/files/ENCFF774APK/@@download/ENCFF774APK.bed.gz,natural_killer_cell_10
ENCFF450XPR,2057916,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR044ATC,https://www.encodeproject.org/files/ENCFF450XPR/@@download/ENCFF450XPR.bed.gz,natural_killer_cell_11
ENCFF473MMF,2000049,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR044ATC,https://www.encodeproject.org/files/ENCFF473MMF/@@download/ENCFF473MMF.bed.gz,natural_killer_cell_12
ENCFF206PEF,36851,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR032MEH,https://www.encodeproject.org/files/ENCFF206PEF/@@download/ENCFF206PEF.bed.gz,T_cell_4
ENCFF374FLX,11966,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR032MEH,https://www.encodeproject.org/files/ENCFF374FLX/@@download/ENCFF374FLX.bed.gz,T_cell_5
ENCFF195XAI,20644,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR032MEH,https://www.encodeproject.org/files/ENCFF195XAI/@@download/ENCFF195XAI.bed.gz,T_cell_6
ENCFF316PNL,2103093,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR283LPH,https://www.encodeproject.org/files/ENCFF316PNL/@@download/ENCFF316PNL.bed.gz,nv_thy_CD8_pos_ab_T__5
ENCFF960LQQ,2105737,GRCh38,"activated CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR545UJP,https://www.encodeproject.org/files/ENCFF960LQQ/@@download/ENCFF960LQQ.bed.gz,activated_CD8_pos_ab_T__1
ENCFF311WRF,2147154,GRCh38,"activated CD8-positive, alpha-beta memory T cell",primary cell,"Michael Snyder, Stanford",ENCSR915MTG,https://www.encodeproject.org/files/ENCFF311WRF/@@download/ENCFF311WRF.bed.gz,activated_CD8_pos_ab_memo_1
ENCFF595GOY,1893985,GRCh38,"activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR983HXF,https://www.encodeproject.org/files/ENCFF595GOY/@@download/ENCFF595GOY.bed.gz,activated_nv_CD8_pos_ab_T_2
ENCFF201BYH,1827771,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR092SRS,https://www.encodeproject.org/files/ENCFF201BYH/@@download/ENCFF201BYH.bed.gz,activated_T_cell_7
ENCFF170XRK,1963064,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR102RXG,https://www.encodeproject.org/files/ENCFF170XRK/@@download/ENCFF170XRK.bed.gz,T_cell_7
ENCFF100NUL,1866703,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR258RSH,https://www.encodeproject.org/files/ENCFF100NUL/@@download/ENCFF100NUL.bed.gz,T_cell_8
ENCFF904ZLF,1614068,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR299LSN,https://www.encodeproject.org/files/ENCFF904ZLF/@@download/ENCFF904ZLF.bed.gz,T_cell_9
ENCFF928JLQ,1951307,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR373GMM,https://www.encodeproject.org/files/ENCFF928JLQ/@@download/ENCFF928JLQ.bed.gz,natural_killer_cell_13
ENCFF590CQU,2206454,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR411NFB,https://www.encodeproject.org/files/ENCFF590CQU/@@download/ENCFF590CQU.bed.gz,activated_T_cell_8
ENCFF230RRP,1763252,GRCh38,"CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR476VJY,https://www.encodeproject.org/files/ENCFF230RRP/@@download/ENCFF230RRP.bed.gz,CD8_pos_ab_T_cell_1
ENCFF492GYV,3771210,GRCh38,WTC11,cell line,"Michael Snyder, Stanford",ENCSR541KFY,https://www.encodeproject.org/files/ENCFF492GYV/@@download/ENCFF492GYV.bed.gz,WTC11_1
ENCFF262EMM,3113991,GRCh38,WTC11,cell line,"Michael Snyder, Stanford",ENCSR541KFY,https://www.encodeproject.org/files/ENCFF262EMM/@@download/ENCFF262EMM.bed.gz,WTC11_2
ENCFF770QET,3083237,GRCh38,WTC11,cell line,"Michael Snyder, Stanford",ENCSR541KFY,https://www.encodeproject.org/files/ENCFF770QET/@@download/ENCFF770QET.bed.gz,WTC11_3
ENCFF390ASB,1616466,GRCh38,B cell,primary cell,"Michael Snyder, Stanford",ENCSR603LVR,https://www.encodeproject.org/files/ENCFF390ASB/@@download/ENCFF390ASB.bed.gz,B_cell_1
ENCFF490FII,1910266,GRCh38,activated B cell,primary cell,"Michael Snyder, Stanford",ENCSR659SFK,https://www.encodeproject.org/files/ENCFF490FII/@@download/ENCFF490FII.bed.gz,activated_B_cell_1
ENCFF330FRH,2544902,GRCh38,"activated CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR718HWW,https://www.encodeproject.org/files/ENCFF330FRH/@@download/ENCFF330FRH.bed.gz,activated_CD8_pos_ab_T__2
ENCFF760LQQ,1459975,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR775KMA,https://www.encodeproject.org/files/ENCFF760LQQ/@@download/ENCFF760LQQ.bed.gz,activated_T_cell_9
ENCFF944LFH,1835417,GRCh38,"CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR841LHT,https://www.encodeproject.org/files/ENCFF944LFH/@@download/ENCFF944LFH.bed.gz,CD4_pos_ab_T_cell_1
ENCFF873YXD,1573222,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR934AWQ,https://www.encodeproject.org/files/ENCFF873YXD/@@download/ENCFF873YXD.bed.gz,T_cell_10
ENCFF464QZE,2690212,GRCh38,"activated CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR991PBP,https://www.encodeproject.org/files/ENCFF464QZE/@@download/ENCFF464QZE.bed.gz,activated_CD4_pos_ab_T__1
ENCFF523YFW,2086255,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR993XED,https://www.encodeproject.org/files/ENCFF523YFW/@@download/ENCFF523YFW.bed.gz,activated_T_cell_10
ENCFF221FSW,224631,GRCh38,posterior cingulate cortex,tissue,"Barbara Wold, Caltech",ENCSR729FNL,https://www.encodeproject.org/files/ENCFF221FSW/@@download/ENCFF221FSW.bed.gz,posterior_cingulate_corte_1
ENCFF858CCP,513872,GRCh38,cerebellum,tissue,"Barbara Wold, Caltech",ENCSR802GEV,https://www.encodeproject.org/files/ENCFF858CCP/@@download/ENCFF858CCP.bed.gz,cerebellum_1
ENCFF559CCG,2328019,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR059IHA,https://www.encodeproject.org/files/ENCFF559CCG/@@download/ENCFF559CCG.bed.gz,dendritic_cell_1
ENCFF500UNC,1806551,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR131CHJ,https://www.encodeproject.org/files/ENCFF500UNC/@@download/ENCFF500UNC.bed.gz,dendritic_cell_2
ENCFF145UGV,1208229,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR156FDX,https://www.encodeproject.org/files/ENCFF145UGV/@@download/ENCFF145UGV.bed.gz,dendritic_cell_3
ENCFF406CMK,1906186,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR204FCV,https://www.encodeproject.org/files/ENCFF406CMK/@@download/ENCFF406CMK.bed.gz,dendritic_cell_4
ENCFF591TRF,2698876,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR208NBF,https://www.encodeproject.org/files/ENCFF591TRF/@@download/ENCFF591TRF.bed.gz,dendritic_cell_5
ENCFF195JHT,1354869,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR222SCA,https://www.encodeproject.org/files/ENCFF195JHT/@@download/ENCFF195JHT.bed.gz,dendritic_cell_6
ENCFF205VUK,1706126,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR237VSF,https://www.encodeproject.org/files/ENCFF205VUK/@@download/ENCFF205VUK.bed.gz,dendritic_cell_7
ENCFF223CMS,1087848,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR290AUK,https://www.encodeproject.org/files/ENCFF223CMS/@@download/ENCFF223CMS.bed.gz,dendritic_cell_8
ENCFF264XMH,1777603,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR329XFZ,https://www.encodeproject.org/files/ENCFF264XMH/@@download/ENCFF264XMH.bed.gz,dendritic_cell_9
ENCFF108OXE,1680542,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR370QWN,https://www.encodeproject.org/files/ENCFF108OXE/@@download/ENCFF108OXE.bed.gz,dendritic_cell_10
ENCFF382JJS,3025875,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR459AYS,https://www.encodeproject.org/files/ENCFF382JJS/@@download/ENCFF382JJS.bed.gz,dendritic_cell_11
ENCFF086PSD,1899484,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR535NOD,https://www.encodeproject.org/files/ENCFF086PSD/@@download/ENCFF086PSD.bed.gz,dendritic_cell_12
ENCFF371BNX,1892666,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR544YXV,https://www.encodeproject.org/files/ENCFF371BNX/@@download/ENCFF371BNX.bed.gz,dendritic_cell_13
ENCFF128JGS,1131583,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR566LZT,https://www.encodeproject.org/files/ENCFF128JGS/@@download/ENCFF128JGS.bed.gz,dendritic_cell_14
ENCFF793JDF,3086767,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR589ROM,https://www.encodeproject.org/files/ENCFF793JDF/@@download/ENCFF793JDF.bed.gz,dendritic_cell_15
ENCFF196XLA,2791350,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR605EGR,https://www.encodeproject.org/files/ENCFF196XLA/@@download/ENCFF196XLA.bed.gz,dendritic_cell_16
ENCFF796SPZ,1452360,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR628SXX,https://www.encodeproject.org/files/ENCFF796SPZ/@@download/ENCFF796SPZ.bed.gz,dendritic_cell_17
ENCFF649JWH,3258393,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR648JJT,https://www.encodeproject.org/files/ENCFF649JWH/@@download/ENCFF649JWH.bed.gz,dendritic_cell_18
ENCFF295ISW,1250828,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR679NRJ,https://www.encodeproject.org/files/ENCFF295ISW/@@download/ENCFF295ISW.bed.gz,dendritic_cell_19
ENCFF497YGE,1372086,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR901IEQ,https://www.encodeproject.org/files/ENCFF497YGE/@@download/ENCFF497YGE.bed.gz,dendritic_cell_20
ENCFF430TYY,2041856,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR904UKR,https://www.encodeproject.org/files/ENCFF430TYY/@@download/ENCFF430TYY.bed.gz,dendritic_cell_21
ENCFF831PZH,2381749,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR926GDT,https://www.encodeproject.org/files/ENCFF831PZH/@@download/ENCFF831PZH.bed.gz,dendritic_cell_22
ENCFF115TFH,1997819,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR926TFY,https://www.encodeproject.org/files/ENCFF115TFH/@@download/ENCFF115TFH.bed.gz,dendritic_cell_23
ENCFF555CWF,800434,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR940QJU,https://www.encodeproject.org/files/ENCFF555CWF/@@download/ENCFF555CWF.bed.gz,dendritic_cell_24
ENCFF114ZSN,3973491,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR158XTU,https://www.encodeproject.org/files/ENCFF114ZSN/@@download/ENCFF114ZSN.bed.gz,foreskin_keratinocyte_1
ENCFF720EEO,3695043,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR158XTU,https://www.encodeproject.org/files/ENCFF720EEO/@@download/ENCFF720EEO.bed.gz,foreskin_keratinocyte_2
ENCFF971JNS,2898465,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR158XTU,https://www.encodeproject.org/files/ENCFF971JNS/@@download/ENCFF971JNS.bed.gz,foreskin_keratinocyte_3
ENCFF023XMG,3885919,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR245LNF,https://www.encodeproject.org/files/ENCFF023XMG/@@download/ENCFF023XMG.bed.gz,foreskin_keratinocyte_4
ENCFF029PPA,3483218,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR245LNF,https://www.encodeproject.org/files/ENCFF029PPA/@@download/ENCFF029PPA.bed.gz,foreskin_keratinocyte_5
ENCFF142VUU,3002383,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR245LNF,https://www.encodeproject.org/files/ENCFF142VUU/@@download/ENCFF142VUU.bed.gz,foreskin_keratinocyte_6
ENCFF184TLY,4126880,GRCh38,pancreas,tissue,"Michael Snyder, Stanford",ENCSR251POP,https://www.encodeproject.org/files/ENCFF184TLY/@@download/ENCFF184TLY.bed.gz,pancreas_1
ENCFF531PFR,591587,GRCh38,thoracic aorta,tissue,"Michael Snyder, Stanford",ENCSR344ZTM,https://www.encodeproject.org/files/ENCFF531PFR/@@download/ENCFF531PFR.bed.gz,thoracic_aorta_1
ENCFF746BDQ,3299400,GRCh38,NCI-H929,cell line,"Michael Snyder, Stanford",ENCSR382LBS,https://www.encodeproject.org/files/ENCFF746BDQ/@@download/ENCFF746BDQ.bed.gz,NCI_H929_1
ENCFF045KOG,3105837,GRCh38,NCI-H929,cell line,"Michael Snyder, Stanford",ENCSR382LBS,https://www.encodeproject.org/files/ENCFF045KOG/@@download/ENCFF045KOG.bed.gz,NCI_H929_2
ENCFF369SMB,3908520,GRCh38,NCI-H929,cell line,"Michael Snyder, Stanford",ENCSR382LBS,https://www.encodeproject.org/files/ENCFF369SMB/@@download/ENCFF369SMB.bed.gz,NCI_H929_3
ENCFF386YHU,2444412,GRCh38,NCI-H929,cell line,"Michael Snyder, Stanford",ENCSR382LBS,https://www.encodeproject.org/files/ENCFF386YHU/@@download/ENCFF386YHU.bed.gz,NCI_H929_4
ENCFF089MWN,1887456,GRCh38,NCI-H929,cell line,"Michael Snyder, Stanford",ENCSR382LBS,https://www.encodeproject.org/files/ENCFF089MWN/@@download/ENCFF089MWN.bed.gz,NCI_H929_5
ENCFF786KPZ,3304252,GRCh38,NCI-H929,cell line,"Michael Snyder, Stanford",ENCSR382LBS,https://www.encodeproject.org/files/ENCFF786KPZ/@@download/ENCFF786KPZ.bed.gz,NCI_H929_6
ENCFF713XSO,3897746,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR460DKJ,https://www.encodeproject.org/files/ENCFF713XSO/@@download/ENCFF713XSO.bed.gz,foreskin_keratinocyte_7
ENCFF391NCW,3584052,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR460DKJ,https://www.encodeproject.org/files/ENCFF391NCW/@@download/ENCFF391NCW.bed.gz,foreskin_keratinocyte_8
ENCFF945HQU,2681416,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR460DKJ,https://www.encodeproject.org/files/ENCFF945HQU/@@download/ENCFF945HQU.bed.gz,foreskin_keratinocyte_9
ENCFF339ONF,3629141,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR521RAL,https://www.encodeproject.org/files/ENCFF339ONF/@@download/ENCFF339ONF.bed.gz,foreskin_keratinocyte_10
ENCFF690BVH,3036427,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR521RAL,https://www.encodeproject.org/files/ENCFF690BVH/@@download/ENCFF690BVH.bed.gz,foreskin_keratinocyte_11
ENCFF539EVT,2815521,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR521RAL,https://www.encodeproject.org/files/ENCFF539EVT/@@download/ENCFF539EVT.bed.gz,foreskin_keratinocyte_12
ENCFF809FCE,4143434,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR572UDP,https://www.encodeproject.org/files/ENCFF809FCE/@@download/ENCFF809FCE.bed.gz,foreskin_keratinocyte_13
ENCFF379ZTQ,3671034,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR572UDP,https://www.encodeproject.org/files/ENCFF379ZTQ/@@download/ENCFF379ZTQ.bed.gz,foreskin_keratinocyte_14
ENCFF146CEC,3579401,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR572UDP,https://www.encodeproject.org/files/ENCFF146CEC/@@download/ENCFF146CEC.bed.gz,foreskin_keratinocyte_15
ENCFF564GDB,3766550,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR649RCV,https://www.encodeproject.org/files/ENCFF564GDB/@@download/ENCFF564GDB.bed.gz,foreskin_keratinocyte_16
ENCFF856OEE,2475909,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR649RCV,https://www.encodeproject.org/files/ENCFF856OEE/@@download/ENCFF856OEE.bed.gz,foreskin_keratinocyte_17
ENCFF761JJD,3398871,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR649RCV,https://www.encodeproject.org/files/ENCFF761JJD/@@download/ENCFF761JJD.bed.gz,foreskin_keratinocyte_18
ENCFF731JBR,2054173,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR769DGC,https://www.encodeproject.org/files/ENCFF731JBR/@@download/ENCFF731JBR.bed.gz,heart_left_ventricle_1
ENCFF472QTQ,3611967,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR798IJQ,https://www.encodeproject.org/files/ENCFF472QTQ/@@download/ENCFF472QTQ.bed.gz,foreskin_keratinocyte_19
ENCFF190THW,3474894,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR798IJQ,https://www.encodeproject.org/files/ENCFF190THW/@@download/ENCFF190THW.bed.gz,foreskin_keratinocyte_20
ENCFF777VYZ,1532556,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR798IJQ,https://www.encodeproject.org/files/ENCFF777VYZ/@@download/ENCFF777VYZ.bed.gz,foreskin_keratinocyte_21
ENCFF157EMV,3887174,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR923WGJ,https://www.encodeproject.org/files/ENCFF157EMV/@@download/ENCFF157EMV.bed.gz,foreskin_keratinocyte_22
ENCFF176IYE,3279719,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR923WGJ,https://www.encodeproject.org/files/ENCFF176IYE/@@download/ENCFF176IYE.bed.gz,foreskin_keratinocyte_23
ENCFF245BXW,3126783,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR923WGJ,https://www.encodeproject.org/files/ENCFF245BXW/@@download/ENCFF245BXW.bed.gz,foreskin_keratinocyte_24
ENCFF518MUM,3968354,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR939WVU,https://www.encodeproject.org/files/ENCFF518MUM/@@download/ENCFF518MUM.bed.gz,foreskin_keratinocyte_25
ENCFF015BAL,3555201,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR939WVU,https://www.encodeproject.org/files/ENCFF015BAL/@@download/ENCFF015BAL.bed.gz,foreskin_keratinocyte_26
ENCFF572PSC,2929928,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR939WVU,https://www.encodeproject.org/files/ENCFF572PSC/@@download/ENCFF572PSC.bed.gz,foreskin_keratinocyte_27
ENCFF968IED,3999951,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR247PVM,https://www.encodeproject.org/files/ENCFF968IED/@@download/ENCFF968IED.bed.gz,foreskin_keratinocyte_28
ENCFF037CYS,3742698,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR247PVM,https://www.encodeproject.org/files/ENCFF037CYS/@@download/ENCFF037CYS.bed.gz,foreskin_keratinocyte_29
ENCFF493WYQ,3019440,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR247PVM,https://www.encodeproject.org/files/ENCFF493WYQ/@@download/ENCFF493WYQ.bed.gz,foreskin_keratinocyte_30
ENCFF363QXD,1301618,GRCh38,thyroid gland,tissue,"Michael Snyder, Stanford",ENCSR914DTI,https://www.encodeproject.org/files/ENCFF363QXD/@@download/ENCFF363QXD.bed.gz,thyroid_gland_1
ENCFF914OPC,3784072,GRCh38,uterus,tissue,"Michael Snyder, Stanford",ENCSR775SYU,https://www.encodeproject.org/files/ENCFF914OPC/@@download/ENCFF914OPC.bed.gz,uterus_1
ENCFF237JBZ,3588643,GRCh38,right cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR177JWR,https://www.encodeproject.org/files/ENCFF237JBZ/@@download/ENCFF237JBZ.bed.gz,right_cardiac_atrium_1
ENCFF023XLF,4100156,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR290YMN,https://www.encodeproject.org/files/ENCFF023XLF/@@download/ENCFF023XLF.bed.gz,foreskin_keratinocyte_31
ENCFF317SPN,3710590,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR290YMN,https://www.encodeproject.org/files/ENCFF317SPN/@@download/ENCFF317SPN.bed.gz,foreskin_keratinocyte_32
ENCFF441ISH,3406392,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR290YMN,https://www.encodeproject.org/files/ENCFF441ISH/@@download/ENCFF441ISH.bed.gz,foreskin_keratinocyte_33
ENCFF792PNO,4202332,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR356KRQ,https://www.encodeproject.org/files/ENCFF792PNO/@@download/ENCFF792PNO.bed.gz,foreskin_keratinocyte_34
ENCFF317AVG,3658977,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR356KRQ,https://www.encodeproject.org/files/ENCFF317AVG/@@download/ENCFF317AVG.bed.gz,foreskin_keratinocyte_35
ENCFF740VKH,3604807,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR356KRQ,https://www.encodeproject.org/files/ENCFF740VKH/@@download/ENCFF740VKH.bed.gz,foreskin_keratinocyte_36
ENCFF367TUM,4095443,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR677MJF,https://www.encodeproject.org/files/ENCFF367TUM/@@download/ENCFF367TUM.bed.gz,foreskin_keratinocyte_37
ENCFF538UHW,3639940,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR677MJF,https://www.encodeproject.org/files/ENCFF538UHW/@@download/ENCFF538UHW.bed.gz,foreskin_keratinocyte_38
ENCFF927ULP,3334558,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR677MJF,https://www.encodeproject.org/files/ENCFF927ULP/@@download/ENCFF927ULP.bed.gz,foreskin_keratinocyte_39
ENCFF459XSO,4398805,GRCh38,left ventricle myocardium superior,tissue,"Michael Snyder, Stanford",ENCSR133WJY,https://www.encodeproject.org/files/ENCFF459XSO/@@download/ENCFF459XSO.bed.gz,left_ventricle_myocardium_1
ENCFF886FAD,3531058,GRCh38,ovary,tissue,"Michael Snyder, Stanford",ENCSR392UJM,https://www.encodeproject.org/files/ENCFF886FAD/@@download/ENCFF886FAD.bed.gz,ovary_1
ENCFF381WAA,3058512,GRCh38,ovary,tissue,"Michael Snyder, Stanford",ENCSR476WNA,https://www.encodeproject.org/files/ENCFF381WAA/@@download/ENCFF381WAA.bed.gz,ovary_2
ENCFF517WLY,2051155,GRCh38,spleen,tissue,"Michael Snyder, Stanford",ENCSR182VGJ,https://www.encodeproject.org/files/ENCFF517WLY/@@download/ENCFF517WLY.bed.gz,spleen_1
ENCFF529QEX,4073162,GRCh38,left cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR310UDW,https://www.encodeproject.org/files/ENCFF529QEX/@@download/ENCFF529QEX.bed.gz,left_cardiac_atrium_1
ENCFF822PFF,3118388,GRCh38,bile duct,tissue,"Michael Snyder, Stanford",ENCSR325NFE,https://www.encodeproject.org/files/ENCFF822PFF/@@download/ENCFF822PFF.bed.gz,bile_duct_1
ENCFF124JSN,1854091,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR403GBR,https://www.encodeproject.org/files/ENCFF124JSN/@@download/ENCFF124JSN.bed.gz,heart_right_ventricle_1
ENCFF953FJE,2980435,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR491VKV,https://www.encodeproject.org/files/ENCFF953FJE/@@download/ENCFF953FJE.bed.gz,heart_right_ventricle_2
ENCFF916IVU,4296014,GRCh38,mucosa of descending colon,tissue,"Michael Snyder, Stanford",ENCSR496PPU,https://www.encodeproject.org/files/ENCFF916IVU/@@download/ENCFF916IVU.bed.gz,mucosa_of_descending_colo_1
ENCFF085LPT,3552132,GRCh38,posterior vena cava,tissue,"Michael Snyder, Stanford",ENCSR552QUA,https://www.encodeproject.org/files/ENCFF085LPT/@@download/ENCFF085LPT.bed.gz,posterior_vena_cava_1
ENCFF748ORI,4459496,GRCh38,lower lobe of left lung,tissue,"Michael Snyder, Stanford",ENCSR555ZDH,https://www.encodeproject.org/files/ENCFF748ORI/@@download/ENCFF748ORI.bed.gz,lower_lobe_of_left_lung_1
ENCFF012SCX,4745994,GRCh38,left lobe of liver,tissue,"Michael Snyder, Stanford",ENCSR624ODL,https://www.encodeproject.org/files/ENCFF012SCX/@@download/ENCFF012SCX.bed.gz,left_lobe_of_liver_1
ENCFF658LHI,4053381,GRCh38,right lobe of liver,tissue,"Michael Snyder, Stanford",ENCSR836FIL,https://www.encodeproject.org/files/ENCFF658LHI/@@download/ENCFF658LHI.bed.gz,right_lobe_of_liver_1
ENCFF656MIU,3133485,GRCh38,upper lobe of left lung,tissue,"Michael Snyder, Stanford",ENCSR895SEG,https://www.encodeproject.org/files/ENCFF656MIU/@@download/ENCFF656MIU.bed.gz,upper_lobe_of_left_lung_1
ENCFF958VMM,4657990,GRCh38,right lobe of liver,tissue,"Michael Snyder, Stanford",ENCSR952SPO,https://www.encodeproject.org/files/ENCFF958VMM/@@download/ENCFF958VMM.bed.gz,right_lobe_of_liver_2
ENCFF403JGN,3383377,GRCh38,upper lobe of right lung,tissue,"Michael Snyder, Stanford",ENCSR990NNX,https://www.encodeproject.org/files/ENCFF403JGN/@@download/ENCFF403JGN.bed.gz,upper_lobe_of_right_lung_1
ENCFF201JGM,4465471,GRCh38,adrenal gland,tissue,"Michael Snyder, Stanford",ENCSR553LAZ,https://www.encodeproject.org/files/ENCFF201JGM/@@download/ENCFF201JGM.bed.gz,adrenal_gland_1
ENCFF425EJD,4673436,GRCh38,right lobe of liver,tissue,"Michael Snyder, Stanford",ENCSR685ZMP,https://www.encodeproject.org/files/ENCFF425EJD/@@download/ENCFF425EJD.bed.gz,right_lobe_of_liver_3
ENCFF047NIZ,2878957,GRCh38,lower lobe of right lung,tissue,"Michael Snyder, Stanford",ENCSR923VTG,https://www.encodeproject.org/files/ENCFF047NIZ/@@download/ENCFF047NIZ.bed.gz,lower_lobe_of_right_lung_1
ENCFF609BSU,4632083,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR291GJU,https://www.encodeproject.org/files/ENCFF609BSU/@@download/ENCFF609BSU.bed.gz,HepG2_1
ENCFF915FZC,4528066,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR291GJU,https://www.encodeproject.org/files/ENCFF915FZC/@@download/ENCFF915FZC.bed.gz,HepG2_2
ENCFF935GLR,4821361,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR291GJU,https://www.encodeproject.org/files/ENCFF935GLR/@@download/ENCFF935GLR.bed.gz,HepG2_3
ENCFF161ZZX,4208414,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR291GJU,https://www.encodeproject.org/files/ENCFF161ZZX/@@download/ENCFF161ZZX.bed.gz,HepG2_4
ENCFF576UEM,3826502,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR291GJU,https://www.encodeproject.org/files/ENCFF576UEM/@@download/ENCFF576UEM.bed.gz,HepG2_5
ENCFF919RKQ,4168105,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR291GJU,https://www.encodeproject.org/files/ENCFF919RKQ/@@download/ENCFF919RKQ.bed.gz,HepG2_6
ENCFF942QQL,4645660,GRCh38,GM23338,cell line,"Michael Snyder, Stanford",ENCSR485TLP,https://www.encodeproject.org/files/ENCFF942QQL/@@download/ENCFF942QQL.bed.gz,GM23338_1
ENCFF426DRI,4603593,GRCh38,GM23338,cell line,"Michael Snyder, Stanford",ENCSR485TLP,https://www.encodeproject.org/files/ENCFF426DRI/@@download/ENCFF426DRI.bed.gz,GM23338_2
ENCFF738KYF,4841520,GRCh38,GM23338,cell line,"Michael Snyder, Stanford",ENCSR485TLP,https://www.encodeproject.org/files/ENCFF738KYF/@@download/ENCFF738KYF.bed.gz,GM23338_3
ENCFF239SMJ,4110523,GRCh38,GM23338,cell line,"Michael Snyder, Stanford",ENCSR485TLP,https://www.encodeproject.org/files/ENCFF239SMJ/@@download/ENCFF239SMJ.bed.gz,GM23338_4
ENCFF166IDS,4336606,GRCh38,GM23338,cell line,"Michael Snyder, Stanford",ENCSR485TLP,https://www.encodeproject.org/files/ENCFF166IDS/@@download/ENCFF166IDS.bed.gz,GM23338_5
ENCFF699ZTV,4145670,GRCh38,GM23338,cell line,"Michael Snyder, Stanford",ENCSR485TLP,https://www.encodeproject.org/files/ENCFF699ZTV/@@download/ENCFF699ZTV.bed.gz,GM23338_6
ENCFF620WFJ,4577528,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR637XSC,https://www.encodeproject.org/files/ENCFF620WFJ/@@download/ENCFF620WFJ.bed.gz,GM12878_1
ENCFF871TST,4543861,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR637XSC,https://www.encodeproject.org/files/ENCFF871TST/@@download/ENCFF871TST.bed.gz,GM12878_2
ENCFF429WTW,4749694,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR637XSC,https://www.encodeproject.org/files/ENCFF429WTW/@@download/ENCFF429WTW.bed.gz,GM12878_3
ENCFF838ISC,4216836,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR637XSC,https://www.encodeproject.org/files/ENCFF838ISC/@@download/ENCFF838ISC.bed.gz,GM12878_4
ENCFF452ZBX,4134636,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR637XSC,https://www.encodeproject.org/files/ENCFF452ZBX/@@download/ENCFF452ZBX.bed.gz,GM12878_5
ENCFF914YHG,4093834,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR637XSC,https://www.encodeproject.org/files/ENCFF914YHG/@@download/ENCFF914YHG.bed.gz,GM12878_6
ENCFF674RFR,4620847,GRCh38,A549,cell line,"Michael Snyder, Stanford",ENCSR032RGS,https://www.encodeproject.org/files/ENCFF674RFR/@@download/ENCFF674RFR.bed.gz,A549_1
ENCFF429FOV,4567849,GRCh38,A549,cell line,"Michael Snyder, Stanford",ENCSR032RGS,https://www.encodeproject.org/files/ENCFF429FOV/@@download/ENCFF429FOV.bed.gz,A549_2
ENCFF964AUZ,4837330,GRCh38,A549,cell line,"Michael Snyder, Stanford",ENCSR032RGS,https://www.encodeproject.org/files/ENCFF964AUZ/@@download/ENCFF964AUZ.bed.gz,A549_3
ENCFF237URW,4405329,GRCh38,A549,cell line,"Michael Snyder, Stanford",ENCSR032RGS,https://www.encodeproject.org/files/ENCFF237URW/@@download/ENCFF237URW.bed.gz,A549_4
ENCFF948AQW,4217413,GRCh38,A549,cell line,"Michael Snyder, Stanford",ENCSR032RGS,https://www.encodeproject.org/files/ENCFF948AQW/@@download/ENCFF948AQW.bed.gz,A549_5
ENCFF681TUR,3820555,GRCh38,A549,cell line,"Michael Snyder, Stanford",ENCSR032RGS,https://www.encodeproject.org/files/ENCFF681TUR/@@download/ENCFF681TUR.bed.gz,A549_6
ENCFF195CXM,5269149,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR182EZJ,https://www.encodeproject.org/files/ENCFF195CXM/@@download/ENCFF195CXM.bed.gz,SK_N_SH_1
ENCFF780LBV,5324669,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR182EZJ,https://www.encodeproject.org/files/ENCFF780LBV/@@download/ENCFF780LBV.bed.gz,SK_N_SH_2
ENCFF964LKY,5524145,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR182EZJ,https://www.encodeproject.org/files/ENCFF964LKY/@@download/ENCFF964LKY.bed.gz,SK_N_SH_3
ENCFF065XVV,5012855,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR182EZJ,https://www.encodeproject.org/files/ENCFF065XVV/@@download/ENCFF065XVV.bed.gz,SK_N_SH_4
ENCFF023OZB,4956946,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR182EZJ,https://www.encodeproject.org/files/ENCFF023OZB/@@download/ENCFF023OZB.bed.gz,SK_N_SH_5
ENCFF286AFI,4689538,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR182EZJ,https://www.encodeproject.org/files/ENCFF286AFI/@@download/ENCFF286AFI.bed.gz,SK_N_SH_6
ENCFF135AEX,3979733,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR868FGK,https://www.encodeproject.org/files/ENCFF135AEX/@@download/ENCFF135AEX.bed.gz,K562_7
ENCFF223QDM,4040861,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR868FGK,https://www.encodeproject.org/files/ENCFF223QDM/@@download/ENCFF223QDM.bed.gz,K562_8
ENCFF948AFM,4215835,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR868FGK,https://www.encodeproject.org/files/ENCFF948AFM/@@download/ENCFF948AFM.bed.gz,K562_9
ENCFF433EPT,3448006,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR868FGK,https://www.encodeproject.org/files/ENCFF433EPT/@@download/ENCFF433EPT.bed.gz,K562_10
ENCFF771HDN,3460380,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR868FGK,https://www.encodeproject.org/files/ENCFF771HDN/@@download/ENCFF771HDN.bed.gz,K562_11
ENCFF993BAP,3651342,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR868FGK,https://www.encodeproject.org/files/ENCFF993BAP/@@download/ENCFF993BAP.bed.gz,K562_12
ENCFF717RPZ,4883871,GRCh38,colonic mucosa,tissue,"Michael Snyder, Stanford",ENCSR970UNF,https://www.encodeproject.org/files/ENCFF717RPZ/@@download/ENCFF717RPZ.bed.gz,colonic_mucosa_1
ENCFF459HLK,4983141,GRCh38,colonic mucosa,tissue,"Michael Snyder, Stanford",ENCSR970UNF,https://www.encodeproject.org/files/ENCFF459HLK/@@download/ENCFF459HLK.bed.gz,colonic_mucosa_2
ENCFF684BUA,3122406,GRCh38,colonic mucosa,tissue,"Michael Snyder, Stanford",ENCSR970UNF,https://www.encodeproject.org/files/ENCFF684BUA/@@download/ENCFF684BUA.bed.gz,colonic_mucosa_3
ENCFF374BNC,914487,GRCh38,Peyer's patch,tissue,"Michael Snyder, Stanford",ENCSR017RQC,https://www.encodeproject.org/files/ENCFF374BNC/@@download/ENCFF374BNC.bed.gz,Peyer's_patch_1
ENCFF675UHQ,525940,GRCh38,right atrium auricular region,tissue,"Michael Snyder, Stanford",ENCSR062SVK,https://www.encodeproject.org/files/ENCFF675UHQ/@@download/ENCFF675UHQ.bed.gz,right_atrium_auricular_re_1
ENCFF105FRE,1335927,GRCh38,spleen,tissue,"Michael Snyder, Stanford",ENCSR078EBD,https://www.encodeproject.org/files/ENCFF105FRE/@@download/ENCFF105FRE.bed.gz,spleen_2
ENCFF697MUB,1381456,GRCh38,sigmoid colon,tissue,"Michael Snyder, Stanford",ENCSR086OGH,https://www.encodeproject.org/files/ENCFF697MUB/@@download/ENCFF697MUB.bed.gz,sigmoid_colon_1
ENCFF984VPG,433320,GRCh38,esophagus squamous epithelium,tissue,"Michael Snyder, Stanford",ENCSR096BPX,https://www.encodeproject.org/files/ENCFF984VPG/@@download/ENCFF984VPG.bed.gz,esophagus_squamous_epithe_1
ENCFF460SWB,2780144,GRCh38,adrenal gland,tissue,"Michael Snyder, Stanford",ENCSR113MBR,https://www.encodeproject.org/files/ENCFF460SWB/@@download/ENCFF460SWB.bed.gz,adrenal_gland_2
ENCFF174HPZ,574105,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR117PYB,https://www.encodeproject.org/files/ENCFF174HPZ/@@download/ENCFF174HPZ.bed.gz,heart_left_ventricle_2
ENCFF804EIN,1785631,GRCh38,body of pancreas,tissue,"Michael Snyder, Stanford",ENCSR152PSA,https://www.encodeproject.org/files/ENCFF804EIN/@@download/ENCFF804EIN.bed.gz,body_of_pancreas_1
ENCFF675XYC,1595924,GRCh38,thyroid gland,tissue,"Michael Snyder, Stanford",ENCSR201FIW,https://www.encodeproject.org/files/ENCFF675XYC/@@download/ENCFF675XYC.bed.gz,thyroid_gland_2
ENCFF643ZJJ,647879,GRCh38,testis,tissue,"Michael Snyder, Stanford",ENCSR210NKB,https://www.encodeproject.org/files/ENCFF643ZJJ/@@download/ENCFF643ZJJ.bed.gz,testis_1
ENCFF034WCY,2394224,GRCh38,gastrocnemius medialis,tissue,"Michael Snyder, Stanford",ENCSR258JCL,https://www.encodeproject.org/files/ENCFF034WCY/@@download/ENCFF034WCY.bed.gz,gastrocnemius_medialis_1
ENCFF580UXE,1519189,GRCh38,gastroesophageal sphincter,tissue,"Michael Snyder, Stanford",ENCSR260ZIV,https://www.encodeproject.org/files/ENCFF580UXE/@@download/ENCFF580UXE.bed.gz,gastroesophageal_sphincte_1
ENCFF461RJQ,2768705,GRCh38,gastrocnemius medialis,tissue,"Michael Snyder, Stanford",ENCSR308HPZ,https://www.encodeproject.org/files/ENCFF461RJQ/@@download/ENCFF461RJQ.bed.gz,gastrocnemius_medialis_2
ENCFF244DRQ,1913859,GRCh38,stomach,tissue,"Michael Snyder, Stanford",ENCSR337UIU,https://www.encodeproject.org/files/ENCFF244DRQ/@@download/ENCFF244DRQ.bed.gz,stomach_1
ENCFF323ALI,1626312,GRCh38,sigmoid colon,tissue,"Michael Snyder, Stanford",ENCSR355SGJ,https://www.encodeproject.org/files/ENCFF323ALI/@@download/ENCFF323ALI.bed.gz,sigmoid_colon_2
ENCFF522FNJ,876765,GRCh38,right lobe of liver,tissue,"Michael Snyder, Stanford",ENCSR373TDL,https://www.encodeproject.org/files/ENCFF522FNJ/@@download/ENCFF522FNJ.bed.gz,right_lobe_of_liver_4
ENCFF609YMS,1830276,GRCh38,transverse colon,tissue,"Michael Snyder, Stanford",ENCSR386HAZ,https://www.encodeproject.org/files/ENCFF609YMS/@@download/ENCFF609YMS.bed.gz,transverse_colon_1
ENCFF995XLK,2636706,GRCh38,transverse colon,tissue,"Michael Snyder, Stanford",ENCSR404LLJ,https://www.encodeproject.org/files/ENCFF995XLK/@@download/ENCFF995XLK.bed.gz,transverse_colon_2
ENCFF663KQH,668783,GRCh38,adrenal gland,tissue,"Michael Snyder, Stanford",ENCSR414DVK,https://www.encodeproject.org/files/ENCFF663KQH/@@download/ENCFF663KQH.bed.gz,adrenal_gland_3
ENCFF365RAW,3242268,GRCh38,thyroid gland,tissue,"Michael Snyder, Stanford",ENCSR474XFV,https://www.encodeproject.org/files/ENCFF365RAW/@@download/ENCFF365RAW.bed.gz,thyroid_gland_3
ENCFF646EYO,547480,GRCh38,ovary,tissue,"Michael Snyder, Stanford",ENCSR490MSG,https://www.encodeproject.org/files/ENCFF646EYO/@@download/ENCFF646EYO.bed.gz,ovary_3
ENCFF072PVO,183137,GRCh38,body of pancreas,tissue,"Michael Snyder, Stanford",ENCSR515CDW,https://www.encodeproject.org/files/ENCFF072PVO/@@download/ENCFF072PVO.bed.gz,body_of_pancreas_2
ENCFF463WUA,1878011,GRCh38,subcutaneous adipose tissue,tissue,"Michael Snyder, Stanford",ENCSR540BML,https://www.encodeproject.org/files/ENCFF463WUA/@@download/ENCFF463WUA.bed.gz,subcutaneous_adipose_tiss_1
ENCFF511DPE,2027906,GRCh38,adrenal gland,tissue,"Michael Snyder, Stanford",ENCSR548KIL,https://www.encodeproject.org/files/ENCFF511DPE/@@download/ENCFF511DPE.bed.gz,adrenal_gland_4
ENCFF207QOW,1589323,GRCh38,sigmoid colon,tissue,"Michael Snyder, Stanford",ENCSR548QCP,https://www.encodeproject.org/files/ENCFF207QOW/@@download/ENCFF207QOW.bed.gz,sigmoid_colon_3
ENCFF231FCU,812910,GRCh38,coronary artery,tissue,"Michael Snyder, Stanford",ENCSR584AXZ,https://www.encodeproject.org/files/ENCFF231FCU/@@download/ENCFF231FCU.bed.gz,coronary_artery_1
ENCFF272UWG,416684,GRCh38,esophagus muscularis mucosa,tissue,"Michael Snyder, Stanford",ENCSR609GST,https://www.encodeproject.org/files/ENCFF272UWG/@@download/ENCFF272UWG.bed.gz,esophagus_muscularis_muco_1
ENCFF831JKU,2591996,GRCh38,tibial artery,tissue,"Michael Snyder, Stanford",ENCSR630REB,https://www.encodeproject.org/files/ENCFF831JKU/@@download/ENCFF831JKU.bed.gz,tibial_artery_1
ENCFF970MTO,1383251,GRCh38,breast epithelium,tissue,"Michael Snyder, Stanford",ENCSR654UYP,https://www.encodeproject.org/files/ENCFF970MTO/@@download/ENCFF970MTO.bed.gz,breast_epithelium_1
ENCFF251CFX,1818673,GRCh38,transverse colon,tissue,"Michael Snyder, Stanford",ENCSR668VCT,https://www.encodeproject.org/files/ENCFF251CFX/@@download/ENCFF251CFX.bed.gz,transverse_colon_3
ENCFF456JOB,1950460,GRCh38,gastroesophageal sphincter,tissue,"Michael Snyder, Stanford",ENCSR670REK,https://www.encodeproject.org/files/ENCFF456JOB/@@download/ENCFF456JOB.bed.gz,gastroesophageal_sphincte_2
ENCFF666UUK,1229821,GRCh38,gastrocnemius medialis,tissue,"Michael Snyder, Stanford",ENCSR689SDA,https://www.encodeproject.org/files/ENCFF666UUK/@@download/ENCFF666UUK.bed.gz,gastrocnemius_medialis_3
ENCFF668ARP,1156602,GRCh38,transverse colon,tissue,"Michael Snyder, Stanford",ENCSR761TKU,https://www.encodeproject.org/files/ENCFF668ARP/@@download/ENCFF668ARP.bed.gz,transverse_colon_4
ENCFF883WHT,2475927,GRCh38,body of pancreas,tissue,"Michael Snyder, Stanford",ENCSR765MXG,https://www.encodeproject.org/files/ENCFF883WHT/@@download/ENCFF883WHT.bed.gz,body_of_pancreas_3
ENCFF075WWH,669679,GRCh38,body of pancreas,tissue,"Michael Snyder, Stanford",ENCSR773USD,https://www.encodeproject.org/files/ENCFF075WWH/@@download/ENCFF075WWH.bed.gz,body_of_pancreas_4
ENCFF963YOX,1792840,GRCh38,omental fat pad,tissue,"Michael Snyder, Stanford",ENCSR788TRR,https://www.encodeproject.org/files/ENCFF963YOX/@@download/ENCFF963YOX.bed.gz,omental_fat_pad_1
ENCFF953QCT,1006603,GRCh38,gastrocnemius medialis,tissue,"Michael Snyder, Stanford",ENCSR823ZCR,https://www.encodeproject.org/files/ENCFF953QCT/@@download/ENCFF953QCT.bed.gz,gastrocnemius_medialis_4
ENCFF623GOH,645449,GRCh38,tibial nerve,tissue,"Michael Snyder, Stanford",ENCSR831KAH,https://www.encodeproject.org/files/ENCFF623GOH/@@download/ENCFF623GOH.bed.gz,tibial_nerve_1
ENCFF487NCC,2425291,GRCh38,sigmoid colon,tissue,"Michael Snyder, Stanford",ENCSR846VLJ,https://www.encodeproject.org/files/ENCFF487NCC/@@download/ENCFF487NCC.bed.gz,sigmoid_colon_4
ENCFF544RPU,1871224,GRCh38,breast epithelium,tissue,"Michael Snyder, Stanford",ENCSR846ZBX,https://www.encodeproject.org/files/ENCFF544RPU/@@download/ENCFF544RPU.bed.gz,breast_epithelium_2
ENCFF560EBG,795440,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR851EBF,https://www.encodeproject.org/files/ENCFF560EBG/@@download/ENCFF560EBG.bed.gz,heart_left_ventricle_3
ENCFF430VEQ,1114050,GRCh38,stomach,tissue,"Michael Snyder, Stanford",ENCSR851SBY,https://www.encodeproject.org/files/ENCFF430VEQ/@@download/ENCFF430VEQ.bed.gz,stomach_2
ENCFF738IZU,1395412,GRCh38,breast epithelium,tissue,"Michael Snyder, Stanford",ENCSR955JSO,https://www.encodeproject.org/files/ENCFF738IZU/@@download/ENCFF738IZU.bed.gz,breast_epithelium_3
ENCFF963ISM,455242,GRCh38,prostate gland,tissue,"Michael Snyder, Stanford",ENCSR999NKW,https://www.encodeproject.org/files/ENCFF963ISM/@@download/ENCFF963ISM.bed.gz,prostate_gland_1
ENCFF277OGS,3454728,GRCh38,sciatic nerve,tissue,"Michael Snyder, Stanford",ENCSR072UYN,https://www.encodeproject.org/files/ENCFF277OGS/@@download/ENCFF277OGS.bed.gz,sciatic_nerve_1
ENCFF033QDT,3436293,GRCh38,RWPE2,cell line,"Michael Snyder, Stanford",ENCSR080SNF,https://www.encodeproject.org/files/ENCFF033QDT/@@download/ENCFF033QDT.bed.gz,RWPE2_1
ENCFF818HSM,3540279,GRCh38,RWPE2,cell line,"Michael Snyder, Stanford",ENCSR080SNF,https://www.encodeproject.org/files/ENCFF818HSM/@@download/ENCFF818HSM.bed.gz,RWPE2_2
ENCFF646WSM,3761386,GRCh38,RWPE2,cell line,"Michael Snyder, Stanford",ENCSR080SNF,https://www.encodeproject.org/files/ENCFF646WSM/@@download/ENCFF646WSM.bed.gz,RWPE2_3
ENCFF652GKL,2354025,GRCh38,RWPE2,cell line,"Michael Snyder, Stanford",ENCSR080SNF,https://www.encodeproject.org/files/ENCFF652GKL/@@download/ENCFF652GKL.bed.gz,RWPE2_4
ENCFF104IMP,2502235,GRCh38,RWPE2,cell line,"Michael Snyder, Stanford",ENCSR080SNF,https://www.encodeproject.org/files/ENCFF104IMP/@@download/ENCFF104IMP.bed.gz,RWPE2_5
ENCFF663XSQ,2815515,GRCh38,RWPE2,cell line,"Michael Snyder, Stanford",ENCSR080SNF,https://www.encodeproject.org/files/ENCFF663XSQ/@@download/ENCFF663XSQ.bed.gz,RWPE2_6
ENCFF168ASJ,3327825,GRCh38,sciatic nerve,tissue,"Michael Snyder, Stanford",ENCSR120MOY,https://www.encodeproject.org/files/ENCFF168ASJ/@@download/ENCFF168ASJ.bed.gz,sciatic_nerve_2
ENCFF131BJK,2482238,GRCh38,posterior vena cava,tissue,"Michael Snyder, Stanford",ENCSR213DBU,https://www.encodeproject.org/files/ENCFF131BJK/@@download/ENCFF131BJK.bed.gz,posterior_vena_cava_2
ENCFF368ACI,1965035,GRCh38,aorta,tissue,"Michael Snyder, Stanford",ENCSR295TFY,https://www.encodeproject.org/files/ENCFF368ACI/@@download/ENCFF368ACI.bed.gz,aorta_1
ENCFF024FSS,2381282,GRCh38,left cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR390SLL,https://www.encodeproject.org/files/ENCFF024FSS/@@download/ENCFF024FSS.bed.gz,left_cardiac_atrium_2
ENCFF802BQC,1003907,GRCh38,sciatic nerve,tissue,"Michael Snyder, Stanford",ENCSR533WFB,https://www.encodeproject.org/files/ENCFF802BQC/@@download/ENCFF802BQC.bed.gz,sciatic_nerve_3
ENCFF478CDQ,2362616,GRCh38,DND-41,cell line,"Michael Snyder, Stanford",ENCSR660WSB,https://www.encodeproject.org/files/ENCFF478CDQ/@@download/ENCFF478CDQ.bed.gz,DND_41_1
ENCFF635DAN,2374974,GRCh38,DND-41,cell line,"Michael Snyder, Stanford",ENCSR660WSB,https://www.encodeproject.org/files/ENCFF635DAN/@@download/ENCFF635DAN.bed.gz,DND_41_2
ENCFF225RUL,2539167,GRCh38,DND-41,cell line,"Michael Snyder, Stanford",ENCSR660WSB,https://www.encodeproject.org/files/ENCFF225RUL/@@download/ENCFF225RUL.bed.gz,DND_41_3
ENCFF073XWR,1774075,GRCh38,DND-41,cell line,"Michael Snyder, Stanford",ENCSR660WSB,https://www.encodeproject.org/files/ENCFF073XWR/@@download/ENCFF073XWR.bed.gz,DND_41_4
ENCFF455OSM,1892202,GRCh38,DND-41,cell line,"Michael Snyder, Stanford",ENCSR660WSB,https://www.encodeproject.org/files/ENCFF455OSM/@@download/ENCFF455OSM.bed.gz,DND_41_5
ENCFF126TTT,1749982,GRCh38,DND-41,cell line,"Michael Snyder, Stanford",ENCSR660WSB,https://www.encodeproject.org/files/ENCFF126TTT/@@download/ENCFF126TTT.bed.gz,DND_41_6
ENCFF239PZJ,1103509,GRCh38,left cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR736CIN,https://www.encodeproject.org/files/ENCFF239PZJ/@@download/ENCFF239PZJ.bed.gz,left_cardiac_atrium_3
ENCFF201EYO,3853011,GRCh38,pancreas,tissue,"Michael Snyder, Stanford",ENCSR808ZMK,https://www.encodeproject.org/files/ENCFF201EYO/@@download/ENCFF201EYO.bed.gz,pancreas_2
ENCFF967XFP,3519953,GRCh38,adrenal gland,tissue,"Michael Snyder, Stanford",ENCSR864ADD,https://www.encodeproject.org/files/ENCFF967XFP/@@download/ENCFF967XFP.bed.gz,adrenal_gland_5
ENCFF775KEG,2590933,GRCh38,psoas muscle,tissue,"Michael Snyder, Stanford",ENCSR890DWH,https://www.encodeproject.org/files/ENCFF775KEG/@@download/ENCFF775KEG.bed.gz,psoas_muscle_1
ENCFF667GYI,1225067,GRCh38,posterior vena cava,tissue,"Michael Snyder, Stanford",ENCSR913FZQ,https://www.encodeproject.org/files/ENCFF667GYI/@@download/ENCFF667GYI.bed.gz,posterior_vena_cava_3
ENCFF251ADS,2283733,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR327DCG,https://www.encodeproject.org/files/ENCFF251ADS/@@download/ENCFF251ADS.bed.gz,heart_right_ventricle_3
ENCFF379NJZ,2031531,GRCh38,mucosa of urinary bladder,tissue,"Michael Snyder, Stanford",ENCSR548SSP,https://www.encodeproject.org/files/ENCFF379NJZ/@@download/ENCFF379NJZ.bed.gz,mucosa_of_urinary_bladder_1
ENCFF368MPK,3287682,GRCh38,mesenteric fat pad,tissue,"Michael Snyder, Stanford",ENCSR551CSY,https://www.encodeproject.org/files/ENCFF368MPK/@@download/ENCFF368MPK.bed.gz,mesenteric_fat_pad_1
ENCFF385QXA,3336201,GRCh38,mesenteric fat pad,tissue,"Michael Snyder, Stanford",ENCSR551CSY,https://www.encodeproject.org/files/ENCFF385QXA/@@download/ENCFF385QXA.bed.gz,mesenteric_fat_pad_2
ENCFF049BCQ,1928156,GRCh38,mesenteric fat pad,tissue,"Michael Snyder, Stanford",ENCSR551CSY,https://www.encodeproject.org/files/ENCFF049BCQ/@@download/ENCFF049BCQ.bed.gz,mesenteric_fat_pad_3
ENCFF433YOV,2064095,GRCh38,bile duct,tissue,"Michael Snyder, Stanford",ENCSR653ZWW,https://www.encodeproject.org/files/ENCFF433YOV/@@download/ENCFF433YOV.bed.gz,bile_duct_2
ENCFF476LUQ,867809,GRCh38,spleen,tissue,"Michael Snyder, Stanford",ENCSR694OWY,https://www.encodeproject.org/files/ENCFF476LUQ/@@download/ENCFF476LUQ.bed.gz,spleen_3
ENCFF360KWB,2473391,GRCh38,pancreas,tissue,"Michael Snyder, Stanford",ENCSR705KEB,https://www.encodeproject.org/files/ENCFF360KWB/@@download/ENCFF360KWB.bed.gz,pancreas_3
ENCFF344WAS,1277502,GRCh38,uterus,tissue,"Michael Snyder, Stanford",ENCSR705RZX,https://www.encodeproject.org/files/ENCFF344WAS/@@download/ENCFF344WAS.bed.gz,uterus_2
ENCFF863FLJ,2270036,GRCh38,ureter,tissue,"Michael Snyder, Stanford",ENCSR814RGG,https://www.encodeproject.org/files/ENCFF863FLJ/@@download/ENCFF863FLJ.bed.gz,ureter_1
ENCFF055YGM,1785911,GRCh38,right cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR822BAD,https://www.encodeproject.org/files/ENCFF055YGM/@@download/ENCFF055YGM.bed.gz,right_cardiac_atrium_2
ENCFF176EOV,3369601,GRCh38,pancreas,tissue,"Michael Snyder, Stanford",ENCSR918TVE,https://www.encodeproject.org/files/ENCFF176EOV/@@download/ENCFF176EOV.bed.gz,pancreas_4
ENCFF984UBD,3665360,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR925LGW,https://www.encodeproject.org/files/ENCFF984UBD/@@download/ENCFF984UBD.bed.gz,heart_left_ventricle_4
ENCFF690FXR,1756946,GRCh38,spleen,tissue,"Michael Snyder, Stanford",ENCSR979TCG,https://www.encodeproject.org/files/ENCFF690FXR/@@download/ENCFF690FXR.bed.gz,spleen_4
ENCFF173AVZ,3816599,GRCh38,mucosa of urinary bladder,tissue,"Michael Snyder, Stanford",ENCSR153IUB,https://www.encodeproject.org/files/ENCFF173AVZ/@@download/ENCFF173AVZ.bed.gz,mucosa_of_urinary_bladder_2
ENCFF141SSO,1664593,GRCh38,psoas muscle,tissue,"Michael Snyder, Stanford",ENCSR154UWN,https://www.encodeproject.org/files/ENCFF141SSO/@@download/ENCFF141SSO.bed.gz,psoas_muscle_2
ENCFF659MXY,1131794,GRCh38,cardiac septum,tissue,"Michael Snyder, Stanford",ENCSR167SYF,https://www.encodeproject.org/files/ENCFF659MXY/@@download/ENCFF659MXY.bed.gz,cardiac_septum_1
ENCFF917NNO,1775754,GRCh38,bile duct,tissue,"Michael Snyder, Stanford",ENCSR174MLD,https://www.encodeproject.org/files/ENCFF917NNO/@@download/ENCFF917NNO.bed.gz,bile_duct_3
ENCFF792EIF,1899641,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR204SMO,https://www.encodeproject.org/files/ENCFF792EIF/@@download/ENCFF792EIF.bed.gz,heart_right_ventricle_4
ENCFF254OVS,3123068,GRCh38,esophagus mucosa,tissue,"Michael Snyder, Stanford",ENCSR303PWB,https://www.encodeproject.org/files/ENCFF254OVS/@@download/ENCFF254OVS.bed.gz,esophagus_mucosa_1
ENCFF718BTP,3015478,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR399OSE,https://www.encodeproject.org/files/ENCFF718BTP/@@download/ENCFF718BTP.bed.gz,heart_left_ventricle_5
ENCFF315UYG,3101182,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR437OOJ,https://www.encodeproject.org/files/ENCFF315UYG/@@download/ENCFF315UYG.bed.gz,heart_right_ventricle_5
ENCFF886PWO,3231400,GRCh38,kidney,tissue,"Michael Snyder, Stanford",ENCSR297VGU,https://www.encodeproject.org/files/ENCFF886PWO/@@download/ENCFF886PWO.bed.gz,kidney_1
ENCFF464HPU,4836821,GRCh38,mucosa of descending colon,tissue,"Michael Snyder, Stanford",ENCSR491VXJ,https://www.encodeproject.org/files/ENCFF464HPU/@@download/ENCFF464HPU.bed.gz,mucosa_of_descending_colo_2
ENCFF481MCI,4421403,GRCh38,adrenal gland,tissue,"Michael Snyder, Stanford",ENCSR542RNG,https://www.encodeproject.org/files/ENCFF481MCI/@@download/ENCFF481MCI.bed.gz,adrenal_gland_6
ENCFF993UXH,4461341,GRCh38,left colon,tissue,"Michael Snyder, Stanford",ENCSR600ZHS,https://www.encodeproject.org/files/ENCFF993UXH/@@download/ENCFF993UXH.bed.gz,left_colon_1
ENCFF659AAS,2240715,GRCh38,sciatic nerve,tissue,"Michael Snyder, Stanford",ENCSR619LHF,https://www.encodeproject.org/files/ENCFF659AAS/@@download/ENCFF659AAS.bed.gz,sciatic_nerve_4
ENCFF389YDC,4336347,GRCh38,adrenal gland,tissue,"Michael Snyder, Stanford",ENCSR651SOJ,https://www.encodeproject.org/files/ENCFF389YDC/@@download/ENCFF389YDC.bed.gz,adrenal_gland_7
ENCFF037ANG,4278013,GRCh38,mucosa of gallbladder,tissue,"Michael Snyder, Stanford",ENCSR695FLC,https://www.encodeproject.org/files/ENCFF037ANG/@@download/ENCFF037ANG.bed.gz,mucosa_of_gallbladder_1
ENCFF800IWT,3372221,GRCh38,psoas muscle,tissue,"Michael Snyder, Stanford",ENCSR886VCC,https://www.encodeproject.org/files/ENCFF800IWT/@@download/ENCFF800IWT.bed.gz,psoas_muscle_3
ENCFF784MGH,2424972,GRCh38,psoas muscle,tissue,"Michael Snyder, Stanford",ENCSR040DJK,https://www.encodeproject.org/files/ENCFF784MGH/@@download/ENCFF784MGH.bed.gz,psoas_muscle_4
ENCFF720GJU,1427798,GRCh38,left cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR330WUK,https://www.encodeproject.org/files/ENCFF720GJU/@@download/ENCFF720GJU.bed.gz,left_cardiac_atrium_4
ENCFF725YMT,1225917,GRCh38,jejunum,tissue,"Michael Snyder, Stanford",ENCSR415LEB,https://www.encodeproject.org/files/ENCFF725YMT/@@download/ENCFF725YMT.bed.gz,jejunum_1
ENCFF567VUC,3166948,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR522FGI,https://www.encodeproject.org/files/ENCFF567VUC/@@download/ENCFF567VUC.bed.gz,heart_right_ventricle_6
ENCFF830CEC,1143184,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR593YFB,https://www.encodeproject.org/files/ENCFF830CEC/@@download/ENCFF830CEC.bed.gz,heart_left_ventricle_6
ENCFF556YGF,1588554,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR710SMN,https://www.encodeproject.org/files/ENCFF556YGF/@@download/ENCFF556YGF.bed.gz,heart_right_ventricle_7
ENCFF991MKU,1200488,GRCh38,left cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR731ODJ,https://www.encodeproject.org/files/ENCFF991MKU/@@download/ENCFF991MKU.bed.gz,left_cardiac_atrium_5
ENCFF303AKJ,1687359,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR846VPV,https://www.encodeproject.org/files/ENCFF303AKJ/@@download/ENCFF303AKJ.bed.gz,heart_left_ventricle_7
ENCFF905LJQ,1136741,GRCh38,ovary,tissue,"Michael Snyder, Stanford",ENCSR872MAO,https://www.encodeproject.org/files/ENCFF905LJQ/@@download/ENCFF905LJQ.bed.gz,ovary_4
ENCFF291YRH,1316999,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR899YEP,https://www.encodeproject.org/files/ENCFF291YRH/@@download/ENCFF291YRH.bed.gz,heart_left_ventricle_8
ENCFF200KIN,812153,GRCh38,left ventricle myocardium inferior,tissue,"Michael Snyder, Stanford",ENCSR946MAI,https://www.encodeproject.org/files/ENCFF200KIN/@@download/ENCFF200KIN.bed.gz,left_ventricle_myocardium_2
ENCFF257UAY,2596253,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR157OSO,https://www.encodeproject.org/files/ENCFF257UAY/@@download/ENCFF257UAY.bed.gz,heart_right_ventricle_8
ENCFF779AOG,1416845,GRCh38,spleen,tissue,"Michael Snyder, Stanford",ENCSR158KCS,https://www.encodeproject.org/files/ENCFF779AOG/@@download/ENCFF779AOG.bed.gz,spleen_5
ENCFF968YWL,3553628,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR212LYK,https://www.encodeproject.org/files/ENCFF968YWL/@@download/ENCFF968YWL.bed.gz,heart_right_ventricle_9
ENCFF783MYY,2817445,GRCh38,right cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR213YPO,https://www.encodeproject.org/files/ENCFF783MYY/@@download/ENCFF783MYY.bed.gz,right_cardiac_atrium_3
ENCFF915OUC,1902864,GRCh38,fallopian tube,tissue,"Michael Snyder, Stanford",ENCSR565DSD,https://www.encodeproject.org/files/ENCFF915OUC/@@download/ENCFF915OUC.bed.gz,fallopian_tube_1
ENCFF988KSK,1952088,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR745CGG,https://www.encodeproject.org/files/ENCFF988KSK/@@download/ENCFF988KSK.bed.gz,heart_left_ventricle_9
ENCFF363FTI,2532083,GRCh38,Right ventricle myocardium inferior,tissue,"Michael Snyder, Stanford",ENCSR855BMI,https://www.encodeproject.org/files/ENCFF363FTI/@@download/ENCFF363FTI.bed.gz,Right_ventricle_myocardiu_1
ENCFF184BRH,3683611,GRCh38,Right ventricle myocardium superior,tissue,"Michael Snyder, Stanford",ENCSR074WMH,https://www.encodeproject.org/files/ENCFF184BRH/@@download/ENCFF184BRH.bed.gz,Right_ventricle_myocardiu_2
ENCFF423JET,3849371,GRCh38,fallopian tube,tissue,"Michael Snyder, Stanford",ENCSR212LAZ,https://www.encodeproject.org/files/ENCFF423JET/@@download/ENCFF423JET.bed.gz,fallopian_tube_2
ENCFF628CCO,4358433,GRCh38,ovary,tissue,"Michael Snyder, Stanford",ENCSR227FVE,https://www.encodeproject.org/files/ENCFF628CCO/@@download/ENCFF628CCO.bed.gz,ovary_5
ENCFF501NNC,3330836,GRCh38,fallopian tube,tissue,"Michael Snyder, Stanford",ENCSR238VHV,https://www.encodeproject.org/files/ENCFF501NNC/@@download/ENCFF501NNC.bed.gz,fallopian_tube_3
ENCFF315XZD,4693230,GRCh38,adrenal gland,tissue,"Michael Snyder, Stanford",ENCSR241OBO,https://www.encodeproject.org/files/ENCFF315XZD/@@download/ENCFF315XZD.bed.gz,adrenal_gland_8
ENCFF347HUL,4309025,GRCh38,right lobe of liver,tissue,"Michael Snyder, Stanford",ENCSR607BTF,https://www.encodeproject.org/files/ENCFF347HUL/@@download/ENCFF347HUL.bed.gz,right_lobe_of_liver_5
ENCFF203KWW,4152289,GRCh38,psoas muscle,tissue,"Michael Snyder, Stanford",ENCSR774NXA,https://www.encodeproject.org/files/ENCFF203KWW/@@download/ENCFF203KWW.bed.gz,psoas_muscle_5
ENCFF250ANB,3318355,GRCh38,ovary,tissue,"Michael Snyder, Stanford",ENCSR996ZCR,https://www.encodeproject.org/files/ENCFF250ANB/@@download/ENCFF250ANB.bed.gz,ovary_6
ENCFF438JMM,2921017,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR042AWH,https://www.encodeproject.org/files/ENCFF438JMM/@@download/ENCFF438JMM.bed.gz,HepG2_7
ENCFF851SSE,1948400,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR042AWH,https://www.encodeproject.org/files/ENCFF851SSE/@@download/ENCFF851SSE.bed.gz,HepG2_8
ENCFF791RKW,2055230,GRCh38,HepG2,cell line,"Michael Snyder, Stanford",ENCSR042AWH,https://www.encodeproject.org/files/ENCFF791RKW/@@download/ENCFF791RKW.bed.gz,HepG2_9
ENCFF179JFB,2418240,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR053SGP,https://www.encodeproject.org/files/ENCFF179JFB/@@download/ENCFF179JFB.bed.gz,heart_right_ventricle_10
ENCFF945SYZ,3476303,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR095QNB,https://www.encodeproject.org/files/ENCFF945SYZ/@@download/ENCFF945SYZ.bed.gz,GM12878_7
ENCFF614SMH,2797797,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR095QNB,https://www.encodeproject.org/files/ENCFF614SMH/@@download/ENCFF614SMH.bed.gz,GM12878_8
ENCFF917REN,2427146,GRCh38,GM12878,cell line,"Michael Snyder, Stanford",ENCSR095QNB,https://www.encodeproject.org/files/ENCFF917REN/@@download/ENCFF917REN.bed.gz,GM12878_9
ENCFF898WYV,3302236,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR133CMC,https://www.encodeproject.org/files/ENCFF898WYV/@@download/ENCFF898WYV.bed.gz,heart_right_ventricle_11
ENCFF114GDS,3829555,GRCh38,IMR-90,cell line,"Michael Snyder, Stanford",ENCSR200OML,https://www.encodeproject.org/files/ENCFF114GDS/@@download/ENCFF114GDS.bed.gz,IMR_90_1
ENCFF383XOM,2742247,GRCh38,IMR-90,cell line,"Michael Snyder, Stanford",ENCSR200OML,https://www.encodeproject.org/files/ENCFF383XOM/@@download/ENCFF383XOM.bed.gz,IMR_90_2
ENCFF235JZB,3259043,GRCh38,IMR-90,cell line,"Michael Snyder, Stanford",ENCSR200OML,https://www.encodeproject.org/files/ENCFF235JZB/@@download/ENCFF235JZB.bed.gz,IMR_90_3
ENCFF258HEB,1768475,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR225FZH,https://www.encodeproject.org/files/ENCFF258HEB/@@download/ENCFF258HEB.bed.gz,heart_left_ventricle_10
ENCFF525ZRG,2764152,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR310RJN,https://www.encodeproject.org/files/ENCFF525ZRG/@@download/ENCFF525ZRG.bed.gz,heart_left_ventricle_11
ENCFF882OVP,3781193,GRCh38,MCF-7,cell line,"Michael Snyder, Stanford",ENCSR422SUG,https://www.encodeproject.org/files/ENCFF882OVP/@@download/ENCFF882OVP.bed.gz,MCF_7_1
ENCFF204NII,3064270,GRCh38,MCF-7,cell line,"Michael Snyder, Stanford",ENCSR422SUG,https://www.encodeproject.org/files/ENCFF204NII/@@download/ENCFF204NII.bed.gz,MCF_7_2
ENCFF882FLC,2861325,GRCh38,MCF-7,cell line,"Michael Snyder, Stanford",ENCSR422SUG,https://www.encodeproject.org/files/ENCFF882FLC/@@download/ENCFF882FLC.bed.gz,MCF_7_3
ENCFF643QLU,2833458,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR439TZT,https://www.encodeproject.org/files/ENCFF643QLU/@@download/ENCFF643QLU.bed.gz,heart_right_ventricle_12
ENCFF925CYR,2777441,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR483RKN,https://www.encodeproject.org/files/ENCFF925CYR/@@download/ENCFF925CYR.bed.gz,K562_13
ENCFF976CEI,1892653,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR483RKN,https://www.encodeproject.org/files/ENCFF976CEI/@@download/ENCFF976CEI.bed.gz,K562_14
ENCFF117MSK,2038046,GRCh38,K562,cell line,"Michael Snyder, Stanford",ENCSR483RKN,https://www.encodeproject.org/files/ENCFF117MSK/@@download/ENCFF117MSK.bed.gz,K562_15
ENCFF369VMF,4237124,GRCh38,PC-3,cell line,"Michael Snyder, Stanford",ENCSR499ASS,https://www.encodeproject.org/files/ENCFF369VMF/@@download/ENCFF369VMF.bed.gz,PC_3_1
ENCFF742PNL,3602234,GRCh38,PC-3,cell line,"Michael Snyder, Stanford",ENCSR499ASS,https://www.encodeproject.org/files/ENCFF742PNL/@@download/ENCFF742PNL.bed.gz,PC_3_2
ENCFF061YKV,3490754,GRCh38,PC-3,cell line,"Michael Snyder, Stanford",ENCSR499ASS,https://www.encodeproject.org/files/ENCFF061YKV/@@download/ENCFF061YKV.bed.gz,PC_3_3
ENCFF202KDL,2590710,GRCh38,ovary,tissue,"Michael Snyder, Stanford",ENCSR571JFO,https://www.encodeproject.org/files/ENCFF202KDL/@@download/ENCFF202KDL.bed.gz,ovary_7
ENCFF720DJU,4631377,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR587TRP,https://www.encodeproject.org/files/ENCFF720DJU/@@download/ENCFF720DJU.bed.gz,SK_N_SH_7
ENCFF461GIA,4552014,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR587TRP,https://www.encodeproject.org/files/ENCFF461GIA/@@download/ENCFF461GIA.bed.gz,SK_N_SH_8
ENCFF855CQR,4906542,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR587TRP,https://www.encodeproject.org/files/ENCFF855CQR/@@download/ENCFF855CQR.bed.gz,SK_N_SH_9
ENCFF833RCJ,4279286,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR587TRP,https://www.encodeproject.org/files/ENCFF833RCJ/@@download/ENCFF833RCJ.bed.gz,SK_N_SH_10
ENCFF464MPR,4366506,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR587TRP,https://www.encodeproject.org/files/ENCFF464MPR/@@download/ENCFF464MPR.bed.gz,SK_N_SH_11
ENCFF371ZSI,3749410,GRCh38,SK-N-SH,cell line,"Michael Snyder, Stanford",ENCSR587TRP,https://www.encodeproject.org/files/ENCFF371ZSI/@@download/ENCFF371ZSI.bed.gz,SK_N_SH_12
ENCFF481ELM,3106941,GRCh38,Panc1,cell line,"Michael Snyder, Stanford",ENCSR591PIX,https://www.encodeproject.org/files/ENCFF481ELM/@@download/ENCFF481ELM.bed.gz,Panc1_1
ENCFF510OSH,2398611,GRCh38,Panc1,cell line,"Michael Snyder, Stanford",ENCSR591PIX,https://www.encodeproject.org/files/ENCFF510OSH/@@download/ENCFF510OSH.bed.gz,Panc1_2
ENCFF953NZY,2239403,GRCh38,Panc1,cell line,"Michael Snyder, Stanford",ENCSR591PIX,https://www.encodeproject.org/files/ENCFF953NZY/@@download/ENCFF953NZY.bed.gz,Panc1_3
ENCFF068ZZZ,1055111,GRCh38,lung,tissue,"Michael Snyder, Stanford",ENCSR647AOY,https://www.encodeproject.org/files/ENCFF068ZZZ/@@download/ENCFF068ZZZ.bed.gz,lung_1
ENCFF811VNG,2078593,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR848TMJ,https://www.encodeproject.org/files/ENCFF811VNG/@@download/ENCFF811VNG.bed.gz,heart_left_ventricle_12
ENCFF054UBO,3772035,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR872WGW,https://www.encodeproject.org/files/ENCFF054UBO/@@download/ENCFF054UBO.bed.gz,HCT116_49
ENCFF429LWF,3119239,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR872WGW,https://www.encodeproject.org/files/ENCFF429LWF/@@download/ENCFF429LWF.bed.gz,HCT116_50
ENCFF647AGG,2872496,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR872WGW,https://www.encodeproject.org/files/ENCFF647AGG/@@download/ENCFF647AGG.bed.gz,HCT116_51
ENCFF890NRK,2698678,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR204PZT,https://www.encodeproject.org/files/ENCFF890NRK/@@download/ENCFF890NRK.bed.gz,heart_left_ventricle_13
ENCFF901EKY,2740617,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR451JSB,https://www.encodeproject.org/files/ENCFF901EKY/@@download/ENCFF901EKY.bed.gz,heart_left_ventricle_14
ENCFF072LFQ,3261133,GRCh38,heart left ventricle,tissue,"Michael Snyder, Stanford",ENCSR286STX,https://www.encodeproject.org/files/ENCFF072LFQ/@@download/ENCFF072LFQ.bed.gz,heart_left_ventricle_15
ENCFF199VHV,3353224,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR563ZNI,https://www.encodeproject.org/files/ENCFF199VHV/@@download/ENCFF199VHV.bed.gz,heart_right_ventricle_13
ENCFF138ISY,4393642,GRCh38,left lung,tissue,"Michael Snyder, Stanford",ENCSR682ETE,https://www.encodeproject.org/files/ENCFF138ISY/@@download/ENCFF138ISY.bed.gz,left_lung_1
ENCFF654SUU,2379245,GRCh38,liver,tissue,"Michael Snyder, Stanford",ENCSR124NNL,https://www.encodeproject.org/files/ENCFF654SUU/@@download/ENCFF654SUU.bed.gz,liver_1
ENCFF775OEK,1563313,GRCh38,upper lobe of left lung,tissue,"Michael Snyder, Stanford",ENCSR242QRV,https://www.encodeproject.org/files/ENCFF775OEK/@@download/ENCFF775OEK.bed.gz,upper_lobe_of_left_lung_2
ENCFF372VNL,3565825,GRCh38,heart right ventricle,tissue,"Michael Snyder, Stanford",ENCSR374ZRW,https://www.encodeproject.org/files/ENCFF372VNL/@@download/ENCFF372VNL.bed.gz,heart_right_ventricle_14
ENCFF981WQJ,4521613,GRCh38,pancreas,tissue,"Michael Snyder, Stanford",ENCSR530XBF,https://www.encodeproject.org/files/ENCFF981WQJ/@@download/ENCFF981WQJ.bed.gz,pancreas_5
ENCFF778HMH,3670600,GRCh38,lower lobe of right lung,tissue,"Michael Snyder, Stanford",ENCSR948MQC,https://www.encodeproject.org/files/ENCFF778HMH/@@download/ENCFF778HMH.bed.gz,lower_lobe_of_right_lung_2
ENCFF912MCC,3737196,GRCh38,right lobe of liver,tissue,"Michael Snyder, Stanford",ENCSR516CPW,https://www.encodeproject.org/files/ENCFF912MCC/@@download/ENCFF912MCC.bed.gz,right_lobe_of_liver_6
ENCFF962LEY,1950610,GRCh38,HG03196,cell line,"Stephen Montgomery, Stanford",ENCSR063UNG,https://www.encodeproject.org/files/ENCFF962LEY/@@download/ENCFF962LEY.bed.gz,HG03196_1
ENCFF586OGP,2152897,GRCh38,GM18517,cell line,"Stephen Montgomery, Stanford",ENCSR442OAV,https://www.encodeproject.org/files/ENCFF586OGP/@@download/ENCFF586OGP.bed.gz,GM18517_1
ENCFF538YUQ,2998871,GRCh38,GM18867,cell line,"Stephen Montgomery, Stanford",ENCSR492FJO,https://www.encodeproject.org/files/ENCFF538YUQ/@@download/ENCFF538YUQ.bed.gz,GM18867_1
ENCFF816FTJ,1677019,GRCh38,HG03097,cell line,"Stephen Montgomery, Stanford",ENCSR608KJD,https://www.encodeproject.org/files/ENCFF816FTJ/@@download/ENCFF816FTJ.bed.gz,HG03097_1
ENCFF451ZYJ,2339186,GRCh38,HG03442,cell line,"Stephen Montgomery, Stanford",ENCSR858GTE,https://www.encodeproject.org/files/ENCFF451ZYJ/@@download/ENCFF451ZYJ.bed.gz,HG03442_1
ENCFF918IOX,1648419,GRCh38,HG02943,cell line,"Stephen Montgomery, Stanford",ENCSR123WME,https://www.encodeproject.org/files/ENCFF918IOX/@@download/ENCFF918IOX.bed.gz,HG02943_1
ENCFF002SXG,2004950,GRCh38,GM19438,cell line,"Stephen Montgomery, Stanford",ENCSR134OSR,https://www.encodeproject.org/files/ENCFF002SXG/@@download/ENCFF002SXG.bed.gz,GM19438_1
ENCFF619LDY,1480615,GRCh38,HG03457,cell line,"Stephen Montgomery, Stanford",ENCSR322SBN,https://www.encodeproject.org/files/ENCFF619LDY/@@download/ENCFF619LDY.bed.gz,HG03457_1
ENCFF027KMZ,829933,GRCh38,HG03575,cell line,"Stephen Montgomery, Stanford",ENCSR331JFZ,https://www.encodeproject.org/files/ENCFF027KMZ/@@download/ENCFF027KMZ.bed.gz,HG03575_1
ENCFF007OSO,2130498,GRCh38,HG03460,cell line,"Stephen Montgomery, Stanford",ENCSR345NVR,https://www.encodeproject.org/files/ENCFF007OSO/@@download/ENCFF007OSO.bed.gz,HG03460_1
ENCFF682ZZT,2006175,GRCh38,HG03095,cell line,"Stephen Montgomery, Stanford",ENCSR359HFH,https://www.encodeproject.org/files/ENCFF682ZZT/@@download/ENCFF682ZZT.bed.gz,HG03095_1
ENCFF030FZS,2001643,GRCh38,GM21526,cell line,"Stephen Montgomery, Stanford",ENCSR408IVU,https://www.encodeproject.org/files/ENCFF030FZS/@@download/ENCFF030FZS.bed.gz,GM21526_1
ENCFF003NCK,1917754,GRCh38,GM19043,cell line,"Stephen Montgomery, Stanford",ENCSR409YCK,https://www.encodeproject.org/files/ENCFF003NCK/@@download/ENCFF003NCK.bed.gz,GM19043_1
ENCFF844BQZ,2279795,GRCh38,GM21447,cell line,"Stephen Montgomery, Stanford",ENCSR424KQH,https://www.encodeproject.org/files/ENCFF844BQZ/@@download/ENCFF844BQZ.bed.gz,GM21447_1
ENCFF611XGS,2242927,GRCh38,GM19452,cell line,"Stephen Montgomery, Stanford",ENCSR454UTH,https://www.encodeproject.org/files/ENCFF611XGS/@@download/ENCFF611XGS.bed.gz,GM19452_1
ENCFF915GOP,3241275,GRCh38,GM18868,cell line,"Stephen Montgomery, Stanford",ENCSR580JBA,https://www.encodeproject.org/files/ENCFF915GOP/@@download/ENCFF915GOP.bed.gz,GM18868_1
ENCFF598EDX,1919277,GRCh38,HG03520,cell line,"Stephen Montgomery, Stanford",ENCSR624CTU,https://www.encodeproject.org/files/ENCFF598EDX/@@download/ENCFF598EDX.bed.gz,HG03520_1
ENCFF828UQL,2088644,GRCh38,GM21825,cell line,"Stephen Montgomery, Stanford",ENCSR641KNU,https://www.encodeproject.org/files/ENCFF828UQL/@@download/ENCFF828UQL.bed.gz,GM21825_1
ENCFF900WSD,2262847,GRCh38,GM21423,cell line,"Stephen Montgomery, Stanford",ENCSR902CFR,https://www.encodeproject.org/files/ENCFF900WSD/@@download/ENCFF900WSD.bed.gz,GM21423_1
ENCFF072JKR,2930695,GRCh38,GM18502,cell line,"Stephen Montgomery, Stanford",ENCSR241VGH,https://www.encodeproject.org/files/ENCFF072JKR/@@download/ENCFF072JKR.bed.gz,GM18502_1
ENCFF529BZQ,1463566,GRCh38,glutamatergic neuron,in vitro differentiated cells,"Yin Shen, UCSF",ENCSR187VKR,https://www.encodeproject.org/files/ENCFF529BZQ/@@download/ENCFF529BZQ.bed.gz,glutamatergic_neuron_1
ENCFF531OVM,862514,GRCh38,glutamatergic neuron,in vitro differentiated cells,"Yin Shen, UCSF",ENCSR187VKR,https://www.encodeproject.org/files/ENCFF531OVM/@@download/ENCFF531OVM.bed.gz,glutamatergic_neuron_2
ENCFF124HHD,1136738,GRCh38,glutamatergic neuron,in vitro differentiated cells,"Yin Shen, UCSF",ENCSR187VKR,https://www.encodeproject.org/files/ENCFF124HHD/@@download/ENCFF124HHD.bed.gz,glutamatergic_neuron_3
ENCFF505YPT,1471775,GRCh38,HG03469,cell line,"Stephen Montgomery, Stanford",ENCSR163KRG,https://www.encodeproject.org/files/ENCFF505YPT/@@download/ENCFF505YPT.bed.gz,HG03469_1
ENCFF857VFO,2514269,GRCh38,HG02678,cell line,"Stephen Montgomery, Stanford",ENCSR180HEL,https://www.encodeproject.org/files/ENCFF857VFO/@@download/ENCFF857VFO.bed.gz,HG02678_1
ENCFF129JFU,2648739,GRCh38,GM21723,cell line,"Stephen Montgomery, Stanford",ENCSR233HSC,https://www.encodeproject.org/files/ENCFF129JFU/@@download/ENCFF129JFU.bed.gz,GM21723_1
ENCFF557BSF,1573054,GRCh38,GM18873,cell line,"Stephen Montgomery, Stanford",ENCSR304ZDQ,https://www.encodeproject.org/files/ENCFF557BSF/@@download/ENCFF557BSF.bed.gz,GM18873_1
ENCFF796ZYH,1864996,GRCh38,GM19025,cell line,"Stephen Montgomery, Stanford",ENCSR419UYY,https://www.encodeproject.org/files/ENCFF796ZYH/@@download/ENCFF796ZYH.bed.gz,GM19025_1
ENCFF703AAZ,2252425,GRCh38,GM21717,cell line,"Stephen Montgomery, Stanford",ENCSR422RPD,https://www.encodeproject.org/files/ENCFF703AAZ/@@download/ENCFF703AAZ.bed.gz,GM21717_1
ENCFF468CHZ,1233669,GRCh38,HG03060,cell line,"Stephen Montgomery, Stanford",ENCSR470ZNM,https://www.encodeproject.org/files/ENCFF468CHZ/@@download/ENCFF468CHZ.bed.gz,HG03060_1
ENCFF252MOE,1747388,GRCh38,HG03521,cell line,"Stephen Montgomery, Stanford",ENCSR594TMY,https://www.encodeproject.org/files/ENCFF252MOE/@@download/ENCFF252MOE.bed.gz,HG03521_1
ENCFF356XWJ,2937562,GRCh38,HG02885,cell line,"Stephen Montgomery, Stanford",ENCSR621JYI,https://www.encodeproject.org/files/ENCFF356XWJ/@@download/ENCFF356XWJ.bed.gz,HG02885_1
ENCFF551EAY,2289418,GRCh38,GM19035,cell line,"Stephen Montgomery, Stanford",ENCSR637MUF,https://www.encodeproject.org/files/ENCFF551EAY/@@download/ENCFF551EAY.bed.gz,GM19035_1
ENCFF694FCS,1589747,GRCh38,GM18520,cell line,"Stephen Montgomery, Stanford",ENCSR762YFD,https://www.encodeproject.org/files/ENCFF694FCS/@@download/ENCFF694FCS.bed.gz,GM18520_1
ENCFF256UDE,1324871,GRCh38,HG02970,cell line,"Stephen Montgomery, Stanford",ENCSR857MCZ,https://www.encodeproject.org/files/ENCFF256UDE/@@download/ENCFF256UDE.bed.gz,HG02970_1
ENCFF182SEJ,2656409,GRCh38,GM21529,cell line,"Stephen Montgomery, Stanford",ENCSR954BDA,https://www.encodeproject.org/files/ENCFF182SEJ/@@download/ENCFF182SEJ.bed.gz,GM21529_1
ENCFF278HDB,1844795,GRCh38,GM18858,cell line,"Stephen Montgomery, Stanford",ENCSR011NJP,https://www.encodeproject.org/files/ENCFF278HDB/@@download/ENCFF278HDB.bed.gz,GM18858_1
ENCFF810XER,1935741,GRCh38,HG03066,cell line,"Stephen Montgomery, Stanford",ENCSR040PBN,https://www.encodeproject.org/files/ENCFF810XER/@@download/ENCFF810XER.bed.gz,HG03066_1
ENCFF136RJT,2098105,GRCh38,HG02588,cell line,"Stephen Montgomery, Stanford",ENCSR189VFC,https://www.encodeproject.org/files/ENCFF136RJT/@@download/ENCFF136RJT.bed.gz,HG02588_1
ENCFF061UDU,2329264,GRCh38,GM21786,cell line,"Stephen Montgomery, Stanford",ENCSR275LCF,https://www.encodeproject.org/files/ENCFF061UDU/@@download/ENCFF061UDU.bed.gz,GM21786_1
ENCFF961CHG,1607995,GRCh38,GM18519,cell line,"Stephen Montgomery, Stanford",ENCSR312UCH,https://www.encodeproject.org/files/ENCFF961CHG/@@download/ENCFF961CHG.bed.gz,GM18519_1
ENCFF138FQA,1897526,GRCh38,GM19372,cell line,"Stephen Montgomery, Stanford",ENCSR335JVB,https://www.encodeproject.org/files/ENCFF138FQA/@@download/ENCFF138FQA.bed.gz,GM19372_1
ENCFF780EDM,2334489,GRCh38,HG02870,cell line,"Stephen Montgomery, Stanford",ENCSR420RVW,https://www.encodeproject.org/files/ENCFF780EDM/@@download/ENCFF780EDM.bed.gz,HG02870_1
ENCFF197NOP,1886865,GRCh38,GM19324,cell line,"Stephen Montgomery, Stanford",ENCSR425WQW,https://www.encodeproject.org/files/ENCFF197NOP/@@download/ENCFF197NOP.bed.gz,GM19324_1
ENCFF184NOC,1838788,GRCh38,GM21367,cell line,"Stephen Montgomery, Stanford",ENCSR509JPT,https://www.encodeproject.org/files/ENCFF184NOC/@@download/ENCFF184NOC.bed.gz,GM21367_1
ENCFF423TQD,2145756,GRCh38,GM19023,cell line,"Stephen Montgomery, Stanford",ENCSR512FHU,https://www.encodeproject.org/files/ENCFF423TQD/@@download/ENCFF423TQD.bed.gz,GM19023_1
ENCFF976AUE,2102274,GRCh38,HG02884,cell line,"Stephen Montgomery, Stanford",ENCSR604YEQ,https://www.encodeproject.org/files/ENCFF976AUE/@@download/ENCFF976AUE.bed.gz,HG02884_1
ENCFF610BYY,2033380,GRCh38,GM18861,cell line,"Stephen Montgomery, Stanford",ENCSR628PLS,https://www.encodeproject.org/files/ENCFF610BYY/@@download/ENCFF610BYY.bed.gz,GM18861_1
ENCFF550WFX,957811,GRCh38,GM19468,cell line,"Stephen Montgomery, Stanford",ENCSR635AIF,https://www.encodeproject.org/files/ENCFF550WFX/@@download/ENCFF550WFX.bed.gz,GM19468_1
ENCFF452XUT,1217058,GRCh38,GM18499,cell line,"Stephen Montgomery, Stanford",ENCSR700HPA,https://www.encodeproject.org/files/ENCFF452XUT/@@download/ENCFF452XUT.bed.gz,GM18499_1
ENCFF374TYJ,3289008,GRCh38,HG03432,cell line,"Stephen Montgomery, Stanford",ENCSR839UOB,https://www.encodeproject.org/files/ENCFF374TYJ/@@download/ENCFF374TYJ.bed.gz,HG03432_1
ENCFF594MCK,2789616,GRCh38,GM18498,cell line,"Stephen Montgomery, Stanford",ENCSR924SDV,https://www.encodeproject.org/files/ENCFF594MCK/@@download/ENCFF594MCK.bed.gz,GM18498_1
ENCFF876LVG,2110874,GRCh38,GM21515,cell line,"Stephen Montgomery, Stanford",ENCSR960KGO,https://www.encodeproject.org/files/ENCFF876LVG/@@download/ENCFF876LVG.bed.gz,GM21515_1
ENCFF176CKB,2959345,GRCh38,GM19351,cell line,"Stephen Montgomery, Stanford",ENCSR231BZU,https://www.encodeproject.org/files/ENCFF176CKB/@@download/ENCFF176CKB.bed.gz,GM19351_1
ENCFF246XGY,1839495,GRCh38,HG03571,cell line,"Stephen Montgomery, Stanford",ENCSR000RBT,https://www.encodeproject.org/files/ENCFF246XGY/@@download/ENCFF246XGY.bed.gz,HG03571_1
ENCFF209RWV,2445968,GRCh38,GM21390,cell line,"Stephen Montgomery, Stanford",ENCSR024WOD,https://www.encodeproject.org/files/ENCFF209RWV/@@download/ENCFF209RWV.bed.gz,GM21390_1
ENCFF816FSQ,3090605,GRCh38,HG03354,cell line,"Stephen Montgomery, Stanford",ENCSR025MEO,https://www.encodeproject.org/files/ENCFF816FSQ/@@download/ENCFF816FSQ.bed.gz,HG03354_1
ENCFF367ROZ,2058184,GRCh38,HG02973,cell line,"Stephen Montgomery, Stanford",ENCSR038WVV,https://www.encodeproject.org/files/ENCFF367ROZ/@@download/ENCFF367ROZ.bed.gz,HG02973_1
ENCFF762FXN,2037394,GRCh38,HG03135,cell line,"Stephen Montgomery, Stanford",ENCSR096VEO,https://www.encodeproject.org/files/ENCFF762FXN/@@download/ENCFF762FXN.bed.gz,HG03135_1
ENCFF697QXK,2806299,GRCh38,HG02759,cell line,"Stephen Montgomery, Stanford",ENCSR144ZXR,https://www.encodeproject.org/files/ENCFF697QXK/@@download/ENCFF697QXK.bed.gz,HG02759_1
ENCFF262DJL,2239403,GRCh38,HG02981,cell line,"Stephen Montgomery, Stanford",ENCSR208HAP,https://www.encodeproject.org/files/ENCFF262DJL/@@download/ENCFF262DJL.bed.gz,HG02981_1
ENCFF674XMD,2391794,GRCh38,GM21619,cell line,"Stephen Montgomery, Stanford",ENCSR214EIV,https://www.encodeproject.org/files/ENCFF674XMD/@@download/ENCFF674XMD.bed.gz,GM21619_1
ENCFF695HXA,3271720,GRCh38,GM18511,cell line,"Stephen Montgomery, Stanford",ENCSR253QLW,https://www.encodeproject.org/files/ENCFF695HXA/@@download/ENCFF695HXA.bed.gz,GM18511_1
ENCFF305ZQQ,1114618,GRCh38,HG03558,cell line,"Stephen Montgomery, Stanford",ENCSR278JWM,https://www.encodeproject.org/files/ENCFF305ZQQ/@@download/ENCFF305ZQQ.bed.gz,HG03558_1
ENCFF044HAG,1840286,GRCh38,GM21737,cell line,"Stephen Montgomery, Stanford",ENCSR315QWI,https://www.encodeproject.org/files/ENCFF044HAG/@@download/ENCFF044HAG.bed.gz,GM21737_1
ENCFF085DFN,1272845,GRCh38,GM18508,cell line,"Stephen Montgomery, Stanford",ENCSR357WQH,https://www.encodeproject.org/files/ENCFF085DFN/@@download/ENCFF085DFN.bed.gz,GM18508_1
ENCFF209BNR,2928381,GRCh38,GM19395,cell line,"Stephen Montgomery, Stanford",ENCSR372IGW,https://www.encodeproject.org/files/ENCFF209BNR/@@download/ENCFF209BNR.bed.gz,GM19395_1
ENCFF390QUM,1703820,GRCh38,HG03064,cell line,"Stephen Montgomery, Stanford",ENCSR487SOP,https://www.encodeproject.org/files/ENCFF390QUM/@@download/ENCFF390QUM.bed.gz,HG03064_1
ENCFF766BZE,2150672,GRCh38,GM21381,cell line,"Stephen Montgomery, Stanford",ENCSR512YXO,https://www.encodeproject.org/files/ENCFF766BZE/@@download/ENCFF766BZE.bed.gz,GM21381_1
ENCFF508EYC,2429604,GRCh38,HG03342,cell line,"Stephen Montgomery, Stanford",ENCSR528YDD,https://www.encodeproject.org/files/ENCFF508EYC/@@download/ENCFF508EYC.bed.gz,HG03342_1
ENCFF761RRO,1726559,GRCh38,HG03139,cell line,"Stephen Montgomery, Stanford",ENCSR615YIL,https://www.encodeproject.org/files/ENCFF761RRO/@@download/ENCFF761RRO.bed.gz,HG03139_1
ENCFF093GBP,1823952,GRCh38,GM19467,cell line,"Stephen Montgomery, Stanford",ENCSR635CAC,https://www.encodeproject.org/files/ENCFF093GBP/@@download/ENCFF093GBP.bed.gz,GM19467_1
ENCFF926ULQ,1989589,GRCh38,GM19455,cell line,"Stephen Montgomery, Stanford",ENCSR706GDQ,https://www.encodeproject.org/files/ENCFF926ULQ/@@download/ENCFF926ULQ.bed.gz,GM19455_1
ENCFF756WUH,1889889,GRCh38,HG02623,cell line,"Stephen Montgomery, Stanford",ENCSR821JHD,https://www.encodeproject.org/files/ENCFF756WUH/@@download/ENCFF756WUH.bed.gz,HG02623_1
ENCFF437HEK,2551734,GRCh38,HG02642,cell line,"Stephen Montgomery, Stanford",ENCSR821QHC,https://www.encodeproject.org/files/ENCFF437HEK/@@download/ENCFF437HEK.bed.gz,HG02642_1
ENCFF744XPE,2241588,GRCh38,HG03378,cell line,"Stephen Montgomery, Stanford",ENCSR824DUE,https://www.encodeproject.org/files/ENCFF744XPE/@@download/ENCFF744XPE.bed.gz,HG03378_1
ENCFF471QJG,1806009,GRCh38,HG03103,cell line,"Stephen Montgomery, Stanford",ENCSR833ACP,https://www.encodeproject.org/files/ENCFF471QJG/@@download/ENCFF471QJG.bed.gz,HG03103_1
ENCFF948GVD,1905066,GRCh38,HG03565,cell line,"Stephen Montgomery, Stanford",ENCSR835WBW,https://www.encodeproject.org/files/ENCFF948GVD/@@download/ENCFF948GVD.bed.gz,HG03565_1
ENCFF340FLI,1552775,GRCh38,HG02840,cell line,"Stephen Montgomery, Stanford",ENCSR854ZBA,https://www.encodeproject.org/files/ENCFF340FLI/@@download/ENCFF340FLI.bed.gz,HG02840_1
ENCFF624KRL,2689823,GRCh38,HG02610,cell line,"Stephen Montgomery, Stanford",ENCSR905PWV,https://www.encodeproject.org/files/ENCFF624KRL/@@download/ENCFF624KRL.bed.gz,HG02610_1
ENCFF344XDS,2771867,GRCh38,GM18909,cell line,"Stephen Montgomery, Stanford",ENCSR917WJS,https://www.encodeproject.org/files/ENCFF344XDS/@@download/ENCFF344XDS.bed.gz,GM18909_1
ENCFF339RRA,2183073,GRCh38,HG02938,cell line,"Stephen Montgomery, Stanford",ENCSR920FQJ,https://www.encodeproject.org/files/ENCFF339RRA/@@download/ENCFF339RRA.bed.gz,HG02938_1
ENCFF411CDL,2133921,GRCh38,GM21528,cell line,"Stephen Montgomery, Stanford",ENCSR939EVW,https://www.encodeproject.org/files/ENCFF411CDL/@@download/ENCFF411CDL.bed.gz,GM21528_1
ENCFF733DJY,2092219,GRCh38,GM18505,cell line,"Stephen Montgomery, Stanford",ENCSR084FHK,https://www.encodeproject.org/files/ENCFF733DJY/@@download/ENCFF733DJY.bed.gz,GM18505_1
ENCFF384IUM,2136890,GRCh38,GM21576,cell line,"Stephen Montgomery, Stanford",ENCSR122NDR,https://www.encodeproject.org/files/ENCFF384IUM/@@download/ENCFF384IUM.bed.gz,GM21576_1
ENCFF132ATE,2516419,GRCh38,HG02852,cell line,"Stephen Montgomery, Stanford",ENCSR368FYV,https://www.encodeproject.org/files/ENCFF132ATE/@@download/ENCFF132ATE.bed.gz,HG02852_1
ENCFF965AOW,1575526,GRCh38,GM19328,cell line,"Stephen Montgomery, Stanford",ENCSR383RDZ,https://www.encodeproject.org/files/ENCFF965AOW/@@download/ENCFF965AOW.bed.gz,GM19328_1
ENCFF630QOE,1779470,GRCh38,HG02571,cell line,"Stephen Montgomery, Stanford",ENCSR400ISH,https://www.encodeproject.org/files/ENCFF630QOE/@@download/ENCFF630QOE.bed.gz,HG02571_1
ENCFF715VOA,2762488,GRCh38,HG02798,cell line,"Stephen Montgomery, Stanford",ENCSR524CPZ,https://www.encodeproject.org/files/ENCFF715VOA/@@download/ENCFF715VOA.bed.gz,HG02798_1
ENCFF063AGI,2070902,GRCh38,HG03280,cell line,"Stephen Montgomery, Stanford",ENCSR554WGQ,https://www.encodeproject.org/files/ENCFF063AGI/@@download/ENCFF063AGI.bed.gz,HG03280_1
ENCFF007FSO,1999454,GRCh38,GM18870,cell line,"Stephen Montgomery, Stanford",ENCSR684KUG,https://www.encodeproject.org/files/ENCFF007FSO/@@download/ENCFF007FSO.bed.gz,GM18870_1
ENCFF489VFL,2412786,GRCh38,HG03159,cell line,"Stephen Montgomery, Stanford",ENCSR940YDN,https://www.encodeproject.org/files/ENCFF489VFL/@@download/ENCFF489VFL.bed.gz,HG03159_1
ENCFF268EVB,1849129,GRCh38,HG03108,cell line,"Stephen Montgomery, Stanford",ENCSR962ITF,https://www.encodeproject.org/files/ENCFF268EVB/@@download/ENCFF268EVB.bed.gz,HG03108_1
ENCFF785YXH,2059174,GRCh38,GM21360,cell line,"Stephen Montgomery, Stanford",ENCSR105SCQ,https://www.encodeproject.org/files/ENCFF785YXH/@@download/ENCFF785YXH.bed.gz,GM21360_1
ENCFF796GGQ,1894090,GRCh38,HG03045,cell line,"Stephen Montgomery, Stanford",ENCSR249FXU,https://www.encodeproject.org/files/ENCFF796GGQ/@@download/ENCFF796GGQ.bed.gz,HG03045_1
ENCFF937GVS,2590883,GRCh38,HG03175,cell line,"Stephen Montgomery, Stanford",ENCSR305XRF,https://www.encodeproject.org/files/ENCFF937GVS/@@download/ENCFF937GVS.bed.gz,HG03175_1
ENCFF107CMG,1966323,GRCh38,HG03439,cell line,"Stephen Montgomery, Stanford",ENCSR376YMU,https://www.encodeproject.org/files/ENCFF107CMG/@@download/ENCFF107CMG.bed.gz,HG03439_1
ENCFF916HWL,1671697,GRCh38,HG03039,cell line,"Stephen Montgomery, Stanford",ENCSR381LJX,https://www.encodeproject.org/files/ENCFF916HWL/@@download/ENCFF916HWL.bed.gz,HG03039_1
ENCFF464KLS,1867216,GRCh38,HG02763,cell line,"Stephen Montgomery, Stanford",ENCSR404OEO,https://www.encodeproject.org/files/ENCFF464KLS/@@download/ENCFF464KLS.bed.gz,HG02763_1
ENCFF966KNT,2986178,GRCh38,GM18907,cell line,"Stephen Montgomery, Stanford",ENCSR487QSB,https://www.encodeproject.org/files/ENCFF966KNT/@@download/ENCFF966KNT.bed.gz,GM18907_1
ENCFF453BWQ,1630705,GRCh38,HG03025,cell line,"Stephen Montgomery, Stanford",ENCSR673ZMQ,https://www.encodeproject.org/files/ENCFF453BWQ/@@download/ENCFF453BWQ.bed.gz,HG03025_1
ENCFF728CSD,2570886,GRCh38,GM19463,cell line,"Stephen Montgomery, Stanford",ENCSR712ZRY,https://www.encodeproject.org/files/ENCFF728CSD/@@download/ENCFF728CSD.bed.gz,GM19463_1
ENCFF597KPI,1975464,GRCh38,GM19397,cell line,"Stephen Montgomery, Stanford",ENCSR839IAD,https://www.encodeproject.org/files/ENCFF597KPI/@@download/ENCFF597KPI.bed.gz,GM19397_1
ENCFF020RRO,3670431,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR065CER,https://www.encodeproject.org/files/ENCFF020RRO/@@download/ENCFF020RRO.bed.gz,motor_neuron_1
ENCFF098WTO,2972677,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR065CER,https://www.encodeproject.org/files/ENCFF098WTO/@@download/ENCFF098WTO.bed.gz,motor_neuron_2
ENCFF500PEM,3096557,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR065CER,https://www.encodeproject.org/files/ENCFF500PEM/@@download/ENCFF500PEM.bed.gz,motor_neuron_3
ENCFF770MIQ,3656050,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR131HOY,https://www.encodeproject.org/files/ENCFF770MIQ/@@download/ENCFF770MIQ.bed.gz,motor_neuron_4
ENCFF571HHD,3091509,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR131HOY,https://www.encodeproject.org/files/ENCFF571HHD/@@download/ENCFF571HHD.bed.gz,motor_neuron_5
ENCFF876NFZ,2930955,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR131HOY,https://www.encodeproject.org/files/ENCFF876NFZ/@@download/ENCFF876NFZ.bed.gz,motor_neuron_6
ENCFF897LOL,4862015,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR459PVP,https://www.encodeproject.org/files/ENCFF897LOL/@@download/ENCFF897LOL.bed.gz,motor_neuron_7
ENCFF901TRI,4552045,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR459PVP,https://www.encodeproject.org/files/ENCFF901TRI/@@download/ENCFF901TRI.bed.gz,motor_neuron_8
ENCFF130ZFF,4420318,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR459PVP,https://www.encodeproject.org/files/ENCFF130ZFF/@@download/ENCFF130ZFF.bed.gz,motor_neuron_9
ENCFF032UMX,3371775,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR516YAD,https://www.encodeproject.org/files/ENCFF032UMX/@@download/ENCFF032UMX.bed.gz,motor_neuron_10
ENCFF193PGD,2765117,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR516YAD,https://www.encodeproject.org/files/ENCFF193PGD/@@download/ENCFF193PGD.bed.gz,motor_neuron_11
ENCFF728QWH,2782074,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR516YAD,https://www.encodeproject.org/files/ENCFF728QWH/@@download/ENCFF728QWH.bed.gz,motor_neuron_12
ENCFF104QTD,3776928,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR634WYX,https://www.encodeproject.org/files/ENCFF104QTD/@@download/ENCFF104QTD.bed.gz,motor_neuron_13
ENCFF869TLY,3207511,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR634WYX,https://www.encodeproject.org/files/ENCFF869TLY/@@download/ENCFF869TLY.bed.gz,motor_neuron_14
ENCFF275SWZ,3203371,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR634WYX,https://www.encodeproject.org/files/ENCFF275SWZ/@@download/ENCFF275SWZ.bed.gz,motor_neuron_15
ENCFF773SBW,4006165,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR704VZY,https://www.encodeproject.org/files/ENCFF773SBW/@@download/ENCFF773SBW.bed.gz,motor_neuron_16
ENCFF371PXK,3438334,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR704VZY,https://www.encodeproject.org/files/ENCFF371PXK/@@download/ENCFF371PXK.bed.gz,motor_neuron_17
ENCFF376USQ,3372821,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR704VZY,https://www.encodeproject.org/files/ENCFF376USQ/@@download/ENCFF376USQ.bed.gz,motor_neuron_18
ENCFF828BIU,3516331,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR709QRD,https://www.encodeproject.org/files/ENCFF828BIU/@@download/ENCFF828BIU.bed.gz,motor_neuron_19
ENCFF592LGY,2983715,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR709QRD,https://www.encodeproject.org/files/ENCFF592LGY/@@download/ENCFF592LGY.bed.gz,motor_neuron_20
ENCFF002RUK,2684238,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR709QRD,https://www.encodeproject.org/files/ENCFF002RUK/@@download/ENCFF002RUK.bed.gz,motor_neuron_21
ENCFF432VON,4489684,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR812ZKP,https://www.encodeproject.org/files/ENCFF432VON/@@download/ENCFF432VON.bed.gz,motor_neuron_22
ENCFF903JJQ,4071134,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR812ZKP,https://www.encodeproject.org/files/ENCFF903JJQ/@@download/ENCFF903JJQ.bed.gz,motor_neuron_23
ENCFF337ECR,4032090,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR812ZKP,https://www.encodeproject.org/files/ENCFF337ECR/@@download/ENCFF337ECR.bed.gz,motor_neuron_24
ENCFF542CXX,4300008,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR913OWV,https://www.encodeproject.org/files/ENCFF542CXX/@@download/ENCFF542CXX.bed.gz,motor_neuron_25
ENCFF671QMH,3916879,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR913OWV,https://www.encodeproject.org/files/ENCFF671QMH/@@download/ENCFF671QMH.bed.gz,motor_neuron_26
ENCFF488VAX,3879707,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR913OWV,https://www.encodeproject.org/files/ENCFF488VAX/@@download/ENCFF488VAX.bed.gz,motor_neuron_27
ENCFF794FOV,4875549,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR410DWV,https://www.encodeproject.org/files/ENCFF794FOV/@@download/ENCFF794FOV.bed.gz,motor_neuron_28
ENCFF133NSM,4460647,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR410DWV,https://www.encodeproject.org/files/ENCFF133NSM/@@download/ENCFF133NSM.bed.gz,motor_neuron_29
ENCFF273ZSS,4489002,GRCh38,motor neuron,in vitro differentiated cells,"Michael Snyder, Stanford",ENCSR410DWV,https://www.encodeproject.org/files/ENCFF273ZSS/@@download/ENCFF273ZSS.bed.gz,motor_neuron_30
ENCFF753XQZ,2623835,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR074AHH,https://www.encodeproject.org/files/ENCFF753XQZ/@@download/ENCFF753XQZ.bed.gz,A549_7
ENCFF894TJN,2658427,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR074AHH,https://www.encodeproject.org/files/ENCFF894TJN/@@download/ENCFF894TJN.bed.gz,A549_8
ENCFF812CUZ,2988196,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR074AHH,https://www.encodeproject.org/files/ENCFF812CUZ/@@download/ENCFF812CUZ.bed.gz,A549_9
ENCFF435WAN,1798310,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR074AHH,https://www.encodeproject.org/files/ENCFF435WAN/@@download/ENCFF435WAN.bed.gz,A549_10
ENCFF548DNV,1788162,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR074AHH,https://www.encodeproject.org/files/ENCFF548DNV/@@download/ENCFF548DNV.bed.gz,A549_11
ENCFF020EUE,1419042,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR074AHH,https://www.encodeproject.org/files/ENCFF020EUE/@@download/ENCFF020EUE.bed.gz,A549_12
ENCFF143XED,2733406,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR139OYS,https://www.encodeproject.org/files/ENCFF143XED/@@download/ENCFF143XED.bed.gz,A549_13
ENCFF804MVZ,2709939,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR139OYS,https://www.encodeproject.org/files/ENCFF804MVZ/@@download/ENCFF804MVZ.bed.gz,A549_14
ENCFF663LWI,3074226,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR139OYS,https://www.encodeproject.org/files/ENCFF663LWI/@@download/ENCFF663LWI.bed.gz,A549_15
ENCFF022BIK,2009256,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR139OYS,https://www.encodeproject.org/files/ENCFF022BIK/@@download/ENCFF022BIK.bed.gz,A549_16
ENCFF036AMC,1829215,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR139OYS,https://www.encodeproject.org/files/ENCFF036AMC/@@download/ENCFF036AMC.bed.gz,A549_17
ENCFF422RQI,1469955,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR139OYS,https://www.encodeproject.org/files/ENCFF422RQI/@@download/ENCFF422RQI.bed.gz,A549_18
ENCFF172ZTK,2582439,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR220ASC,https://www.encodeproject.org/files/ENCFF172ZTK/@@download/ENCFF172ZTK.bed.gz,A549_19
ENCFF516KWQ,2543336,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR220ASC,https://www.encodeproject.org/files/ENCFF516KWQ/@@download/ENCFF516KWQ.bed.gz,A549_20
ENCFF795ASF,2895977,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR220ASC,https://www.encodeproject.org/files/ENCFF795ASF/@@download/ENCFF795ASF.bed.gz,A549_21
ENCFF110LOX,1734488,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR220ASC,https://www.encodeproject.org/files/ENCFF110LOX/@@download/ENCFF110LOX.bed.gz,A549_22
ENCFF463DJH,1814533,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR220ASC,https://www.encodeproject.org/files/ENCFF463DJH/@@download/ENCFF463DJH.bed.gz,A549_23
ENCFF181FFW,1455241,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR220ASC,https://www.encodeproject.org/files/ENCFF181FFW/@@download/ENCFF181FFW.bed.gz,A549_24
ENCFF117JPF,2474974,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR265ZXX,https://www.encodeproject.org/files/ENCFF117JPF/@@download/ENCFF117JPF.bed.gz,A549_25
ENCFF197CSK,2443955,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR265ZXX,https://www.encodeproject.org/files/ENCFF197CSK/@@download/ENCFF197CSK.bed.gz,A549_26
ENCFF886VWX,2737939,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR265ZXX,https://www.encodeproject.org/files/ENCFF886VWX/@@download/ENCFF886VWX.bed.gz,A549_27
ENCFF771OGQ,1792254,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR265ZXX,https://www.encodeproject.org/files/ENCFF771OGQ/@@download/ENCFF771OGQ.bed.gz,A549_28
ENCFF965ILK,1524721,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR265ZXX,https://www.encodeproject.org/files/ENCFF965ILK/@@download/ENCFF965ILK.bed.gz,A549_29
ENCFF848NZM,1437314,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR265ZXX,https://www.encodeproject.org/files/ENCFF848NZM/@@download/ENCFF848NZM.bed.gz,A549_30
ENCFF619XXH,2402900,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR288YMH,https://www.encodeproject.org/files/ENCFF619XXH/@@download/ENCFF619XXH.bed.gz,A549_31
ENCFF525YKE,2380920,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR288YMH,https://www.encodeproject.org/files/ENCFF525YKE/@@download/ENCFF525YKE.bed.gz,A549_32
ENCFF857ZHU,2810525,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR288YMH,https://www.encodeproject.org/files/ENCFF857ZHU/@@download/ENCFF857ZHU.bed.gz,A549_33
ENCFF018OJP,1829832,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR288YMH,https://www.encodeproject.org/files/ENCFF018OJP/@@download/ENCFF018OJP.bed.gz,A549_34
ENCFF754DCB,1651286,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR288YMH,https://www.encodeproject.org/files/ENCFF754DCB/@@download/ENCFF754DCB.bed.gz,A549_35
ENCFF813DSX,1304775,GRCh38,A549,cell line,"Tim Reddy, Duke",ENCSR288YMH,https://www.encodeproject.org/files/ENCFF813DSX/@@download/ENCFF813DSX.bed.gz,A549_36
ENCFF252SWZ,1668011,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR091YVK,https://www.encodeproject.org/files/ENCFF252SWZ/@@download/ENCFF252SWZ.bed.gz,K562_16
ENCFF409ZYN,1184510,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR091YVK,https://www.encodeproject.org/files/ENCFF409ZYN/@@download/ENCFF409ZYN.bed.gz,K562_17
ENCFF403TTG,1152785,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR091YVK,https://www.encodeproject.org/files/ENCFF403TTG/@@download/ENCFF403TTG.bed.gz,K562_18
ENCFF181KMW,967543,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR199HSR,https://www.encodeproject.org/files/ENCFF181KMW/@@download/ENCFF181KMW.bed.gz,K562_19
ENCFF191MTX,603158,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR199HSR,https://www.encodeproject.org/files/ENCFF191MTX/@@download/ENCFF191MTX.bed.gz,K562_20
ENCFF775YZO,583957,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR199HSR,https://www.encodeproject.org/files/ENCFF775YZO/@@download/ENCFF775YZO.bed.gz,K562_21
ENCFF694AWW,888372,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR281LEY,https://www.encodeproject.org/files/ENCFF694AWW/@@download/ENCFF694AWW.bed.gz,K562_22
ENCFF342CXD,422513,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR281LEY,https://www.encodeproject.org/files/ENCFF342CXD/@@download/ENCFF342CXD.bed.gz,K562_23
ENCFF366RUC,607896,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR281LEY,https://www.encodeproject.org/files/ENCFF366RUC/@@download/ENCFF366RUC.bed.gz,K562_24
ENCFF704CKA,1054140,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR314EDO,https://www.encodeproject.org/files/ENCFF704CKA/@@download/ENCFF704CKA.bed.gz,K562_25
ENCFF634JMH,649210,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR314EDO,https://www.encodeproject.org/files/ENCFF634JMH/@@download/ENCFF634JMH.bed.gz,K562_26
ENCFF965KWA,630953,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR314EDO,https://www.encodeproject.org/files/ENCFF965KWA/@@download/ENCFF965KWA.bed.gz,K562_27
ENCFF044RHR,966488,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR342GFK,https://www.encodeproject.org/files/ENCFF044RHR/@@download/ENCFF044RHR.bed.gz,K562_28
ENCFF174RPR,694557,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR342GFK,https://www.encodeproject.org/files/ENCFF174RPR/@@download/ENCFF174RPR.bed.gz,K562_29
ENCFF779NJC,414215,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR342GFK,https://www.encodeproject.org/files/ENCFF779NJC/@@download/ENCFF779NJC.bed.gz,K562_30
ENCFF090DLI,464111,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR390UVH,https://www.encodeproject.org/files/ENCFF090DLI/@@download/ENCFF090DLI.bed.gz,K562_31
ENCFF172TXF,338863,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR390UVH,https://www.encodeproject.org/files/ENCFF172TXF/@@download/ENCFF172TXF.bed.gz,K562_32
ENCFF155RFT,444504,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR390UVH,https://www.encodeproject.org/files/ENCFF155RFT/@@download/ENCFF155RFT.bed.gz,K562_33
ENCFF973ZSJ,708077,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR420NIU,https://www.encodeproject.org/files/ENCFF973ZSJ/@@download/ENCFF973ZSJ.bed.gz,K562_34
ENCFF602STW,444667,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR420NIU,https://www.encodeproject.org/files/ENCFF602STW/@@download/ENCFF602STW.bed.gz,K562_35
ENCFF247FDQ,393386,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR420NIU,https://www.encodeproject.org/files/ENCFF247FDQ/@@download/ENCFF247FDQ.bed.gz,K562_36
ENCFF619ETM,1116757,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR446UJL,https://www.encodeproject.org/files/ENCFF619ETM/@@download/ENCFF619ETM.bed.gz,K562_37
ENCFF322ESX,795292,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR446UJL,https://www.encodeproject.org/files/ENCFF322ESX/@@download/ENCFF322ESX.bed.gz,K562_38
ENCFF755BER,884206,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR446UJL,https://www.encodeproject.org/files/ENCFF755BER/@@download/ENCFF755BER.bed.gz,K562_39
ENCFF235RHR,1091874,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR465IDZ,https://www.encodeproject.org/files/ENCFF235RHR/@@download/ENCFF235RHR.bed.gz,K562_40
ENCFF922KCR,700573,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR465IDZ,https://www.encodeproject.org/files/ENCFF922KCR/@@download/ENCFF922KCR.bed.gz,K562_41
ENCFF758CDY,761597,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR465IDZ,https://www.encodeproject.org/files/ENCFF758CDY/@@download/ENCFF758CDY.bed.gz,K562_42
ENCFF184BBI,393670,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR508LLL,https://www.encodeproject.org/files/ENCFF184BBI/@@download/ENCFF184BBI.bed.gz,K562_43
ENCFF450ALM,340482,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR508LLL,https://www.encodeproject.org/files/ENCFF450ALM/@@download/ENCFF450ALM.bed.gz,K562_44
ENCFF823SKP,271525,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR508LLL,https://www.encodeproject.org/files/ENCFF823SKP/@@download/ENCFF823SKP.bed.gz,K562_45
ENCFF530SMZ,1273436,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR509MYW,https://www.encodeproject.org/files/ENCFF530SMZ/@@download/ENCFF530SMZ.bed.gz,K562_46
ENCFF008RND,929770,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR509MYW,https://www.encodeproject.org/files/ENCFF008RND/@@download/ENCFF008RND.bed.gz,K562_47
ENCFF806GBO,624434,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR509MYW,https://www.encodeproject.org/files/ENCFF806GBO/@@download/ENCFF806GBO.bed.gz,K562_48
ENCFF787CLI,1606276,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR522ALT,https://www.encodeproject.org/files/ENCFF787CLI/@@download/ENCFF787CLI.bed.gz,K562_49
ENCFF096EAF,1211704,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR522ALT,https://www.encodeproject.org/files/ENCFF096EAF/@@download/ENCFF096EAF.bed.gz,K562_50
ENCFF768SES,1073587,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR522ALT,https://www.encodeproject.org/files/ENCFF768SES/@@download/ENCFF768SES.bed.gz,K562_51
ENCFF384DKM,621129,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR524ZSN,https://www.encodeproject.org/files/ENCFF384DKM/@@download/ENCFF384DKM.bed.gz,K562_52
ENCFF804NWJ,408556,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR524ZSN,https://www.encodeproject.org/files/ENCFF804NWJ/@@download/ENCFF804NWJ.bed.gz,K562_53
ENCFF627HRL,268834,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR524ZSN,https://www.encodeproject.org/files/ENCFF627HRL/@@download/ENCFF627HRL.bed.gz,K562_54
ENCFF296VSD,667760,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR548GGF,https://www.encodeproject.org/files/ENCFF296VSD/@@download/ENCFF296VSD.bed.gz,K562_55
ENCFF718IDT,563559,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR548GGF,https://www.encodeproject.org/files/ENCFF718IDT/@@download/ENCFF718IDT.bed.gz,K562_56
ENCFF111EPS,52601,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR548GGF,https://www.encodeproject.org/files/ENCFF111EPS/@@download/ENCFF111EPS.bed.gz,K562_57
ENCFF375KRS,1567328,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR582NPJ,https://www.encodeproject.org/files/ENCFF375KRS/@@download/ENCFF375KRS.bed.gz,K562_58
ENCFF446TVN,1252194,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR582NPJ,https://www.encodeproject.org/files/ENCFF446TVN/@@download/ENCFF446TVN.bed.gz,K562_59
ENCFF631UCA,890456,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR582NPJ,https://www.encodeproject.org/files/ENCFF631UCA/@@download/ENCFF631UCA.bed.gz,K562_60
ENCFF784VWL,599276,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR632VDU,https://www.encodeproject.org/files/ENCFF784VWL/@@download/ENCFF784VWL.bed.gz,K562_61
ENCFF978MZO,495674,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR632VDU,https://www.encodeproject.org/files/ENCFF978MZO/@@download/ENCFF978MZO.bed.gz,K562_62
ENCFF981QBY,437310,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR632VDU,https://www.encodeproject.org/files/ENCFF981QBY/@@download/ENCFF981QBY.bed.gz,K562_63
ENCFF251MIK,1060650,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR644KPP,https://www.encodeproject.org/files/ENCFF251MIK/@@download/ENCFF251MIK.bed.gz,K562_64
ENCFF215IBV,405267,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR644KPP,https://www.encodeproject.org/files/ENCFF215IBV/@@download/ENCFF215IBV.bed.gz,K562_65
ENCFF147WQH,720722,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR644KPP,https://www.encodeproject.org/files/ENCFF147WQH/@@download/ENCFF147WQH.bed.gz,K562_66
ENCFF193GTQ,710713,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR685YMG,https://www.encodeproject.org/files/ENCFF193GTQ/@@download/ENCFF193GTQ.bed.gz,K562_67
ENCFF130LTN,588635,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR685YMG,https://www.encodeproject.org/files/ENCFF130LTN/@@download/ENCFF130LTN.bed.gz,K562_68
ENCFF180APM,574892,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR685YMG,https://www.encodeproject.org/files/ENCFF180APM/@@download/ENCFF180APM.bed.gz,K562_69
ENCFF161UIY,971190,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR745KUZ,https://www.encodeproject.org/files/ENCFF161UIY/@@download/ENCFF161UIY.bed.gz,K562_70
ENCFF184TXL,316088,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR745KUZ,https://www.encodeproject.org/files/ENCFF184TXL/@@download/ENCFF184TXL.bed.gz,K562_71
ENCFF955YHU,837985,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR745KUZ,https://www.encodeproject.org/files/ENCFF955YHU/@@download/ENCFF955YHU.bed.gz,K562_72
ENCFF007GNW,1321687,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR793YFK,https://www.encodeproject.org/files/ENCFF007GNW/@@download/ENCFF007GNW.bed.gz,K562_73
ENCFF826MZH,1003095,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR793YFK,https://www.encodeproject.org/files/ENCFF826MZH/@@download/ENCFF826MZH.bed.gz,K562_74
ENCFF946QVT,808618,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR793YFK,https://www.encodeproject.org/files/ENCFF946QVT/@@download/ENCFF946QVT.bed.gz,K562_75
ENCFF547JMC,2063617,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR800RAH,https://www.encodeproject.org/files/ENCFF547JMC/@@download/ENCFF547JMC.bed.gz,K562_76
ENCFF456TIE,1425580,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR800RAH,https://www.encodeproject.org/files/ENCFF456TIE/@@download/ENCFF456TIE.bed.gz,K562_77
ENCFF710PWT,1402907,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR800RAH,https://www.encodeproject.org/files/ENCFF710PWT/@@download/ENCFF710PWT.bed.gz,K562_78
ENCFF107YLS,1191126,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR934FTO,https://www.encodeproject.org/files/ENCFF107YLS/@@download/ENCFF107YLS.bed.gz,K562_79
ENCFF244JPT,1023643,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR934FTO,https://www.encodeproject.org/files/ENCFF244JPT/@@download/ENCFF244JPT.bed.gz,K562_80
ENCFF776JZA,574133,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR934FTO,https://www.encodeproject.org/files/ENCFF776JZA/@@download/ENCFF776JZA.bed.gz,K562_81
ENCFF701WXB,1049585,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR935JVI,https://www.encodeproject.org/files/ENCFF701WXB/@@download/ENCFF701WXB.bed.gz,K562_82
ENCFF341YYG,446319,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR935JVI,https://www.encodeproject.org/files/ENCFF341YYG/@@download/ENCFF341YYG.bed.gz,K562_83
ENCFF684GDX,529099,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR935JVI,https://www.encodeproject.org/files/ENCFF684GDX/@@download/ENCFF684GDX.bed.gz,K562_84
ENCFF194REL,826646,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR974QYY,https://www.encodeproject.org/files/ENCFF194REL/@@download/ENCFF194REL.bed.gz,K562_85
ENCFF366XOR,425456,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR974QYY,https://www.encodeproject.org/files/ENCFF366XOR/@@download/ENCFF366XOR.bed.gz,K562_86
ENCFF064ACX,586179,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR974QYY,https://www.encodeproject.org/files/ENCFF064ACX/@@download/ENCFF064ACX.bed.gz,K562_87
ENCFF528GLY,807083,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR096HVF,https://www.encodeproject.org/files/ENCFF528GLY/@@download/ENCFF528GLY.bed.gz,K562_88
ENCFF089VSS,601185,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR096HVF,https://www.encodeproject.org/files/ENCFF089VSS/@@download/ENCFF089VSS.bed.gz,K562_89
ENCFF326WUN,194834,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR096HVF,https://www.encodeproject.org/files/ENCFF326WUN/@@download/ENCFF326WUN.bed.gz,K562_90
ENCFF381YTN,1009721,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR121GEL,https://www.encodeproject.org/files/ENCFF381YTN/@@download/ENCFF381YTN.bed.gz,K562_91
ENCFF490ESL,591665,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR121GEL,https://www.encodeproject.org/files/ENCFF490ESL/@@download/ENCFF490ESL.bed.gz,K562_92
ENCFF957JPV,522269,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR121GEL,https://www.encodeproject.org/files/ENCFF957JPV/@@download/ENCFF957JPV.bed.gz,K562_93
ENCFF579ZRL,863877,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR163VQD,https://www.encodeproject.org/files/ENCFF579ZRL/@@download/ENCFF579ZRL.bed.gz,K562_94
ENCFF606HOD,554576,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR163VQD,https://www.encodeproject.org/files/ENCFF606HOD/@@download/ENCFF606HOD.bed.gz,K562_95
ENCFF215LTC,593133,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR163VQD,https://www.encodeproject.org/files/ENCFF215LTC/@@download/ENCFF215LTC.bed.gz,K562_96
ENCFF414WGI,1437564,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR165JXS,https://www.encodeproject.org/files/ENCFF414WGI/@@download/ENCFF414WGI.bed.gz,K562_97
ENCFF860GXG,439029,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR165JXS,https://www.encodeproject.org/files/ENCFF860GXG/@@download/ENCFF860GXG.bed.gz,K562_98
ENCFF309RUE,1119096,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR165JXS,https://www.encodeproject.org/files/ENCFF309RUE/@@download/ENCFF309RUE.bed.gz,K562_99
ENCFF991DKL,734970,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR179IAC,https://www.encodeproject.org/files/ENCFF991DKL/@@download/ENCFF991DKL.bed.gz,K562_100
ENCFF430MTT,204339,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR179IAC,https://www.encodeproject.org/files/ENCFF430MTT/@@download/ENCFF430MTT.bed.gz,K562_101
ENCFF202UJQ,460138,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR179IAC,https://www.encodeproject.org/files/ENCFF202UJQ/@@download/ENCFF202UJQ.bed.gz,K562_102
ENCFF989SWN,945697,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR197GOQ,https://www.encodeproject.org/files/ENCFF989SWN/@@download/ENCFF989SWN.bed.gz,K562_103
ENCFF486YVX,526075,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR197GOQ,https://www.encodeproject.org/files/ENCFF486YVX/@@download/ENCFF486YVX.bed.gz,K562_104
ENCFF742PXT,356865,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR197GOQ,https://www.encodeproject.org/files/ENCFF742PXT/@@download/ENCFF742PXT.bed.gz,K562_105
ENCFF139RFI,1032579,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR214QZO,https://www.encodeproject.org/files/ENCFF139RFI/@@download/ENCFF139RFI.bed.gz,K562_106
ENCFF287WSJ,449576,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR214QZO,https://www.encodeproject.org/files/ENCFF287WSJ/@@download/ENCFF287WSJ.bed.gz,K562_107
ENCFF781EYW,609755,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR214QZO,https://www.encodeproject.org/files/ENCFF781EYW/@@download/ENCFF781EYW.bed.gz,K562_108
ENCFF971CUZ,513527,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR217QAB,https://www.encodeproject.org/files/ENCFF971CUZ/@@download/ENCFF971CUZ.bed.gz,K562_109
ENCFF049AFY,416228,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR217QAB,https://www.encodeproject.org/files/ENCFF049AFY/@@download/ENCFF049AFY.bed.gz,K562_110
ENCFF345KVP,77948,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR217QAB,https://www.encodeproject.org/files/ENCFF345KVP/@@download/ENCFF345KVP.bed.gz,K562_111
ENCFF608OMF,1011610,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR243BXK,https://www.encodeproject.org/files/ENCFF608OMF/@@download/ENCFF608OMF.bed.gz,K562_112
ENCFF419MBX,600771,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR243BXK,https://www.encodeproject.org/files/ENCFF419MBX/@@download/ENCFF419MBX.bed.gz,K562_113
ENCFF662YKX,536432,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR243BXK,https://www.encodeproject.org/files/ENCFF662YKX/@@download/ENCFF662YKX.bed.gz,K562_114
ENCFF909LRH,839191,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR303NUP,https://www.encodeproject.org/files/ENCFF909LRH/@@download/ENCFF909LRH.bed.gz,K562_115
ENCFF584SNE,717242,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR303NUP,https://www.encodeproject.org/files/ENCFF584SNE/@@download/ENCFF584SNE.bed.gz,K562_116
ENCFF517BUV,423828,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR303NUP,https://www.encodeproject.org/files/ENCFF517BUV/@@download/ENCFF517BUV.bed.gz,K562_117
ENCFF038YZQ,499737,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR304UWR,https://www.encodeproject.org/files/ENCFF038YZQ/@@download/ENCFF038YZQ.bed.gz,K562_118
ENCFF021RWF,446705,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR304UWR,https://www.encodeproject.org/files/ENCFF021RWF/@@download/ENCFF021RWF.bed.gz,K562_119
ENCFF962XBP,395635,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR304UWR,https://www.encodeproject.org/files/ENCFF962XBP/@@download/ENCFF962XBP.bed.gz,K562_120
ENCFF037LIG,1052268,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR343TQX,https://www.encodeproject.org/files/ENCFF037LIG/@@download/ENCFF037LIG.bed.gz,K562_121
ENCFF869AYX,691681,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR343TQX,https://www.encodeproject.org/files/ENCFF869AYX/@@download/ENCFF869AYX.bed.gz,K562_122
ENCFF956DHP,524970,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR343TQX,https://www.encodeproject.org/files/ENCFF956DHP/@@download/ENCFF956DHP.bed.gz,K562_123
ENCFF571LUM,1030583,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR442DWQ,https://www.encodeproject.org/files/ENCFF571LUM/@@download/ENCFF571LUM.bed.gz,K562_124
ENCFF617EIR,690737,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR442DWQ,https://www.encodeproject.org/files/ENCFF617EIR/@@download/ENCFF617EIR.bed.gz,K562_125
ENCFF529MUU,627148,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR442DWQ,https://www.encodeproject.org/files/ENCFF529MUU/@@download/ENCFF529MUU.bed.gz,K562_126
ENCFF965QMG,515241,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR503EZG,https://www.encodeproject.org/files/ENCFF965QMG/@@download/ENCFF965QMG.bed.gz,K562_127
ENCFF795JNN,168889,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR503EZG,https://www.encodeproject.org/files/ENCFF795JNN/@@download/ENCFF795JNN.bed.gz,K562_128
ENCFF899VBX,317428,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR503EZG,https://www.encodeproject.org/files/ENCFF899VBX/@@download/ENCFF899VBX.bed.gz,K562_129
ENCFF367NPM,451819,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR676UFY,https://www.encodeproject.org/files/ENCFF367NPM/@@download/ENCFF367NPM.bed.gz,K562_130
ENCFF263PTR,292748,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR676UFY,https://www.encodeproject.org/files/ENCFF263PTR/@@download/ENCFF263PTR.bed.gz,K562_131
ENCFF966AZB,330937,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR676UFY,https://www.encodeproject.org/files/ENCFF966AZB/@@download/ENCFF966AZB.bed.gz,K562_132
ENCFF824DOL,931226,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR680XOP,https://www.encodeproject.org/files/ENCFF824DOL/@@download/ENCFF824DOL.bed.gz,K562_133
ENCFF552BXC,498829,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR680XOP,https://www.encodeproject.org/files/ENCFF552BXC/@@download/ENCFF552BXC.bed.gz,K562_134
ENCFF049RMX,356027,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR680XOP,https://www.encodeproject.org/files/ENCFF049RMX/@@download/ENCFF049RMX.bed.gz,K562_135
ENCFF555BSM,797300,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR741QNS,https://www.encodeproject.org/files/ENCFF555BSM/@@download/ENCFF555BSM.bed.gz,K562_136
ENCFF528FYX,631865,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR741QNS,https://www.encodeproject.org/files/ENCFF528FYX/@@download/ENCFF528FYX.bed.gz,K562_137
ENCFF559IYS,555836,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR741QNS,https://www.encodeproject.org/files/ENCFF559IYS/@@download/ENCFF559IYS.bed.gz,K562_138
ENCFF904VAX,992783,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR761CBY,https://www.encodeproject.org/files/ENCFF904VAX/@@download/ENCFF904VAX.bed.gz,K562_139
ENCFF295HMK,801177,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR761CBY,https://www.encodeproject.org/files/ENCFF295HMK/@@download/ENCFF295HMK.bed.gz,K562_140
ENCFF645AYO,436013,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR761CBY,https://www.encodeproject.org/files/ENCFF645AYO/@@download/ENCFF645AYO.bed.gz,K562_141
ENCFF951JBG,974466,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR803QIE,https://www.encodeproject.org/files/ENCFF951JBG/@@download/ENCFF951JBG.bed.gz,K562_142
ENCFF735DXR,606722,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR803QIE,https://www.encodeproject.org/files/ENCFF735DXR/@@download/ENCFF735DXR.bed.gz,K562_143
ENCFF793YNX,630433,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR803QIE,https://www.encodeproject.org/files/ENCFF793YNX/@@download/ENCFF793YNX.bed.gz,K562_144
ENCFF608MSI,591651,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR874GAJ,https://www.encodeproject.org/files/ENCFF608MSI/@@download/ENCFF608MSI.bed.gz,K562_145
ENCFF130GPN,469697,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR874GAJ,https://www.encodeproject.org/files/ENCFF130GPN/@@download/ENCFF130GPN.bed.gz,K562_146
ENCFF570PUT,52880,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR874GAJ,https://www.encodeproject.org/files/ENCFF570PUT/@@download/ENCFF570PUT.bed.gz,K562_147
ENCFF880GBA,872932,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR888JRM,https://www.encodeproject.org/files/ENCFF880GBA/@@download/ENCFF880GBA.bed.gz,K562_148
ENCFF199ZML,433605,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR888JRM,https://www.encodeproject.org/files/ENCFF199ZML/@@download/ENCFF199ZML.bed.gz,K562_149
ENCFF124ZSS,362689,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR888JRM,https://www.encodeproject.org/files/ENCFF124ZSS/@@download/ENCFF124ZSS.bed.gz,K562_150
ENCFF601UAA,1019208,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR920YDG,https://www.encodeproject.org/files/ENCFF601UAA/@@download/ENCFF601UAA.bed.gz,K562_151
ENCFF970EDS,559081,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR920YDG,https://www.encodeproject.org/files/ENCFF970EDS/@@download/ENCFF970EDS.bed.gz,K562_152
ENCFF357LAT,463680,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR920YDG,https://www.encodeproject.org/files/ENCFF357LAT/@@download/ENCFF357LAT.bed.gz,K562_153
ENCFF587UKV,856845,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR962TDN,https://www.encodeproject.org/files/ENCFF587UKV/@@download/ENCFF587UKV.bed.gz,K562_154
ENCFF491EMI,469032,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR962TDN,https://www.encodeproject.org/files/ENCFF491EMI/@@download/ENCFF491EMI.bed.gz,K562_155
ENCFF002NSV,386063,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR962TDN,https://www.encodeproject.org/files/ENCFF002NSV/@@download/ENCFF002NSV.bed.gz,K562_156
ENCFF668KZC,576152,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR978HFU,https://www.encodeproject.org/files/ENCFF668KZC/@@download/ENCFF668KZC.bed.gz,K562_157
ENCFF361ZBI,387440,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR978HFU,https://www.encodeproject.org/files/ENCFF361ZBI/@@download/ENCFF361ZBI.bed.gz,K562_158
ENCFF299JUT,120954,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR978HFU,https://www.encodeproject.org/files/ENCFF299JUT/@@download/ENCFF299JUT.bed.gz,K562_159
ENCFF433QEA,1287148,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR005BVE,https://www.encodeproject.org/files/ENCFF433QEA/@@download/ENCFF433QEA.bed.gz,K562_160
ENCFF679HEZ,863785,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR005BVE,https://www.encodeproject.org/files/ENCFF679HEZ/@@download/ENCFF679HEZ.bed.gz,K562_161
ENCFF204BTQ,894901,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR005BVE,https://www.encodeproject.org/files/ENCFF204BTQ/@@download/ENCFF204BTQ.bed.gz,K562_162
ENCFF685GEN,1578060,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR017LGQ,https://www.encodeproject.org/files/ENCFF685GEN/@@download/ENCFF685GEN.bed.gz,K562_163
ENCFF770AOH,1003876,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR017LGQ,https://www.encodeproject.org/files/ENCFF770AOH/@@download/ENCFF770AOH.bed.gz,K562_164
ENCFF417IPO,1096330,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR017LGQ,https://www.encodeproject.org/files/ENCFF417IPO/@@download/ENCFF417IPO.bed.gz,K562_165
ENCFF254GBM,645809,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR021GTX,https://www.encodeproject.org/files/ENCFF254GBM/@@download/ENCFF254GBM.bed.gz,K562_166
ENCFF263VLT,370899,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR021GTX,https://www.encodeproject.org/files/ENCFF263VLT/@@download/ENCFF263VLT.bed.gz,K562_167
ENCFF151UCX,364743,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR021GTX,https://www.encodeproject.org/files/ENCFF151UCX/@@download/ENCFF151UCX.bed.gz,K562_168
ENCFF380BWK,1385950,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR068MIW,https://www.encodeproject.org/files/ENCFF380BWK/@@download/ENCFF380BWK.bed.gz,K562_169
ENCFF054KVO,950389,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR068MIW,https://www.encodeproject.org/files/ENCFF054KVO/@@download/ENCFF054KVO.bed.gz,K562_170
ENCFF431DUU,1038613,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR068MIW,https://www.encodeproject.org/files/ENCFF431DUU/@@download/ENCFF431DUU.bed.gz,K562_171
ENCFF437QQL,400432,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR180IJO,https://www.encodeproject.org/files/ENCFF437QQL/@@download/ENCFF437QQL.bed.gz,K562_172
ENCFF978DIW,162521,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR180IJO,https://www.encodeproject.org/files/ENCFF978DIW/@@download/ENCFF978DIW.bed.gz,K562_173
ENCFF262YDG,97083,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR180IJO,https://www.encodeproject.org/files/ENCFF262YDG/@@download/ENCFF262YDG.bed.gz,K562_174
ENCFF432FLV,699728,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR214SFA,https://www.encodeproject.org/files/ENCFF432FLV/@@download/ENCFF432FLV.bed.gz,K562_175
ENCFF132LNI,303070,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR214SFA,https://www.encodeproject.org/files/ENCFF132LNI/@@download/ENCFF132LNI.bed.gz,K562_176
ENCFF017LVC,442892,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR214SFA,https://www.encodeproject.org/files/ENCFF017LVC/@@download/ENCFF017LVC.bed.gz,K562_177
ENCFF237ZRG,843255,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR259HTB,https://www.encodeproject.org/files/ENCFF237ZRG/@@download/ENCFF237ZRG.bed.gz,K562_178
ENCFF165PFK,539243,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR259HTB,https://www.encodeproject.org/files/ENCFF165PFK/@@download/ENCFF165PFK.bed.gz,K562_179
ENCFF901SEI,291545,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR259HTB,https://www.encodeproject.org/files/ENCFF901SEI/@@download/ENCFF901SEI.bed.gz,K562_180
ENCFF139UQN,627584,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR296YAS,https://www.encodeproject.org/files/ENCFF139UQN/@@download/ENCFF139UQN.bed.gz,K562_181
ENCFF196WWN,348781,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR296YAS,https://www.encodeproject.org/files/ENCFF196WWN/@@download/ENCFF196WWN.bed.gz,K562_182
ENCFF920UWC,305728,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR296YAS,https://www.encodeproject.org/files/ENCFF920UWC/@@download/ENCFF920UWC.bed.gz,K562_183
ENCFF891NEM,1091253,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR366CEQ,https://www.encodeproject.org/files/ENCFF891NEM/@@download/ENCFF891NEM.bed.gz,K562_184
ENCFF606QCJ,667123,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR366CEQ,https://www.encodeproject.org/files/ENCFF606QCJ/@@download/ENCFF606QCJ.bed.gz,K562_185
ENCFF729JJM,597914,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR366CEQ,https://www.encodeproject.org/files/ENCFF729JJM/@@download/ENCFF729JJM.bed.gz,K562_186
ENCFF428ESD,1030365,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR737HKX,https://www.encodeproject.org/files/ENCFF428ESD/@@download/ENCFF428ESD.bed.gz,K562_187
ENCFF127JMM,442905,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR737HKX,https://www.encodeproject.org/files/ENCFF127JMM/@@download/ENCFF127JMM.bed.gz,K562_188
ENCFF888IBW,527177,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR737HKX,https://www.encodeproject.org/files/ENCFF888IBW/@@download/ENCFF888IBW.bed.gz,K562_189
ENCFF373OJM,1069536,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR822IYG,https://www.encodeproject.org/files/ENCFF373OJM/@@download/ENCFF373OJM.bed.gz,K562_190
ENCFF683IHS,678253,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR822IYG,https://www.encodeproject.org/files/ENCFF683IHS/@@download/ENCFF683IHS.bed.gz,K562_191
ENCFF157JZX,707090,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR822IYG,https://www.encodeproject.org/files/ENCFF157JZX/@@download/ENCFF157JZX.bed.gz,K562_192
ENCFF648HQB,1365833,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR966ZCL,https://www.encodeproject.org/files/ENCFF648HQB/@@download/ENCFF648HQB.bed.gz,K562_193
ENCFF859LGM,991339,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR966ZCL,https://www.encodeproject.org/files/ENCFF859LGM/@@download/ENCFF859LGM.bed.gz,K562_194
ENCFF507AXY,910338,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR966ZCL,https://www.encodeproject.org/files/ENCFF507AXY/@@download/ENCFF507AXY.bed.gz,K562_195
ENCFF817SLK,2159872,GRCh38,WTC11,cell line,"Yin Shen, UCSF",ENCSR089KIJ,https://www.encodeproject.org/files/ENCFF817SLK/@@download/ENCFF817SLK.bed.gz,WTC11_4
ENCFF400NWC,1162733,GRCh38,WTC11,cell line,"Yin Shen, UCSF",ENCSR089KIJ,https://www.encodeproject.org/files/ENCFF400NWC/@@download/ENCFF400NWC.bed.gz,WTC11_5
ENCFF203DPW,1560665,GRCh38,WTC11,cell line,"Yin Shen, UCSF",ENCSR089KIJ,https://www.encodeproject.org/files/ENCFF203DPW/@@download/ENCFF203DPW.bed.gz,WTC11_6
ENCFF820DKN,668358,GRCh38,right cardiac atrium,tissue,"Michael Snyder, Stanford",ENCSR452OSK,https://www.encodeproject.org/files/ENCFF820DKN/@@download/ENCFF820DKN.bed.gz,right_cardiac_atrium_4
