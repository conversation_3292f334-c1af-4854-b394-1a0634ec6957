#!/usr/bin/env python3
"""
Complete pipeline to download and process all ENCODE ATAC-seq IDR files.
"""

import requests
import pandas as pd
import os
import subprocess
import time
import argparse
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
import gzip
import shutil


def get_all_encode_atac_files(limit=None):
    """
    Get all human ENCODE ATAC-seq IDR files.
    """
    print("Querying ENCODE for all human ATAC-seq IDR files...")
    
    url = "https://www.encodeproject.org/search/"
    params = {
        "type": "File",
        "file_format": "bed",
        "output_type": "IDR thresholded peaks",
        "status": "released",
        "assembly": "GRCh38",
        "assay_title": "ATAC-seq",
        "limit": limit or 1000,
        "format": "json"
    }
    
    try:
        response = requests.get(url, params=params, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            files = data.get("@graph", [])
            total = data.get("total", len(files))
            print(f"Found {len(files)} files (total available: {total})")
            
            return files, total
        else:
            print(f"Error: HTTP {response.status_code}")
            return [], 0
            
    except Exception as e:
        print(f"Error: {e}")
        return [], 0


def get_detailed_encode_info(files):
    """
    Get detailed information for ENCODE files.
    """
    print("Getting detailed file information...")
    
    detailed_files = []
    
    for i, file_info in enumerate(files):
        if i % 25 == 0:
            print(f"  Processing file {i+1}/{len(files)}...")
        
        try:
            accession = file_info.get("accession", "")
            
            # Get detailed info
            file_url = f"https://www.encodeproject.org/files/{accession}/?format=json"
            response = requests.get(file_url, timeout=30)
            
            if response.status_code == 200:
                detailed_info = response.json()
                
                file_data = {
                    "accession": accession,
                    "file_size": detailed_info.get("file_size", 0),
                    "assembly": detailed_info.get("assembly", ""),
                    "biosample_term_name": "",
                    "biosample_type": "",
                    "lab": "",
                    "dataset_accession": "",
                    "download_url": f"https://www.encodeproject.org/files/{accession}/@@download/{accession}.bed.gz"
                }
                
                # Get dataset information
                dataset_path = detailed_info.get("dataset", "")
                if dataset_path and isinstance(dataset_path, str):
                    dataset_accession = dataset_path.split("/")[-2] if "/" in dataset_path else dataset_path
                    file_data["dataset_accession"] = dataset_accession
                    
                    # Get dataset details
                    dataset_url = f"https://www.encodeproject.org{dataset_path}?format=json"
                    dataset_response = requests.get(dataset_url, timeout=30)
                    
                    if dataset_response.status_code == 200:
                        dataset_info = dataset_response.json()
                        
                        # Lab information
                        lab = dataset_info.get("lab", {})
                        if isinstance(lab, dict):
                            file_data["lab"] = lab.get("title", "")
                        
                        # Biosample information
                        biosample_ontology = dataset_info.get("biosample_ontology", {})
                        file_data["biosample_term_name"] = biosample_ontology.get("term_name", "")
                        file_data["biosample_type"] = biosample_ontology.get("classification", "")
                
                detailed_files.append(file_data)
                
        except Exception as e:
            print(f"    Error processing {accession}: {e}")
            continue
    
    return detailed_files


def create_encode_labels(detailed_files):
    """
    Create TCGA-style labels for ENCODE files.
    """
    print("Creating ENCODE labels...")
    
    # Group by biosample type
    biosample_counts = {}
    labeled_files = []
    
    for file_info in detailed_files:
        biosample = file_info["biosample_term_name"]
        
        # Clean up biosample name for labeling
        if biosample:
            # Simplify long names
            label_name = biosample.replace(" ", "_").replace(",", "").replace("-", "_")
            label_name = label_name.replace("positive", "pos").replace("negative", "neg")
            label_name = label_name.replace("alpha_beta", "ab")
            
            # Limit length
            if len(label_name) > 25:
                # Use abbreviations for common terms
                label_name = label_name.replace("stimulated_activated", "stim_act")
                label_name = label_name.replace("naive", "nv")
                label_name = label_name.replace("thymus_derived", "thy")
                label_name = label_name.replace("cell", "")
                
                # Take first 25 characters if still too long
                if len(label_name) > 25:
                    label_name = label_name[:25]
        else:
            label_name = "UNKNOWN"
        
        # Count occurrences
        if label_name not in biosample_counts:
            biosample_counts[label_name] = 0
        biosample_counts[label_name] += 1
        
        # Create numbered label
        numbered_label = f"{label_name}_{biosample_counts[label_name]}"
        
        file_info["encode_label"] = numbered_label
        labeled_files.append(file_info)
    
    return labeled_files


def download_encode_files(labeled_files, download_dir="encode_downloads"):
    """
    Download ENCODE files.
    """
    print(f"Downloading {len(labeled_files)} ENCODE files...")
    
    os.makedirs(download_dir, exist_ok=True)
    
    downloaded_files = []
    
    for i, file_info in enumerate(labeled_files):
        print(f"  Downloading {i+1}/{len(labeled_files)}: {file_info['accession']} ({file_info['encode_label']})")
        
        try:
            # Download file
            response = requests.get(file_info['download_url'], timeout=300)
            
            if response.status_code == 200:
                # Save compressed file
                gz_filename = os.path.join(download_dir, f"{file_info['accession']}.bed.gz")
                with open(gz_filename, 'wb') as f:
                    f.write(response.content)
                
                # Decompress
                bed_filename = os.path.join(download_dir, f"{file_info['accession']}.bed")
                with gzip.open(gz_filename, 'rb') as f_in:
                    with open(bed_filename, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                # Remove compressed file to save space
                os.remove(gz_filename)
                
                file_info['local_path'] = bed_filename
                downloaded_files.append(file_info)
                
            else:
                print(f"    Failed to download {file_info['accession']}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    Error downloading {file_info['accession']}: {e}")
            continue
    
    print(f"Successfully downloaded {len(downloaded_files)} files")
    return downloaded_files


def process_encode_file(file_info, window_size=10000):
    """
    Process a single ENCODE file to create genomic windows.
    """
    try:
        accession = file_info['accession']
        encode_label = file_info['encode_label']
        file_path = file_info['local_path']
        
        print(f"  Processing {encode_label} ({accession})...")
        
        # Create temporary output file
        temp_output = f"temp_{accession}_windows.tsv"
        
        # Run processing script (reuse the one we created earlier)
        cmd = [
            'python', 'process_narrowpeak_memory_efficient.py',
            '--main-chr-only',
            '--window-size', str(window_size),
            '--pattern', file_path,
            '--output', temp_output
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            # Load the results
            windows_df = pd.read_csv(temp_output, sep='\t')
            
            # Clean up temp file
            os.remove(temp_output)
            
            # Keep only the columns we need and rename CA_signal
            windows_df = windows_df[['chr', 'start', 'end', 'CA_signal']].copy()
            windows_df = windows_df.rename(columns={'CA_signal': encode_label})
            
            return True, encode_label, windows_df
            
        else:
            return False, encode_label, f"Processing error: {result.stderr}"
            
    except Exception as e:
        return False, file_info.get('encode_label', 'unknown'), f"Exception: {str(e)}"


def create_genomic_windows_template(window_size=10000):
    """
    Create genomic windows template.
    """
    print("Creating genomic windows template...")
    
    chr_lengths = {
        'chr1': 248956422, 'chr2': 242193529, 'chr3': 198295559, 'chr4': 190214555,
        'chr5': 181538259, 'chr6': 170805979, 'chr7': 159345973, 'chr8': 145138636,
        'chr9': 138394717, 'chr10': 133797422, 'chr11': 135086622, 'chr12': 133275309,
        'chr13': 114364328, 'chr14': 107043718, 'chr15': 101991189, 'chr16': 90338345,
        'chr17': 83257441, 'chr18': 80373285, 'chr19': 58617616, 'chr20': 64444167,
        'chr21': 46709983, 'chr22': 50818468, 'chrX': 156040895, 'chrY': 57227415
    }
    
    windows_list = []
    
    for chr_name, max_pos in chr_lengths.items():
        starts = list(range(1, max_pos + 1, window_size))
        
        for start in starts:
            end = min(start + window_size - 1, max_pos)
            windows_list.append({
                'chr': chr_name,
                'start': start,
                'end': end
            })
    
    template_df = pd.DataFrame(windows_list)
    print(f"Created template with {len(template_df)} windows")
    
    return template_df


def combine_encode_files(downloaded_files, window_size=10000, max_workers=2):
    """
    Combine all ENCODE files into a single matrix.
    """
    print(f"Combining {len(downloaded_files)} ENCODE files into matrix...")
    
    # Create genomic windows template
    template_df = create_genomic_windows_template(window_size)
    template_df['window_id'] = template_df['chr'] + ':' + template_df['start'].astype(str) + '-' + template_df['end'].astype(str)
    
    # Initialize combined matrix
    combined_matrix = template_df[['chr', 'start', 'end', 'window_id']].copy()
    
    successful_samples = []
    failed_samples = []
    
    # Process files in batches
    batch_size = 20
    total_batches = (len(downloaded_files) + batch_size - 1) // batch_size
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, len(downloaded_files))
        batch_files = downloaded_files[start_idx:end_idx]
        
        print(f"\nProcessing batch {batch_num + 1}/{total_batches} ({len(batch_files)} files)...")
        
        # Process batch in parallel
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(process_encode_file, file_info, window_size): file_info['encode_label']
                for file_info in batch_files
            }
            
            for future in as_completed(future_to_file):
                sample_name = future_to_file[future]
                
                try:
                    success, returned_name, result = future.result()
                    
                    if success:
                        windows_df = result
                        
                        # Create window_id for merging
                        windows_df['window_id'] = windows_df['chr'] + ':' + windows_df['start'].astype(str) + '-' + windows_df['end'].astype(str)
                        
                        # Merge with combined matrix
                        combined_matrix = pd.merge(
                            combined_matrix, 
                            windows_df[['window_id', returned_name]], 
                            on='window_id', 
                            how='left'
                        )
                        
                        # Fill NaN values with 0
                        combined_matrix[returned_name] = combined_matrix[returned_name].fillna(0)
                        
                        successful_samples.append(returned_name)
                        print(f"    ✓ {returned_name}")
                        
                    else:
                        failed_samples.append((returned_name, result))
                        print(f"    ✗ {returned_name}: {result}")
                        
                except Exception as e:
                    failed_samples.append((sample_name, f"Future exception: {e}"))
                    print(f"    ✗ {sample_name}: Future exception: {e}")
    
    # Remove window_id column
    combined_matrix = combined_matrix.drop('window_id', axis=1)
    
    return combined_matrix, successful_samples, failed_samples


def main():
    parser = argparse.ArgumentParser(description='Download and process all ENCODE ATAC-seq files')
    parser.add_argument('--limit', type=int, help='Limit number of files to download (for testing)')
    parser.add_argument('--window-size', type=int, default=10000, help='Window size in bp (default: 10000)')
    parser.add_argument('--max-workers', type=int, default=2, help='Max parallel workers (default: 2)')
    parser.add_argument('--skip-download', action='store_true', help='Skip download (use existing files)')
    
    args = parser.parse_args()
    
    start_time = time.time()
    
    try:
        if not args.skip_download:
            # Step 1: Get all ENCODE files
            files, total = get_all_encode_atac_files(args.limit)
            
            if not files:
                print("No files found")
                return
            
            # Step 2: Get detailed information
            detailed_files = get_detailed_encode_info(files)
            
            # Step 3: Create labels
            labeled_files = create_encode_labels(detailed_files)
            
            # Save file mapping
            mapping_df = pd.DataFrame(labeled_files)
            mapping_df.to_csv('encode_file_mapping.csv', index=False)
            print(f"Saved file mapping to: encode_file_mapping.csv")
            
            # Step 4: Download files
            downloaded_files = download_encode_files(labeled_files)
            
        else:
            # Load existing mapping
            mapping_df = pd.read_csv('encode_file_mapping.csv')
            downloaded_files = mapping_df.to_dict('records')
            print(f"Loaded {len(downloaded_files)} files from mapping")
        
        if not downloaded_files:
            print("No files to process")
            return
        
        # Step 5: Combine into matrix
        combined_matrix, successful, failed = combine_encode_files(
            downloaded_files, args.window_size, args.max_workers
        )
        
        # Step 6: Save results
        if args.window_size >= 1000:
            size_label = f"{args.window_size // 1000}kb"
        else:
            size_label = f"{args.window_size}bp"
        
        output_file = f"ENCODE_all_samples_combined_{size_label}.tsv"
        print(f"\nSaving combined matrix to {output_file}...")
        combined_matrix.to_csv(output_file, sep='\t', index=False)
        
        # Summary
        elapsed_time = time.time() - start_time
        print(f"\n{'='*60}")
        print(f"ENCODE Pipeline Completed!")
        print(f"Processing time: {elapsed_time:.1f} seconds")
        print(f"Successful samples: {len(successful)}")
        print(f"Failed samples: {len(failed)}")
        print(f"Total windows: {len(combined_matrix)}")
        print(f"Total columns: {len(combined_matrix.columns)} (3 coordinate + {len(successful)} samples)")
        print(f"Output file: {output_file}")
        
        if successful:
            print(f"\nSample column names (first 10):")
            for i, col in enumerate([c for c in combined_matrix.columns if c not in ['chr', 'start', 'end']][:10]):
                print(f"  {col}")
        
    except Exception as e:
        print(f"Pipeline error: {e}")


if __name__ == "__main__":
    main()
