import pandas as pd

# 1. Read the probeMap (the file with columns: id, gene, chrom, chromStart, chromEnd, strand)
probe_map = pd.read_csv("probeMap.txt", sep="\t")
# Rename columns if you like consistency:
probe_map = probe_map.rename(columns={
    "chromStart": "start",
    "chromEnd": "end"
})

# Build a dictionary from peakID -> (chrom, start, end).
peak_to_coords = {}
for i, row in probe_map.iterrows():
    peak_id = row["id"]  # e.g. "LIHC_2"
    chrom = row["chrom"]
    start = row["start"]
    end   = row["end"]
    peak_to_coords[peak_id] = (chrom, start, end)

# 2. Read the big data table (rows = peakIDs like "LIHC_2", columns = sample IDs).
#    Make sure the first column is the peak 'id' and that you set index_col=0.
data_matrix = pd.read_csv("atac_values.tsv", sep="\t", index_col=0)  
# data_matrix.index might look like ["PCPG_2", "LIHC_2", "LIHC_3", "TGCT_2", …]
# data_matrix.columns are your sample IDs (TCGA-XX-XXXX-...).

# 3. Melt/combine into long format
rows_out = []
for peak_id, row in data_matrix.iterrows():
    # Get the coords from the dictionary
    if peak_id not in peak_to_coords:
        # Possibly skip or handle missing
        continue
    chrom, start, end = peak_to_coords[peak_id]

    # Optionally parse the cancer type from the peak ID (e.g. "LIHC" from "LIHC_2")
    # This works if the prefix up to the underscore is indeed the tumor code.
    # If not needed, you can skip this.
    cancer_type = peak_id.split("_")[0]  # e.g. "LIHC"

    # For each sample, we have one numeric value
    for sample_id, value in row.items():
        rows_out.append({
            "chr": chrom,
            "start": start,
            "end": end,
            "sample": sample_id,
            "value": value,
            "cancerType": cancer_type
        })

# Convert to a dataframe
final_df = pd.DataFrame(rows_out)

# 4. Write out as a TSV/CSV
final_df.to_csv("combined_output.tsv", sep="\t", index=False)
