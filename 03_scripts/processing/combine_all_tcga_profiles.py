#!/usr/bin/env python3
"""
Combine all TCGA ATAC-seq profiles into a single 10kb window matrix.
Each column will be named like BRCA_1, BRCA_2, LUSC_1, etc.
"""

import pandas as pd
import numpy as np
import os
import subprocess
import argparse
from concurrent.futures import ProcessPoolExecutor, as_completed
import time


def process_single_sample(sample_info, window_size=10000):
    """
    Process a single TCGA sample to create genomic windows.
    
    Args:
        sample_info: Dictionary with sample information
        window_size: Window size in bp
    
    Returns:
        Tuple of (success, sample_name, windows_df or error_message)
    """
    try:
        barcode = sample_info['barcode']
        cancer_type = sample_info['cancer_type']
        numbered_name = sample_info['cancer_type_numbered']
        file_path = sample_info['file_path']
        
        print(f"  Processing {numbered_name} ({barcode})...")
        
        # Create temporary output file
        temp_output = f"temp_{numbered_name}_windows.tsv"
        
        # Run processing script
        cmd = [
            'python', 'process_narrowpeak_memory_efficient.py',
            '--main-chr-only',
            '--window-size', str(window_size),
            '--pattern', file_path,
            '--output', temp_output
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            # Load the results
            windows_df = pd.read_csv(temp_output, sep='\t')
            
            # Clean up temp file
            os.remove(temp_output)
            
            # Keep only the columns we need and rename CA_signal
            windows_df = windows_df[['chr', 'start', 'end', 'CA_signal']].copy()
            windows_df = windows_df.rename(columns={'CA_signal': numbered_name})
            
            return True, numbered_name, windows_df
            
        else:
            return False, numbered_name, f"Processing error: {result.stderr}"
            
    except Exception as e:
        return False, sample_info.get('cancer_type_numbered', 'unknown'), f"Exception: {str(e)}"


def create_genomic_windows_template(window_size=10000):
    """
    Create a template of genomic windows for main chromosomes.
    This ensures all samples have the same window coordinates.
    """
    print("Creating genomic windows template...")
    
    # Define main chromosomes and their approximate lengths
    chr_lengths = {
        'chr1': 248956422, 'chr2': 242193529, 'chr3': 198295559, 'chr4': 190214555,
        'chr5': 181538259, 'chr6': 170805979, 'chr7': 159345973, 'chr8': 145138636,
        'chr9': 138394717, 'chr10': 133797422, 'chr11': 135086622, 'chr12': 133275309,
        'chr13': 114364328, 'chr14': 107043718, 'chr15': 101991189, 'chr16': 90338345,
        'chr17': 83257441, 'chr18': 80373285, 'chr19': 58617616, 'chr20': 64444167,
        'chr21': 46709983, 'chr22': 50818468, 'chrX': 156040895, 'chrY': 57227415
    }
    
    windows_list = []
    
    for chr_name, max_pos in chr_lengths.items():
        # Create windows from 1 to max_pos with step = window_size
        starts = list(range(1, max_pos + 1, window_size))
        
        for start in starts:
            end = min(start + window_size - 1, max_pos)
            windows_list.append({
                'chr': chr_name,
                'start': start,
                'end': end
            })
    
    template_df = pd.DataFrame(windows_list)
    print(f"Created template with {len(template_df)} windows")
    
    return template_df


def combine_all_tcga_profiles(mapping_file, window_size=10000, max_workers=4, output_file=None):
    """
    Combine all TCGA profiles into a single matrix.
    """
    # Load mapping
    mapping_df = pd.read_csv(mapping_file, sep='\t')
    
    print(f"Found {len(mapping_df)} TCGA samples to process")
    
    # Create genomic windows template
    template_df = create_genomic_windows_template(window_size)
    
    # Create window identifier for merging
    template_df['window_id'] = template_df['chr'] + ':' + template_df['start'].astype(str) + '-' + template_df['end'].astype(str)
    
    # Initialize the combined matrix with the template
    combined_matrix = template_df[['chr', 'start', 'end', 'window_id']].copy()
    
    print(f"\nProcessing samples with {max_workers} parallel workers...")
    
    successful_samples = []
    failed_samples = []
    
    # Process samples in batches to avoid memory issues
    batch_size = 50
    total_batches = (len(mapping_df) + batch_size - 1) // batch_size
    
    for batch_num in range(total_batches):
        start_idx = batch_num * batch_size
        end_idx = min((batch_num + 1) * batch_size, len(mapping_df))
        batch_samples = mapping_df.iloc[start_idx:end_idx]
        
        print(f"\nProcessing batch {batch_num + 1}/{total_batches} ({len(batch_samples)} samples)...")
        
        # Process batch in parallel
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit jobs for this batch
            future_to_sample = {
                executor.submit(process_single_sample, row.to_dict(), window_size): row['cancer_type_numbered']
                for _, row in batch_samples.iterrows()
            }
            
            # Collect results
            for future in as_completed(future_to_sample):
                sample_name = future_to_sample[future]
                
                try:
                    success, returned_name, result = future.result()
                    
                    if success:
                        windows_df = result
                        
                        # Create window_id for merging
                        windows_df['window_id'] = windows_df['chr'] + ':' + windows_df['start'].astype(str) + '-' + windows_df['end'].astype(str)
                        
                        # Merge with combined matrix
                        combined_matrix = pd.merge(
                            combined_matrix, 
                            windows_df[['window_id', returned_name]], 
                            on='window_id', 
                            how='left'
                        )
                        
                        # Fill NaN values with 0 (windows with no signal)
                        combined_matrix[returned_name] = combined_matrix[returned_name].fillna(0)
                        
                        successful_samples.append(returned_name)
                        print(f"    ✓ {returned_name}")
                        
                    else:
                        failed_samples.append((returned_name, result))
                        print(f"    ✗ {returned_name}: {result}")
                        
                except Exception as e:
                    failed_samples.append((sample_name, f"Future exception: {e}"))
                    print(f"    ✗ {sample_name}: Future exception: {e}")
    
    # Remove the temporary window_id column
    combined_matrix = combined_matrix.drop('window_id', axis=1)
    
    # Generate output filename if not provided
    if output_file is None:
        if window_size >= 1000:
            size_label = f"{window_size // 1000}kb"
        else:
            size_label = f"{window_size}bp"
        output_file = f"TCGA_all_samples_combined_{size_label}.tsv"
    
    # Save the combined matrix
    print(f"\nSaving combined matrix to {output_file}...")
    combined_matrix.to_csv(output_file, sep='\t', index=False)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"TCGA Profile Combination Completed!")
    print(f"Successful samples: {len(successful_samples)}")
    print(f"Failed samples: {len(failed_samples)}")
    print(f"Total windows: {len(combined_matrix)}")
    print(f"Total columns: {len(combined_matrix.columns)} (3 coordinate + {len(successful_samples)} samples)")
    print(f"Output file: {output_file}")
    
    if failed_samples:
        print(f"\nFailed samples:")
        for sample, error in failed_samples[:10]:  # Show first 10 failures
            print(f"  {sample}: {error}")
        if len(failed_samples) > 10:
            print(f"  ... and {len(failed_samples) - 10} more")
    
    # Show sample of the matrix
    print(f"\nSample of combined matrix:")
    print(combined_matrix.head())
    
    # Show column names
    sample_columns = [col for col in combined_matrix.columns if col not in ['chr', 'start', 'end']]
    print(f"\nSample column names:")
    for i, col in enumerate(sample_columns[:20]):
        print(f"  {col}")
    if len(sample_columns) > 20:
        print(f"  ... and {len(sample_columns) - 20} more")
    
    return output_file, successful_samples, failed_samples


def main():
    parser = argparse.ArgumentParser(description='Combine all TCGA ATAC-seq profiles into a single matrix')
    parser.add_argument('--mapping-file', default='tcga_cancer_type_mapping.tsv',
                       help='Sample mapping file (default: tcga_cancer_type_mapping.tsv)')
    parser.add_argument('--window-size', type=int, default=10000,
                       help='Window size in base pairs (default: 10000)')
    parser.add_argument('--output', help='Output file name (auto-generated if not specified)')
    parser.add_argument('--max-workers', type=int, default=4,
                       help='Maximum number of parallel processes (default: 4)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.mapping_file):
        print(f"Error: Mapping file {args.mapping_file} not found")
        print("Run: python analyze_tcga_cancer_types.py first")
        return
    
    start_time = time.time()
    
    try:
        output_file, successful, failed = combine_all_tcga_profiles(
            args.mapping_file, 
            args.window_size, 
            args.max_workers,
            args.output
        )
        
        elapsed_time = time.time() - start_time
        print(f"\nTotal processing time: {elapsed_time:.1f} seconds")
        
    except Exception as e:
        print(f"Error during processing: {e}")


if __name__ == "__main__":
    main()
